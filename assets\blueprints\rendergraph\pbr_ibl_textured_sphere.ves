var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "depth_test" : true, "depth_func" : "less", "cull" : "back" }
node0.skip = false

_editor.add_node(node0, 148.91947125898, 485.81236523337)

var node1 = ::rendergraph::nodes::shader::Shader()

node1.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoords;

out vec2 TexCoords;
out vec3 WorldPos;
out vec3 Normal;

uniform UBO
{
    mat4 projection;
    mat4 view;
    mat4 model;    
};

void main()
{
    TexCoords = aTexCoords;
    WorldPos = vec3(model * vec4(aPos, 1.0));
    Normal = mat3(model) * aNormal;   

    gl_Position =  projection * view * vec4(WorldPos, 1.0);
}
"
node1.tcs = ""
node1.tes = ""
node1.gs = ""
node1.fs = "
#version 330 core
out vec4 FragColor;
in vec2 TexCoords;
in vec3 WorldPos;
in vec3 Normal;

// material parameters
uniform sampler2D albedoMap;
uniform sampler2D normalMap;
uniform sampler2D metallicMap;
uniform sampler2D roughnessMap;
uniform sampler2D aoMap;

// IBL
uniform samplerCube irradianceMap;
uniform samplerCube prefilterMap;
uniform sampler2D brdfLUT;

uniform UBO
{
    // lights
    vec3 lightPositions[4];
    vec3 lightColors[4];

    vec3 camPos;    
};

const float PI = 3.14159265359;
// ----------------------------------------------------------------------------
// Easy trick to get tangent-normals to world-space to keep PBR code simplified.
// Don't worry if you don't get what's going on; you generally want to do normal 
// mapping the usual way for performance anways; I do plan make a note of this 
// technique somewhere later in the normal mapping tutorial.
vec3 getNormalFromMap()
{
    vec3 tangentNormal = texture(normalMap, TexCoords).xyz * 2.0 - 1.0;

    vec3 Q1  = dFdx(WorldPos);
    vec3 Q2  = dFdy(WorldPos);
    vec2 st1 = dFdx(TexCoords);
    vec2 st2 = dFdy(TexCoords);

    vec3 N   = normalize(Normal);
    vec3 T  = normalize(Q1*st2.t - Q2*st1.t);
    vec3 B  = -normalize(cross(N, T));
    mat3 TBN = mat3(T, B, N);

    return normalize(TBN * tangentNormal);
}
// ----------------------------------------------------------------------------
float DistributionGGX(vec3 N, vec3 H, float roughness)
{
    float a = roughness*roughness;
    float a2 = a*a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH*NdotH;

    float nom   = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;

    return nom / denom;
}
// ----------------------------------------------------------------------------
float GeometrySchlickGGX(float NdotV, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r*r) / 8.0;

    float nom   = NdotV;
    float denom = NdotV * (1.0 - k) + k;

    return nom / denom;
}
// ----------------------------------------------------------------------------
float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness)
{
    float NdotV = max(dot(N, V), 0.0);
    float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);

    return ggx1 * ggx2;
}
// ----------------------------------------------------------------------------
vec3 fresnelSchlick(float cosTheta, vec3 F0)
{
    return F0 + (1.0 - F0) * pow(max(1.0 - cosTheta, 0.0), 5.0);
}
// ----------------------------------------------------------------------------
vec3 fresnelSchlickRoughness(float cosTheta, vec3 F0, float roughness)
{
    return F0 + (max(vec3(1.0 - roughness), F0) - F0) * pow(max(1.0 - cosTheta, 0.0), 5.0);
}  
// ----------------------------------------------------------------------------
void main()
{       
    // material properties
    vec3 albedo = pow(texture(albedoMap, TexCoords).rgb, vec3(2.2));
    float metallic = texture(metallicMap, TexCoords).r;
    float roughness = texture(roughnessMap, TexCoords).r;
    float ao = texture(aoMap, TexCoords).r;
       
    // input lighting data
    vec3 N = getNormalFromMap();
    vec3 V = normalize(camPos - WorldPos);
    vec3 R = reflect(-V, N); 

    // calculate reflectance at normal incidence; if dia-electric (like plastic) use F0 
    // of 0.04 and if it's a metal, use the albedo color as F0 (metallic workflow)    
    vec3 F0 = vec3(0.04); 
    F0 = mix(F0, albedo, metallic);

    // reflectance equation
    vec3 Lo = vec3(0.0);
    for(int i = 0; i < 4; ++i) 
    {
        // calculate per-light radiance
        vec3 L = normalize(lightPositions[i] - WorldPos);
        vec3 H = normalize(V + L);
        float distance = length(lightPositions[i] - WorldPos);
        float attenuation = 1.0 / (distance * distance);
        vec3 radiance = lightColors[i] * attenuation;

        // Cook-Torrance BRDF
        float NDF = DistributionGGX(N, H, roughness);   
        float G   = GeometrySmith(N, V, L, roughness);    
        vec3 F    = fresnelSchlick(max(dot(H, V), 0.0), F0);        
        
        vec3 nominator    = NDF * G * F;
        float denominator = 4 * max(dot(N, V), 0.0) * max(dot(N, L), 0.0) + 0.001; // 0.001 to prevent divide by zero.
        vec3 specular = nominator / denominator;
        
         // kS is equal to Fresnel
        vec3 kS = F;
        // for energy conservation, the diffuse and specular light can't
        // be above 1.0 (unless the surface emits light); to preserve this
        // relationship the diffuse component (kD) should equal 1.0 - kS.
        vec3 kD = vec3(1.0) - kS;
        // multiply kD by the inverse metalness such that only non-metals 
        // have diffuse lighting, or a linear blend if partly metal (pure metals
        // have no diffuse light).
        kD *= 1.0 - metallic;                   
            
        // scale light by NdotL
        float NdotL = max(dot(N, L), 0.0);        

        // add to outgoing radiance Lo
        Lo += (kD * albedo / PI + specular) * radiance * NdotL; // note that we already multiplied the BRDF by the Fresnel (kS) so we won't multiply by kS again
    }   
    
    // ambient lighting (we now use IBL as the ambient term)
    vec3 F = fresnelSchlickRoughness(max(dot(N, V), 0.0), F0, roughness);
    
    vec3 kS = F;
    vec3 kD = 1.0 - kS;
    kD *= 1.0 - metallic;     
    
    vec3 irradiance = texture(irradianceMap, N).rgb;
    vec3 diffuse      = irradiance * albedo;
    
    // sample both the pre-filter map and the BRDF lut and combine them together as per the Split-Sum approximation to get the IBL specular part.
    const float MAX_REFLECTION_LOD = 4.0;
    vec3 prefilteredColor = textureLod(prefilterMap, R,  roughness * MAX_REFLECTION_LOD).rgb;    
    vec2 brdf  = texture(brdfLUT, vec2(max(dot(N, V), 0.0), roughness)).rg;
    vec3 specular = prefilteredColor * (F * brdf.x + brdf.y);

    vec3 ambient = (kD * diffuse + specular) * ao;
    
    vec3 color = ambient + Lo;

    // HDR tonemapping
    color = color / (color + vec3(1.0));
    // gamma correct
    color = pow(color, vec3(1.0/2.2)); 

    FragColor = vec4(color , 1.0);
}
"
node1.cs = ""
node1.render_gen()
node1.set_uniform("lightPositions[0]", [ -10, 10, 10 ])
node1.set_uniform("lightPositions[1]", [ 10, 10, 10 ])
node1.set_uniform("lightPositions[2]", [ -10, -10, 10 ])
node1.set_uniform("lightPositions[3]", [ 10, -10, 10 ])
node1.set_uniform("lightColors[0]", [ 300, 300, 300 ])
node1.set_uniform("lightColors[1]", [ 300, 300, 300 ])
node1.set_uniform("lightColors[2]", [ 300, 300, 300 ])
node1.set_uniform("lightColors[3]", [ 300, 300, 300 ])

_editor.add_node(node1, -234.64757067856, -114.22841016813)

var node2 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node2.type = "sphere"
node2.layout = [ "position", "normal", "texture" ]
node2.shape_params = {  }

_editor.add_node(node2, -31.55285166978, 395.15647287629)

var node3 = ::rendergraph::nodes::pass::Pass()

node3.once = false

_editor.add_node(node3, 407.61166520037, 532.04509164372)

var node4 = ::blueprint::nodes::input::Input()

node4.var_name = "normalMap"
node4.var_type = "texture"

_editor.add_node(node4, -743.01176080411, 357.69728796851)

var node5 = ::blueprint::nodes::input::Input()

node5.var_name = "albedoMap"
node5.var_type = "texture"

_editor.add_node(node5, -739.93283226574, 316.64461622002)

var node6 = ::blueprint::nodes::input::Input()

node6.var_name = "metallicMap"
node6.var_type = "texture"

_editor.add_node(node6, -738.39605671685, 274.31312179511)

var node7 = ::blueprint::nodes::input::Input()

node7.var_name = "roughnessMap"
node7.var_type = "texture"

_editor.add_node(node7, -736.76632851504, 231.68748051216)

var node8 = ::blueprint::nodes::input::Input()

node8.var_name = "aoMap"
node8.var_type = "texture"

_editor.add_node(node8, -734.0675703293, 189.27769857054)

var node9 = ::blueprint::nodes::input::Input()

node9.var_name = "model"
node9.var_type = "mat4"

_editor.add_node(node9, -518.43911721498, 459.78098073586)

var node10 = ::blueprint::nodes::input::Input()

node10.var_name = "projection"
node10.var_type = "mat4"

_editor.add_node(node10, -520.64192244461, 563.61311468079)

var node11 = ::blueprint::nodes::input::Input()

node11.var_name = "view"
node11.var_type = "mat4"

_editor.add_node(node11, -524.70613955746, 511.34279808277)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "camPos"
node12.var_type = "num3"

_editor.add_node(node12, -521.61700869931, 399.68666293813)

var node13 = ::blueprint::nodes::input::Input()

node13.var_name = "irradianceMap"
node13.var_type = "texture"

_editor.add_node(node13, -480.19296879982, 91.21640503176)

var node14 = ::blueprint::nodes::input::Input()

node14.var_name = "prefilterMap"
node14.var_type = "texture"

_editor.add_node(node14, -483.66284643903, 48.421101596777)

var node15 = ::blueprint::nodes::input::Input()

node15.var_name = "brdfLUT"
node15.var_type = "texture"

_editor.add_node(node15, -473.63870391439, -5.169408423593)

Blueprint.connect(node10, "var", node1, "projection")
Blueprint.connect(node11, "var", node1, "view")
Blueprint.connect(node9, "var", node1, "model")
Blueprint.connect(node12, "var", node1, "camPos")
Blueprint.connect(node4, "var", node1, "normalMap")
Blueprint.connect(node5, "var", node1, "albedoMap")
Blueprint.connect(node6, "var", node1, "metallicMap")
Blueprint.connect(node7, "var", node1, "roughnessMap")
Blueprint.connect(node8, "var", node1, "aoMap")
Blueprint.connect(node13, "var", node1, "irradianceMap")
Blueprint.connect(node14, "var", node1, "prefilterMap")
Blueprint.connect(node15, "var", node1, "brdfLUT")
Blueprint.connect(node1, "out", node0, "shader")
Blueprint.connect(node2, "out", node0, "va")
