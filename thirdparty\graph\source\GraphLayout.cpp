#include "graph/GraphLayout.h"
#include "graph/Graph.h"
#include "graph/Node.h"

#include <SM_Calc.h>
#include <random>
#include <cmath>
#include <algorithm>
#include <map>

namespace graph
{

GraphLayout::GraphLayout()
{
}

GraphLayout::~GraphLayout()
{
}

void GraphLayout::SetNodePosition(std::shared_ptr<Node> node, const sm::vec2& pos)
{
    node->SetPos(pos);
    
    // Also store in our internal position map
    for (auto& pair : m_node_positions) {
        if (pair.first == node) {
            pair.second = pos;
            return;
        }
    }
    m_node_positions.push_back({node, pos});
}

sm::vec2 GraphLayout::GetNodePosition(std::shared_ptr<Node> node) const
{
    return node->GetPos();
}

// Static layout methods
void GraphLayout::StressMinimization(Graph& graph)
{
    // Simple stress minimization implementation using force-directed approach
    auto& nodes = graph.GetNodes();
    if (nodes.empty()) return;
    
    const int iterations = 100;
    const float k = 1.0f; // Spring constant
    const float dt = 0.01f; // Time step
    
    // Initialize random positions if not set
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<float> dis(-5.0f, 5.0f);
    
    for (auto& node : nodes) {
        if (node->GetPos().x == 0 && node->GetPos().y == 0) {
            node->SetPos({dis(gen), dis(gen)});
        }
    }
    
    // Iterative force-directed layout
    for (int iter = 0; iter < iterations; ++iter) {
        std::vector<sm::vec2> forces(nodes.size(), {0, 0});
        
        // Calculate repulsive forces between all pairs
        for (size_t i = 0; i < nodes.size(); ++i) {
            for (size_t j = i + 1; j < nodes.size(); ++j) {
                sm::vec2 delta = nodes[i]->GetPos() - nodes[j]->GetPos();
                float dist = delta.Length();
                if (dist > 0.01f) {
                    sm::vec2 force = delta * (k * k / (dist * dist * dist));
                    forces[i] = forces[i] + force;
                    forces[j] = forces[j] - force;
                }
            }
        }
        
        // Calculate attractive forces for connected nodes
        for (size_t i = 0; i < nodes.size(); ++i) {
            auto& connections = nodes[i]->GetConnects();
            for (auto& conn : connections) {
                // Find index of connected node
                for (size_t j = 0; j < nodes.size(); ++j) {
                    if (nodes[j] == conn) {
                        sm::vec2 delta = conn->GetPos() - nodes[i]->GetPos();
                        float dist = delta.Length();
                        if (dist > 0.01f) {
                            sm::vec2 force = delta * (dist / k);
                            forces[i] = forces[i] + force;
                        }
                        break;
                    }
                }
            }
        }
        
        // Apply forces
        for (size_t i = 0; i < nodes.size(); ++i) {
            sm::vec2 newPos = nodes[i]->GetPos() + forces[i] * dt;
            nodes[i]->SetPos(newPos);
        }
    }
}

void GraphLayout::OptimalHierarchy(Graph& graph)
{
    // Simple hierarchical layout - arrange nodes in layers
    auto& nodes = graph.GetNodes();
    if (nodes.empty()) return;
    
    // Assign layers based on connectivity (simplified)
    std::vector<int> layers(nodes.size(), 0);
    
    // Simple layer assignment - nodes with no incoming connections go to layer 0
    for (size_t i = 0; i < nodes.size(); ++i) {
        int maxLayer = 0;
        // Check all other nodes to see if they connect to this one
        for (size_t j = 0; j < nodes.size(); ++j) {
            if (i != j) {
                auto& connections = nodes[j]->GetConnects();
                for (auto& conn : connections) {
                    if (conn == nodes[i]) {
                        maxLayer = std::max(maxLayer, layers[j] + 1);
                    }
                }
            }
        }
        layers[i] = maxLayer;
    }
    
    // Position nodes based on layers
    std::map<int, int> layerCounts;
    for (int layer : layers) {
        layerCounts[layer]++;
    }
    
    std::map<int, int> layerPositions;
    for (size_t i = 0; i < nodes.size(); ++i) {
        int layer = layers[i];
        float x = layerPositions[layer] * 2.0f - layerCounts[layer];
        float y = layer * 3.0f;
        nodes[i]->SetPos({x, y});
        layerPositions[layer]++;
    }
}

void GraphLayout::HierarchyRanking(Graph& graph)
{
    // Similar to OptimalHierarchy but with different spacing
    auto& nodes = graph.GetNodes();
    if (nodes.empty()) return;
    
    // Use a simple ranking based on node degree
    std::vector<std::pair<int, size_t>> rankings; // (degree, index)
    
    for (size_t i = 0; i < nodes.size(); ++i) {
        int degree = nodes[i]->GetConnects().size();
        rankings.push_back({degree, i});
    }
    
    // Sort by degree (descending)
    std::sort(rankings.begin(), rankings.end(), 
              [](const auto& a, const auto& b) { return a.first > b.first; });
    
    // Position nodes in a grid based on ranking
    int cols = std::max(1, (int)std::sqrt(nodes.size()));
    for (size_t i = 0; i < rankings.size(); ++i) {
        size_t nodeIdx = rankings[i].second;
        float x = (i % cols) * 3.0f;
        float y = (i / cols) * 3.0f;
        nodes[nodeIdx]->SetPos({x, y});
    }
}

// ForceDirectedLayout implementation
ForceDirectedLayout::ForceDirectedLayout()
    : m_iterations(50), m_repulsion_force(1.0f), m_attraction_force(0.1f)
{
}

ForceDirectedLayout::~ForceDirectedLayout()
{
}

void ForceDirectedLayout::Layout(const Graph& graph)
{
    // Use the static StressMinimization method for now
    Graph& mutableGraph = const_cast<Graph&>(graph);
    GraphLayout::StressMinimization(mutableGraph);
}

}
