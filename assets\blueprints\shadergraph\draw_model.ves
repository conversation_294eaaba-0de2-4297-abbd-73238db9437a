var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "stencil_test" : false, "rasterization" : "fill", "stencil_func" : "always", "stencil_mask" : 255, "cull" : "back", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node0.skip = false

_editor.add_node(node0, 383.03894618395, -4.3723831784738)

var node1 = ::shadergraph::nodes::shader_gen::ShaderGen()

_editor.add_node(node1, 216.51126212912, 63.238847903249)

var node2 = ::rendergraph::nodes::shader_code::ShaderCode()
node2.init_filepath("../../shaders/model_vert.glsl")

_editor.add_node(node2, 55.140644831376, 135.01740722014)

var node3 = ::shadergraph::nodes::frag_color::FragColor()

_editor.add_node(node3, 57.685210790249, 43.44295080676)

var node4 = ::blueprint::nodes::input::Input()

node4.var_name = "rgb"
node4.var_type = "num3"

_editor.add_node(node4, -89.944502336617, 41.23814559637)

var node5 = ::blueprint::nodes::input::Input()

node5.var_name = "model"
node5.var_type = "mat4"

_editor.add_node(node5, 63.612335205078, -46.620361328125)

var node6 = ::blueprint::nodes::input::Input()

node6.var_name = "view"
node6.var_type = "mat4"

_editor.add_node(node6, 61.612335205078, -95.620361328125)

var node7 = ::blueprint::nodes::input::Input()

node7.var_name = "projection"
node7.var_type = "mat4"

_editor.add_node(node7, 65.612335205078, -144.62036132813)

var node8 = ::blueprint::nodes::input::Input()

node8.var_name = "va"
node8.var_type = "va"

_editor.add_node(node8, 213.26631297163, -25.83801704046)

Blueprint.connect(node4, "var", node3, "rgb")
Blueprint.connect(node2, "code", node1, "vs")
Blueprint.connect(node3, "next", node1, "fs")
Blueprint.connect(node5, "var", node1, "model")
Blueprint.connect(node6, "var", node1, "view")
Blueprint.connect(node7, "var", node1, "projection")
Blueprint.connect(node1, "shader", node0, "shader")
Blueprint.connect(node8, "var", node0, "va")
