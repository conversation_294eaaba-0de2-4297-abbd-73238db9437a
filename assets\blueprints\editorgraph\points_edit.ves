var node0 = ::editorgraph::nodes::mouse_left_down::Mouse<PERSON>eftDown()

_editor.add_node(node0, -460.60379588581, -176.63070640734)

var node1 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node1, 499.98568575066, -153.81065376048)

var node2 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node2, -292.59387040054, -208.43882940278)

var node3 = ::geograph::nodes::draw_geometry::DrawGeometry()
node3.query_param("skip").value = false

_editor.add_node(node3, -744.29369411841, -917.48594219119)

var node4 = ::blueprint::nodes::number3::Number3()

node4.value.set(0, 0, 0.93263077735901)

_editor.add_node(node4, -898.22737342608, -942.64015897099)

var node5 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node5, 315.04033138758, -257.7534353733)

var node6 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node6, 128.44053010938, -222.36998523462)

var node7 = ::blueprint::nodes::number2::Number2()

node7.value.set(12.049763816764, -829.70875911507)

_editor.add_node(node7, -904.26287461202, -441.50069639264)

var node8 = ::blueprint::nodes::commentary::Commentary()

node8.set_size(1102.1489257813, 430.30456542969)
node8.title = "Add"

_editor.add_node(node8, 19.083372006396, -87.48476926136)

var node9 = ::blueprint::nodes::store::Store()

node9.var_name = "selected"

_editor.add_node(node9, -749.39727632676, -513.53746341362)

var node10 = ::blueprint::nodes::load::Load()

node10.var_name = "selected"

_editor.add_node(node10, -898.09141282912, -865.84286980377)

var node11 = ::blueprint::nodes::load::Load()

node11.var_name = "selected"

_editor.add_node(node11, -460.21952829307, -740.85421037646)

var node12 = ::blueprint::nodes::store::Store()

node12.var_name = "last_pos"

_editor.add_node(node12, -741.77223307612, -428.73688824897)

var node13 = ::blueprint::nodes::load::Load()

node13.var_name = "last_pos"

_editor.add_node(node13, -33.046468225988, -246.93707093265)

var node14 = ::blueprint::nodes::load::Load()

node14.var_name = "last_pos"

_editor.add_node(node14, -459.57587576358, -787.41188150411)

var node15 = ::blueprint::nodes::store::Store()

node15.var_name = "all_points"

_editor.add_node(node15, -750.29928426755, -577.95036368567)

var node16 = ::blueprint::nodes::commentary::Commentary()

node16.set_size(400, 292.4020690918)
node16.title = "Variates"

_editor.add_node(node16, -823.86647263596, -383.32813818116)

var node17 = ::blueprint::nodes::commentary::Commentary()

node17.set_size(400, 154.11874389648)
node17.title = "Draw"

_editor.add_node(node17, -818.00205522664, -820.16906581505)

var node18 = ::blueprint::nodes::list_add::ListAdd()
node18.query_param("unique").value = true

_editor.add_node(node18, 134.02129255725, -336.4924295254)

var node19 = ::geograph::nodes::circle::Circle()
node19.query_param("x").value = 0
node19.query_param("y").value = 0
node19.query_param("r").value = 11.111110687256
node19.query_param("fill").value = true
node19.query_param("color").value.set(0, 0.62222224473953, 0.53333336114883)

_editor.add_node(node19, -11.895780320281, -426.53826949537)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "all_points"

_editor.add_node(node20, -454.73666511794, -586.09128733939)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "all_points"

_editor.add_node(node21, -10.963496342561, -295.2751299762)

var node22 = ::blueprint::nodes::input::Input()

node22.var_name = "points"
node22.var_type = "array"

_editor.add_node(node22, -901.34315974236, -576.37857344479)

var node23 = ::blueprint::nodes::subgraph::Subgraph()
node23.load_from_file(_editor, "right_down_remove.ves")

_editor.add_node(node23, -258.06289656683, -613.65056753953)

var node24 = ::blueprint::nodes::subgraph::Subgraph()
node24.load_from_file(_editor, "left_down_select.ves")

_editor.add_node(node24, 73.6814005375, 13.083992149198)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "selected"

_editor.add_node(node25, -121.30861096245, 20.710172536763)

var node26 = ::blueprint::nodes::load::Load()

node26.var_name = "all_points"

_editor.add_node(node26, -121.66271826229, 64.958989660525)

var node27 = ::blueprint::nodes::input::Input()

node27.var_name = "selection"
node27.var_type = "array"

_editor.add_node(node27, -901.28284587955, -513.33236808696)

var node28 = ::blueprint::nodes::output::Output()

node28.var_name = "right_rm_succ"
node28.var_type = "bool"

_editor.add_node(node28, -62.872409670569, -604.01489850346)

var node29 = ::blueprint::nodes::output::Output()

node29.var_name = "select_succ"
node29.var_type = "bool"

_editor.add_node(node29, 285.02223668841, 45.774563640624)

var node30 = ::blueprint::nodes::subgraph::Subgraph()
node30.load_from_file(_editor, "translate_selected.ves")

_editor.add_node(node30, -259.4631369484, -762.74639165292)

var node31 = ::blueprint::nodes::load::Load()

node31.var_name = "selected"

_editor.add_node(node31, -454.04028320312, -630.34042358399)

var node32 = ::blueprint::nodes::input::Input()

node32.var_name = "cam_mat"
node32.var_type = "mat4"

_editor.add_node(node32, -890.40891557174, -648.43647416549)

var node33 = ::blueprint::nodes::store::Store()

node33.var_name = "cam_mat"

_editor.add_node(node33, -757.38232059608, -644.46730783235)

var node34 = ::blueprint::nodes::load::Load()

node34.var_name = "cam_mat"

_editor.add_node(node34, -461.11635777038, -232.61155766029)

var node35 = ::blueprint::nodes::load::Load()

node35.var_name = "cam_mat"

_editor.add_node(node35, -897.66244174689, -1019.3590429345)

var node36 = ::blueprint::nodes::load::Load()

node36.var_name = "cam_mat"

_editor.add_node(node36, -122.1549759522, -25.548830387561)

var node37 = ::blueprint::nodes::load::Load()

node37.var_name = "cam_mat"

_editor.add_node(node37, -456.14904304311, -681.5560117512)

Blueprint.connect(node32, "var", node33, "var")
Blueprint.connect(node33, "var", node37, "var")
Blueprint.connect(node33, "var", node36, "var")
Blueprint.connect(node33, "var", node35, "var")
Blueprint.connect(node33, "var", node34, "var")
Blueprint.connect(node27, "var", node9, "var")
Blueprint.connect(node9, "var", node31, "var")
Blueprint.connect(node9, "var", node25, "var")
Blueprint.connect(node9, "var", node11, "var")
Blueprint.connect(node9, "var", node10, "var")
Blueprint.connect(node22, "var", node15, "var")
Blueprint.connect(node15, "var", node26, "var")
Blueprint.connect(node26, "var", node24, "geos")
Blueprint.connect(node25, "var", node24, "selected")
Blueprint.connect(node36, "var", node24, "cam_mat")
Blueprint.connect(node24, "success", node29, "var")
Blueprint.connect(node15, "var", node21, "var")
Blueprint.connect(node15, "var", node20, "var")
Blueprint.connect(node20, "var", node23, "geos")
Blueprint.connect(node31, "var", node23, "selected")
Blueprint.connect(node37, "var", node23, "cam_mat")
Blueprint.connect(node23, "success", node28, "var")
Blueprint.connect(node7, "v2", node12, "var")
Blueprint.connect(node12, "var", node14, "var")
Blueprint.connect(node11, "var", node30, "selected")
Blueprint.connect(node14, "var", node30, "last_pos")
Blueprint.connect(node37, "var", node30, "cam_mat")
Blueprint.connect(node12, "var", node13, "var")
Blueprint.connect(node4, "v3", node3, "color")
Blueprint.connect(node35, "var", node3, "cam_mat")
Blueprint.connect(node0, "pos", node2, "screen")
Blueprint.connect(node34, "var", node2, "cam_mat")
Blueprint.connect(node2, "world", node19, "center")
Blueprint.connect(node21, "var", node18, "list")
Blueprint.connect(node19, "geo", node18, "add")
Blueprint.connect(node2, "world", node6, "src")
Blueprint.connect(node13, "var", node6, "dst")
Blueprint.connect(node6, "next", node5, "prev")
Blueprint.connect(node24, "success", node5, "cond")
Blueprint.connect(node18, "next", node5, "false")
Blueprint.connect(node24, "next", node1, "prev")
Blueprint.connect(node0, "event", node1, "event")
Blueprint.connect(node5, "next", node1, "action")
