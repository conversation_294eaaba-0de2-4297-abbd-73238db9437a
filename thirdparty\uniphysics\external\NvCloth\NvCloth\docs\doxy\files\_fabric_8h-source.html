<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Fabric.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Fabric.h</h1><a href="_fabric_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include "<a class="code" href="_callbacks_8h.html" title="All functions to initialize and use user provided callbacks are declared in this...">NvCloth/Callbacks.h</a>"</span>
<a name="l00033"></a>00033 <span class="preprocessor">#include "<a class="code" href="_allocator_8h.html" title="This file together with Callbacks.h define most memory management interfaces for...">NvCloth/Allocator.h</a>"</span>
<a name="l00034"></a>00034 <span class="preprocessor">#include &lt;PsAtomic.h&gt;</span>
<a name="l00035"></a>00035 
<a name="l00036"></a>00036 <span class="keyword">namespace </span>nv
<a name="l00037"></a>00037 {
<a name="l00038"></a>00038 <span class="keyword">namespace </span>cloth
<a name="l00039"></a>00039 {
<a name="l00040"></a>00040 
<a name="l00041"></a>00041 <span class="keyword">class </span>Factory;
<a name="l00042"></a>00042 
<a name="l00043"></a>00043 <span class="comment">// abstract cloth constraints and triangle indices</span>
<a name="l00044"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html">00044</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> : <span class="keyword">public</span> UserAllocated
<a name="l00045"></a>00045 {
<a name="l00046"></a>00046 <span class="keyword">protected</span>:
<a name="l00047"></a>00047     <a class="code" href="classnv_1_1cloth_1_1_fabric.html#bb5cffce0412bc67bafbb1c47b56886e">Fabric</a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp;);
<a name="l00048"></a>00048     <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_fabric.html#3d850841d4168a5827731f3fa4cc07c6">operator = </a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp;);
<a name="l00049"></a>00049 
<a name="l00050"></a>00050 <span class="keyword">protected</span>:
<a name="l00051"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html#bb5cffce0412bc67bafbb1c47b56886e">00051</a>     <a class="code" href="classnv_1_1cloth_1_1_fabric.html#bb5cffce0412bc67bafbb1c47b56886e">Fabric</a>() : <a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a>(1)
<a name="l00052"></a>00052     {
<a name="l00053"></a>00053     }
<a name="l00054"></a>00054 
<a name="l00055"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html#8d3748f793f73d1cc5547ee99d052038">00055</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html#8d3748f793f73d1cc5547ee99d052038">~Fabric</a>()
<a name="l00056"></a>00056     {
<a name="l00057"></a>00057         <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(0 == <a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a>);
<a name="l00058"></a>00058     }
<a name="l00059"></a>00059 
<a name="l00060"></a>00060 <span class="keyword">public</span>:
<a name="l00062"></a>00062     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_fabric.html#e6ab4bb76335c9af1a67435eb2520d62" title="Returns the Factory used to create this Fabric.">getFactory</a>() <span class="keyword">const</span> = 0;
<a name="l00063"></a>00063 
<a name="l00069"></a>00069     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#4d9348de98c1c00498709dc591fa27ba" title="Returns the number of constraint solve phases stored.">getNumPhases</a>() <span class="keyword">const</span> = 0;
<a name="l00070"></a>00070 
<a name="l00074"></a>00074     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#52c968ff1b808ab00d994db25bc01d83" title="Returns the number of rest lengths stored.">getNumRestvalues</a>() <span class="keyword">const</span> = 0;
<a name="l00075"></a>00075 
<a name="l00082"></a>00082     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#24d3ef1c25d42d981a12f5b7a96114e4" title="Returns the number of constraint stiffness values stored.">getNumStiffnessValues</a>() <span class="keyword">const</span> = 0;
<a name="l00083"></a>00083 
<a name="l00084"></a>00084 
<a name="l00088"></a>00088     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#d28fcf11b0c9ebb20325cafb5dbcde4d" title="Returns the number of sets stored.">getNumSets</a>() <span class="keyword">const</span> = 0;
<a name="l00089"></a>00089 
<a name="l00093"></a>00093     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#057b35a8f4b7cce31a0be2eb0704e52d" title="Returns the number of indices stored.">getNumIndices</a>() <span class="keyword">const</span> = 0;
<a name="l00095"></a>00095     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#8dd6c3990522e16832311a2b04b17619" title="Returns the number of particles.">getNumParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00097"></a>00097     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#aa6b9b09786b98e3be8cc9f362c1f09d" title="Returns the number of Tethers stored.">getNumTethers</a>() <span class="keyword">const</span> = 0;
<a name="l00099"></a>00099     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#8d15c9c15000eeaad9b855cb3ca1d8c8" title="Returns the number of triangles that make up the cloth mesh.">getNumTriangles</a>() <span class="keyword">const</span> = 0;
<a name="l00100"></a>00100 
<a name="l00102"></a>00102     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html#8343cbc315361fc0ebb1322009076c86" title="Scales all constraint rest lengths.">scaleRestvalues</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00104"></a>00104     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html#b884bf893050c00ec8bacb25a5dd76a0" title="Scales all tether lengths.">scaleTetherLengths</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00105"></a>00105 
<a name="l00106"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html#7566de18b6640949fcce3839238fb9ce">00106</a>     <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html#7566de18b6640949fcce3839238fb9ce">incRefCount</a>()
<a name="l00107"></a>00107     {
<a name="l00108"></a>00108         physx::shdfnd::atomicIncrement(&amp;<a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a>);
<a name="l00109"></a>00109         <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(<a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a> &gt; 0);
<a name="l00110"></a>00110     }
<a name="l00111"></a>00111 
<a name="l00113"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html#dbabafe4f0954eb5cea92463de89dfa0">00113</a>     <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html#dbabafe4f0954eb5cea92463de89dfa0" title="Returns true if the object is destroyed.">decRefCount</a>()
<a name="l00114"></a>00114     {
<a name="l00115"></a>00115         <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(<a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a> &gt; 0);
<a name="l00116"></a>00116         <span class="keywordtype">int</span> result = physx::shdfnd::atomicDecrement(&amp;<a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a>);
<a name="l00117"></a>00117         <span class="keywordflow">if</span> (result == 0)
<a name="l00118"></a>00118         {
<a name="l00119"></a>00119             <span class="keyword">delete</span> <span class="keyword">this</span>;
<a name="l00120"></a>00120             <span class="keywordflow">return</span> <span class="keyword">true</span>;
<a name="l00121"></a>00121         }
<a name="l00122"></a>00122         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00123"></a>00123     }
<a name="l00124"></a>00124 
<a name="l00125"></a>00125   <span class="keyword">protected</span>:
<a name="l00126"></a><a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">00126</a>     int32_t <a class="code" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a>;
<a name="l00127"></a>00127 };
<a name="l00128"></a>00128 
<a name="l00129"></a>00129 } <span class="comment">// namespace cloth</span>
<a name="l00130"></a>00130 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
