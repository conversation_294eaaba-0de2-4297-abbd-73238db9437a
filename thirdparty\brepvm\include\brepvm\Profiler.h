#pragma once

#include <string>
#include <chrono>
#include <unordered_map>

namespace brepvm {

class Profiler
{
public:
    static Profiler& Instance();
    
    void StartTimer(const std::string& name);
    void EndTimer(const std::string& name);
    
    double GetElapsedTime(const std::string& name) const;
    void PrintResults() const;
    void Clear();
    
private:
    Profiler() = default;
    
    struct TimerData {
        std::chrono::high_resolution_clock::time_point start_time;
        double total_time = 0.0;
        int call_count = 0;
    };
    
    std::unordered_map<std::string, TimerData> m_timers;
};

#define PROFILE_START(name) brepvm::Profiler::Instance().StartTimer(name)
#define PROFILE_END(name) brepvm::Profiler::Instance().EndTimer(name)

class ScopedProfiler
{
public:
    ScopedProfiler(const std::string& name) : m_name(name) {
        PROFILE_START(m_name);
    }
    
    ~ScopedProfiler() {
        PROFILE_END(m_name);
    }
    
private:
    std::string m_name;
};

#define PROFILE_SCOPE(name) brepvm::ScopedProfiler _prof(name)

} // namespace brepvm
