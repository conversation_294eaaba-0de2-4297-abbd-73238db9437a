var node0 = ::geograph::nodes::brush_to_gltf::BrushToGltf()
node0.query_param("adjacencies").value = false

_editor.add_node(node0, -20.676023581753, -291.10174525378)

var node1 = ::blueprint::nodes::fetch::Fetch()
node1.index = "node0"

_editor.add_node(node1, 384.76483479879, -331.68694907861)

var node2 = ::blueprint::nodes::fetch::Fetch()
node2.index = "va"

_editor.add_node(node2, 589.87377684355, -330.18435483226)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "geos"
node3.var_type = "array"

_editor.add_node(node3, 595.46840404795, 245.77793726673)

var node4 = ::rendergraph::nodes::draw::Draw()

node4.set_prim_type("triangles")
node4.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node4.skip = false

_editor.add_node(node4, 784.30380359571, -191.81447758435)

var node5 = ::rendergraph::nodes::shader::Shader()
node5.query_param("inc_dir").value = ""

node5.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;

out VS_OUT {
    vec3 frag_pos;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;
};

void main()
{
	vs_out.frag_pos = vec3(model * vec4(aPos, 1.0));
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"
node5.tcs = ""
node5.tes = ""
node5.gs = ""
node5.fs = "
#version 330 core
out vec4 FragColor;

in VS_OUT {
    vec3 frag_pos;
} fs_in;

uniform UBO
{
	vec3 light_pos;
	vec3 cam_pos;
};

void main()
{
 	vec3 dFdxPos = dFdx( fs_in.frag_pos );
    vec3 dFdyPos = dFdy( fs_in.frag_pos );
    vec3 N = normalize( cross(dFdxPos,dFdyPos ));

	// ambient
	vec3 ambient = vec3(0.25);

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * vec3(1.0);

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(N, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    FragColor = vec4(ambient + diffuse + specular, 1.0); 
}
"
node5.cs = ""
node5.render_gen()

_editor.add_node(node5, 595.83667455228, -102.3677095474)

var node6 = ::rendergraph::nodes::clear::Clear()

node6.masks = [ "color", "depth" ]
node6.values = { "color" : [ 0.5, 0.5, 0.5, 1 ] }

_editor.add_node(node6, 1036.6602889827, 386.80421665113)

var node7 = ::blueprint::nodes::perspective::Perspective()

node7.fovy = 45
node7.aspect = 0
node7.znear = 0.1
node7.zfar = 100

_editor.add_node(node7, 413.60400748731, -99.260075321102)

var node8 = ::blueprint::nodes::proxy::Proxy()

node8.real_name = "view_cam"
node8.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node8, 409.76615286487, 48.85559416076)

var node9 = ::blueprint::nodes::number3::Number3()

node9.value.set(5.9766573905945, 5.6433238983154, 5.3099908828735)

_editor.add_node(node9, 412.51009621452, -222.35700607071)

var node10 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node10, 1204.9441409146, 267.91360697397)

var node11 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node11, 188.36502375197, -214.470801618)

var node12 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node12, 1011.9389355872, 20.226619751981)

var node13 = ::geograph::nodes::draw_geometry::DrawGeometry()
node13.query_param("skip").value = false

_editor.add_node(node13, 783.1307712437, 32.615556424757)

var node14 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node14, 560.9265762203, 104.9157175414)

var node15 = ::blueprint::nodes::list_flatten::ListFlatten()

_editor.add_node(node15, 737.37665349787, 245.690715443)

var node16 = ::blueprint::nodes::cache::Cache()
node16.query_param("disable").value = false

_editor.add_node(node16, 889.19487970526, 242.96342052113)

Blueprint.connect(node10, "out", node13, "geos")
Blueprint.connect(node8, "mat", node13, "cam_mat")
Blueprint.connect(node8, "zoom", node7, "fovy")
Blueprint.connect(node7, "mat", node14, "a")
Blueprint.connect(node8, "mat", node14, "b")
Blueprint.connect(node8, "mat", node5, "view")
Blueprint.connect(node7, "mat", node5, "projection")
Blueprint.connect(node9, "v3", node5, "light_pos")
Blueprint.connect(node8, "pos", node5, "cam_pos")
Blueprint.connect(node6, "next", node10, "prev")
Blueprint.connect(node16, "var", node10, "in")
Blueprint.connect(node12, "next", node10, "do")
Blueprint.connect(node3, "var", node15, "list")
Blueprint.connect(node15, "list", node16, "var")
Blueprint.connect(node10, "out", node0, "brush")
Blueprint.connect(node0, "gltf", node11, "in")
Blueprint.connect(node11, "out", node12, "cond")
Blueprint.connect(node13, "next", node12, "true")
Blueprint.connect(node4, "next", node12, "false")
Blueprint.connect(node0, "gltf", node1, "items")
Blueprint.connect(node1, "item", node2, "items")
Blueprint.connect(node5, "out", node4, "shader")
Blueprint.connect(node2, "item", node4, "va")
