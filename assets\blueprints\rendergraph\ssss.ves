var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node0.skip = false

_editor.add_node(node0, 419.17248364728, -614.94842814451)

var node1 = ::rendergraph::nodes::clear::Clear()

node1.masks = [ "depth", "color" ]
node1.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node1, 152.71743481182, -385.48383761108)

var node2 = ::rendergraph::nodes::shader::Shader()

node2.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoords;

out VS_OUT 
{
	vec2 TexCoords;
	vec3 WorldPos;
	vec3 Normal;
} vs_out;

uniform UBO
{
	mat4 projection;
	mat4 view;
	mat4 model;	
};

void main()
{
    vs_out.TexCoords = aTexCoords;
    vs_out.WorldPos = vec3(model * vec4(aPos, 1.0));
    vs_out.Normal = mat3(model) * aNormal;   
    gl_Position =  projection * view * vec4(vs_out.WorldPos, 1.0);
}
"
node2.tcs = ""
node2.tes = ""
node2.gs = ""
node2.fs = "
#version 330 core
out vec4 FragColor;

in VS_OUT 
{
	vec2 TexCoords;
	vec3 WorldPos;
	vec3 Normal;
} fs_in;

#define N_LIGHTS 3

struct Light {
    vec3 position;
    vec3 direction;
    float falloffStart;
    float falloffWidth;
    vec3 color;
    float attenuation;
    float farPlane;
    float bias;
    //mat4 viewProjection;
};

uniform UBO
{
    Light lights[N_LIGHTS];
    vec3 viewPos;
    float specularFresnel;
    float ambient;
};

uniform sampler2D diffuseTex;
uniform sampler2D normalTex;
uniform sampler2D aoTex;
uniform sampler2D beckmannTex;

float Fresnel(vec3 _half, float f0) 
{
    float base = 1.0 - dot(viewPos, _half);
    float exponential = pow(base, 5.0);
    return exponential + f0 * (1.0 - exponential);
}

float SpecularKSK(vec3 normal, vec3 light, float roughness) 
{
    vec3 _half = viewPos + light;
    vec3 halfn = normalize(_half);

    float ndotl = max(dot(normal, light), 0.0);
    float ndoth = max(dot(normal, halfn), 0.0);

    float ph = pow(2.0 * texture(beckmannTex, vec2(ndoth, roughness)).r, 10.0);
    float f = mix(0.25, Fresnel(halfn, 0.028), specularFresnel);
    float ksk = max(ph * f / dot(_half, _half), 0.0);

    return ndotl * ksk;   
}

void main()
{
    vec3 N = normalize(fs_in.Normal);
    vec3 V = normalize(viewPos - fs_in.WorldPos);
    vec3 normal = N;

    vec4 albedo = texture(diffuseTex, vec2(fs_in.TexCoords.x, 1.0 - fs_in.TexCoords.y));

    float occlusion = texture(aoTex, fs_in.TexCoords).r;
    float intensity = 0.5;
    float roughness = 0.5;

    vec4 color = vec4(0.0, 0.0, 0.0, 0.0);

    for (int i = 0; i < N_LIGHTS; i++) 
    {
        vec3 light = lights[i].position - fs_in.WorldPos;
        float dist = length(light);
        light /= dist;

        float spot = dot(lights[i].direction, -light);
        if (spot > lights[i].falloffStart) 
        {
            // Calculate attenuation:
            float curve = min(pow(dist / lights[i].farPlane, 6.0), 1.0);
            float attenuation = mix(1.0 / (1.0 + lights[i].attenuation * dist * dist), 0.0, curve);

            // And the spot light falloff:
            spot = clamp((spot - lights[i].falloffStart) / lights[i].falloffWidth, 0.0, 1.0);

            // Calculate some terms we will use later on:
            vec3 f1 = lights[i].color * attenuation * spot;
            vec3 f2 = albedo.rgb * f1;

            // Calculate the diffuse and specular lighting:
            vec3 diffuse = vec3(clamp(dot(light, normal), 0.0, 1.0));
            float specular = intensity * SpecularKSK(normal, light, roughness);

            // And also the shadowing:
//            float shadow = ShadowPCF(fs_in.WorldPos, i, 3, 1.0);
			float shadow = 1.0;

            // Add the diffuse and specular components:
#ifdef SEPARATE_SPECULARS
            color.rgb += shadow * f2 * diffuse;
            specularColor.rgb += shadow * f1 * specular;
#else
            color.rgb += shadow * (f2 * diffuse + f1 * specular);
#endif

            // Add the transmittance component:
//            if (sssEnabled)
//                color.rgb += f2 * SSSSTransmittance(translucency, sssWidth, fs_in.WorldPos, input.normal, light, shadowMaps[i], lights[i].viewProjection, lights[i].farPlane);
        }
    }

    // Add the ambient component:
    color.rgb += occlusion * ambient * albedo.rgb;

    color.a = 1.0;

    FragColor = color;
}

"
node2.cs = ""
node2.render_gen()
node2.set_uniform("lights[0].position", [ -0.425, 0.515, 0.48 ])
node2.set_uniform("lights[0].direction", [ 0.6, -0.378, -0.7 ])
node2.set_uniform("lights[0].falloffStart", [ 0.92395569947027 ])
node2.set_uniform("lights[0].falloffWidth", [ 0.05 ])
node2.set_uniform("lights[0].color", [ 0.5, 0.5, 0.5 ])
node2.set_uniform("lights[0].attenuation", [ 0.0078125 ])
node2.set_uniform("lights[0].farPlane", [ 10 ])
node2.set_uniform("lights[0].bias", [ -0.01 ])
node2.set_uniform("lights[1].position", [ 0.466, 0.457, 0.457 ])
node2.set_uniform("lights[1].direction", [ -0.519, -0.4, -0.75 ])
node2.set_uniform("lights[1].falloffStart", [ 0.92395569947027 ])
node2.set_uniform("lights[1].falloffWidth", [ 0.05 ])
node2.set_uniform("lights[1].color", [ 0.5, 0.5, 0.5 ])
node2.set_uniform("lights[1].attenuation", [ 0.0078125 ])
node2.set_uniform("lights[1].farPlane", [ 10 ])
node2.set_uniform("lights[1].bias", [ -0.01 ])
node2.set_uniform("lights[2].position", [ 0.0587, 0.59, 0.8 ])
node2.set_uniform("lights[2].direction", [ 0.075, -0.448, -0.891 ])
node2.set_uniform("lights[2].falloffStart", [ 0.92395569947027 ])
node2.set_uniform("lights[2].falloffWidth", [ 0.05 ])
node2.set_uniform("lights[2].color", [ 0.5, 0.5, 0.5 ])
node2.set_uniform("lights[2].attenuation", [ 0.0078125 ])
node2.set_uniform("lights[2].farPlane", [ 10 ])
node2.set_uniform("lights[2].bias", [ -0.01 ])
node2.set_uniform("specularFresnel", [ 1.5927958488464 ])
node2.set_uniform("ambient", [ 0.19279581308365 ])

_editor.add_node(node2, 150.95113152161, -704.56720496823)

var node3 = ::rendergraph::nodes::texture::Texture()
node3.query_param("unif_name").value = "u_tex"
node3.gamma_correction = false
node3.init_texture("assets/textures/beckmann.jpg")

_editor.add_node(node3, -103.6992535953, -685.9844071648)

var node4 = ::blueprint::nodes::perspective::Perspective()

node4.fovy = 45
node4.aspect = 0
node4.znear = 0.1
node4.zfar = 100

_editor.add_node(node4, -75.04054660547, -448.01030607813)

var node5 = ::blueprint::nodes::camera3d::Camera3d()

node5.cam.position.set(-0.13016721848106, 0.33143328307611, 0.41881437280284)
node5.cam.yaw = -66.2
node5.cam.pitch = -20.5
node5.cam.zoom = 45
node5.cam.update_vectors()
node5.speed = 0.01

_editor.add_node(node5, -299.43589083762, -395.79108780427)

var node6 = ::rendergraph::nodes::render_target::RenderTarget()

node6.width = 2048
node6.height = 2048

_editor.add_node(node6, -240.2319044918, -1505.2498841702)

var node7 = ::rendergraph::nodes::texture::Texture()
node7.query_param("unif_name").value = "u_tex"
node7.gamma_correction = false
node7.init_texture(2048, 2048, "rgba8")

_editor.add_node(node7, -465.62440443845, -1368.7714169007)

var node8 = ::rendergraph::nodes::texture::Texture()
node8.query_param("unif_name").value = "u_tex"
node8.gamma_correction = false
node8.init_texture(2048, 2048, "depth")

_editor.add_node(node8, -465.17868555518, -1702.8043428238)

var node9 = ::rendergraph::nodes::pass::Pass()

node9.once = false

_editor.add_node(node9, 582.08497865463, -534.47724821779)

var node10 = ::blueprint::nodes::commentary::Commentary()

node10.set_size(1077.5629882813, 744.18518066406)
node10.title = "Lighting"

_editor.add_node(node10, 132.43077759375, -274.40898269922)

var node11 = ::rendergraph::nodes::draw::Draw()

node11.set_prim_type("tri_strip")
node11.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node11.skip = false

_editor.add_node(node11, 572.66582932266, -1396.7914714975)

var node12 = ::rendergraph::nodes::clear::Clear()

node12.masks = [ "color" ]
node12.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node12, 292.00137717654, -1200.0579559291)

var node13 = ::rendergraph::nodes::shader::Shader()

node13.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;
out vec2 TexCoord;
void main()
{
    TexCoord = vec2(aTexCoord.x, aTexCoord.y);
    gl_Position = vec4(aPos, 1.0);
}
"
node13.tcs = ""
node13.tes = ""
node13.gs = ""
node13.fs = "
#version 330 core
out vec4 FragColor;

in vec2 TexCoord;

uniform sampler2D color_tex;
uniform sampler2D depth_tex;

uniform UBO
{
	float SSSIntencity;	
};

#define SSSS_FOVY 20.0
#define SSSS_N_SAMPLES 17
vec4 kernel[] = vec4[](
    vec4(0.536343, 0.624624, 0.748867, 0),
    vec4(0.00317394, 0.000134823, 3.77269e-005, -2),
    vec4(0.0100386, 0.000914679, 0.000275702, -1.53125),
    vec4(0.0144609, 0.00317269, 0.00106399, -1.125),
    vec4(0.0216301, 0.00794618, 0.00376991, -0.78125),
    vec4(0.0347317, 0.0151085, 0.00871983, -0.5),
    vec4(0.0571056, 0.0287432, 0.0172844, -0.28125),
    vec4(0.0582416, 0.0659959, 0.0411329, -0.125),
    vec4(0.0324462, 0.0656718, 0.0532821, -0.03125),
    vec4(0.0324462, 0.0656718, 0.0532821, 0.03125),
    vec4(0.0582416, 0.0659959, 0.0411329, 0.125),
    vec4(0.0571056, 0.0287432, 0.0172844, 0.28125),
    vec4(0.0347317, 0.0151085, 0.00871983, 0.5),
    vec4(0.0216301, 0.00794618, 0.00376991, 0.78125),
    vec4(0.0144609, 0.00317269, 0.00106399, 1.125),
    vec4(0.0100386, 0.000914679, 0.000275702, 1.53125),
    vec4(0.00317394, 0.000134823, 3.77269e-005, 2)
);

vec4 SSS(vec4 SceneColor, vec2 uv, vec2 SSSIntencity) 
{
    // Fetch linear depth of current pixel
    float depth = texture(depth_tex, uv).r;                                   
    // Calculate the sssWidth scale (1.0 for a unit plane sitting on the
    // projection window):
    float distanceToProjectionWindow = 1.0 / tan(0.5 * radians(SSSS_FOVY));
    float scale = distanceToProjectionWindow / depth;
    // Calculate the final step to fetch the surrounding pixels:
    vec2 finalStep = SSSIntencity;
    finalStep *= SceneColor.a; // Modulate it using the alpha channel.
    finalStep *= 1.0 / 3.0; // Divide by 3 as the kernels range from -3 to 3.
    // Accumulate the center sample:
    vec4 colorBlurred = SceneColor;
    colorBlurred.rgb *= kernel[0].rgb;
    // Accumulate the other samples:
    for (int i = 1; i < SSSS_N_SAMPLES; i++) 
    {
        // Fetch color and depth for current sample:
        vec2 offset = uv + kernel[i].a * finalStep;
        vec4 color = texture(color_tex, offset);
        // Accumulate:
        colorBlurred.rgb += kernel[i].rgb * color.rgb;
    }
    return colorBlurred;
}

void main()
{
//    FragColor = texture(color_tex, TexCoord);
    vec4 SceneColor = texture(color_tex, TexCoord);
    //    float SSSIntencity = (_SSSScale * _CameraDepthTexture_TexelSize.x);
    vec3 BlurColor = SSS(SceneColor, TexCoord, vec2(SSSIntencity, 0)).rgb;
    FragColor = vec4(BlurColor, SceneColor.a);
}
"
node13.cs = ""
node13.render_gen()
node13.set_uniform("SSSIntencity", [ 0.1 ])

_editor.add_node(node13, 119.67524652302, -1347.6761985859)

var node14 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node14.type = "quad"
node14.layout = [ "position", "texture" ]
node14.shape_params = {  }

_editor.add_node(node14, 304.43148949626, -1455.9785878489)

var node15 = ::rendergraph::nodes::render_target::RenderTarget()

node15.width = 2048
node15.height = 2048

_editor.add_node(node15, 362.15171412109, -1647.1301340625)

var node16 = ::rendergraph::nodes::texture::Texture()
node16.query_param("unif_name").value = "u_tex"
node16.gamma_correction = false
node16.init_texture(2048, 2048, "rgba8")

_editor.add_node(node16, 150.95176294922, -1638.3301340625)

var node17 = ::rendergraph::nodes::draw::Draw()

node17.set_prim_type("tri_strip")
node17.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node17.skip = false

_editor.add_node(node17, 1557.9023166477, -1775.3101226795)

var node18 = ::rendergraph::nodes::shader::Shader()

node18.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;
out vec2 TexCoord;
void main()
{
    TexCoord = vec2(aTexCoord.x, aTexCoord.y);
    gl_Position = vec4(aPos, 1.0);
}
"
node18.tcs = ""
node18.tes = ""
node18.gs = ""
node18.fs = "
#version 330 core
out vec4 FragColor;

in vec2 TexCoord;

uniform sampler2D color_tex;
uniform sampler2D depth_tex;

uniform UBO
{
	float SSSIntencity;	
};

#define SSSS_FOVY 20.0
#define SSSS_N_SAMPLES 17
vec4 kernel[] = vec4[](
    vec4(0.536343, 0.624624, 0.748867, 0),
    vec4(0.00317394, 0.000134823, 3.77269e-005, -2),
    vec4(0.0100386, 0.000914679, 0.000275702, -1.53125),
    vec4(0.0144609, 0.00317269, 0.00106399, -1.125),
    vec4(0.0216301, 0.00794618, 0.00376991, -0.78125),
    vec4(0.0347317, 0.0151085, 0.00871983, -0.5),
    vec4(0.0571056, 0.0287432, 0.0172844, -0.28125),
    vec4(0.0582416, 0.0659959, 0.0411329, -0.125),
    vec4(0.0324462, 0.0656718, 0.0532821, -0.03125),
    vec4(0.0324462, 0.0656718, 0.0532821, 0.03125),
    vec4(0.0582416, 0.0659959, 0.0411329, 0.125),
    vec4(0.0571056, 0.0287432, 0.0172844, 0.28125),
    vec4(0.0347317, 0.0151085, 0.00871983, 0.5),
    vec4(0.0216301, 0.00794618, 0.00376991, 0.78125),
    vec4(0.0144609, 0.00317269, 0.00106399, 1.125),
    vec4(0.0100386, 0.000914679, 0.000275702, 1.53125),
    vec4(0.00317394, 0.000134823, 3.77269e-005, 2)
);

vec4 SSS(vec4 SceneColor, vec2 uv, vec2 SSSIntencity) 
{
    // Fetch linear depth of current pixel
    float depth = texture(depth_tex, uv).r;                                   
    // Calculate the sssWidth scale (1.0 for a unit plane sitting on the
    // projection window):
    float distanceToProjectionWindow = 1.0 / tan(0.5 * radians(SSSS_FOVY));
    float scale = distanceToProjectionWindow / depth;
    // Calculate the final step to fetch the surrounding pixels:
    vec2 finalStep = SSSIntencity;
    finalStep *= SceneColor.a; // Modulate it using the alpha channel.
    finalStep *= 1.0 / 3.0; // Divide by 3 as the kernels range from -3 to 3.
    // Accumulate the center sample:
    vec4 colorBlurred = SceneColor;
    colorBlurred.rgb *= kernel[0].rgb;
    // Accumulate the other samples:
    for (int i = 1; i < SSSS_N_SAMPLES; i++) 
    {
        // Fetch color and depth for current sample:
        vec2 offset = uv + kernel[i].a * finalStep;
        vec4 color = texture(color_tex, offset);
        // Accumulate:
        colorBlurred.rgb += kernel[i].rgb * color.rgb;
    }
    return colorBlurred;
}

void main()
{
//    FragColor = texture(color_tex, TexCoord);
    vec4 SceneColor = texture(color_tex, TexCoord);
    //    float SSSIntencity = (_SSSScale * _CameraDepthTexture_TexelSize.x);
    vec3 BlurColor = SSS(SceneColor, TexCoord, vec2(0, SSSIntencity)).rgb;
    FragColor = vec4(BlurColor, SceneColor.a);
}
"
node18.cs = ""
node18.render_gen()
node18.set_uniform("SSSIntencity", [ 0.1 ])

_editor.add_node(node18, 1347.9849612758, -1697.7895281063)

var node19 = ::rendergraph::nodes::render_target::RenderTarget()

node19.width = 2048
node19.height = 2048

_editor.add_node(node19, 1401.730863039, -1844.5713730179)

var node20 = ::rendergraph::nodes::texture::Texture()
node20.query_param("unif_name").value = "u_tex"
node20.gamma_correction = false
node20.init_texture(2048, 2048, "rgba8")

_editor.add_node(node20, 1200.0660068837, -1839.5724094096)

var node21 = ::blueprint::nodes::commentary::Commentary()

node21.set_size(912.26666259766, 830.73748779297)
node21.title = "SSSS BlurX"

_editor.add_node(node21, 394.27344974375, -1085.8761199969)

var node22 = ::blueprint::nodes::commentary::Commentary()

node22.set_size(863.86663818359, 340.52084350586)
node22.title = "SSSS BlurY"

_editor.add_node(node22, 1434.6913663052, -1603.1961572919)

var node23 = ::blueprint::nodes::commentary::Commentary()

node23.set_size(486.10208129883, 672.83123779297)
node23.title = "Lighting result"

_editor.add_node(node23, -364.14337667002, -1277.0318127864)

var node24 = ::blueprint::nodes::input::Input()

node24.var_name = "diffuseTex"
node24.var_type = "texture"

_editor.add_node(node24, -321.05248264062, -845.91170012109)

var node25 = ::blueprint::nodes::input::Input()

node25.var_name = "aoTex"
node25.var_type = "texture"

_editor.add_node(node25, -321.05245822656, -909.71173674219)

var node26 = ::blueprint::nodes::input::Input()

node26.var_name = "normalTex"
node26.var_type = "texture"

_editor.add_node(node26, -318.85250705469, -973.51171232813)

var node27 = ::blueprint::nodes::input::Input()

node27.var_name = "model"
node27.var_type = "model"

_editor.add_node(node27, 174.8813120815, -955.82282205824)

var node28 = ::blueprint::nodes::input::Input()

node28.var_name = "view"
node28.var_type = "mat4"

_editor.add_node(node28, -297.98043363494, -497.85191995863)

var node29 = ::blueprint::nodes::input::Input()

node29.var_name = "viewPos"
node29.var_type = "num3"

_editor.add_node(node29, -297.07132053125, -542.39738282706)

var node30 = ::blueprint::nodes::input::Input()

node30.var_name = "fovy"
node30.var_type = "num"

_editor.add_node(node30, -296.16224626811, -587.85193660458)

var node31 = ::blueprint::nodes::output::Output()

node31.var_name = "tex"
node31.var_type = "texture"

_editor.add_node(node31, 1400.2367896017, -1937.7725348178)

var node32 = ::rendergraph::nodes::pass::Pass()

node32.once = false

_editor.add_node(node32, 755.5450033252, -1319.8714056043)

var node33 = ::rendergraph::nodes::pass::Pass()

node33.once = false

_editor.add_node(node33, 1760.309732777, -1695.188864968)

Blueprint.connect(node17, "next", node33, "do")
Blueprint.connect(node19, "fbo", node33, "fbo")
Blueprint.connect(node11, "next", node32, "do")
Blueprint.connect(node15, "fbo", node32, "fbo")
Blueprint.connect(node30, "var", node4, "fovy")
Blueprint.connect(node20, "tex", node31, "var")
Blueprint.connect(node20, "tex", node19, "col0")
Blueprint.connect(node16, "tex", node15, "col0")
Blueprint.connect(node0, "next", node9, "do")
Blueprint.connect(node6, "fbo", node9, "fbo")
Blueprint.connect(node8, "tex", node18, "depth_tex")
Blueprint.connect(node16, "tex", node18, "color_tex")
Blueprint.connect(node32, "next", node17, "prev")
Blueprint.connect(node18, "out", node17, "shader")
Blueprint.connect(node14, "out", node17, "va")
Blueprint.connect(node8, "tex", node13, "depth_tex")
Blueprint.connect(node7, "tex", node13, "color_tex")
Blueprint.connect(node12, "next", node11, "prev")
Blueprint.connect(node13, "out", node11, "shader")
Blueprint.connect(node14, "out", node11, "va")
Blueprint.connect(node7, "tex", node6, "col0")
Blueprint.connect(node8, "tex", node6, "depth")
Blueprint.connect(node4, "mat", node2, "projection")
Blueprint.connect(node28, "var", node2, "view")
Blueprint.connect(node29, "var", node2, "viewPos")
Blueprint.connect(node3, "tex", node2, "beckmannTex")
Blueprint.connect(node24, "var", node2, "diffuseTex")
Blueprint.connect(node25, "var", node2, "aoTex")
Blueprint.connect(node26, "var", node2, "normalTex")
Blueprint.connect(node1, "next", node0, "prev")
Blueprint.connect(node2, "out", node0, "shader")
Blueprint.connect(node27, "var", node0, "model")
