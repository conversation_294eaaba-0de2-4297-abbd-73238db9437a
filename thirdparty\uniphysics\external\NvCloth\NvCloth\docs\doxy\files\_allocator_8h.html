<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Allocator.h File Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Allocator.h File Reference</h1>This file together with <a class="el" href="_callbacks_8h.html" title="All functions to initialize and use user provided callbacks are declared in this...">Callbacks.h</a> define most memory management interfaces for internal use.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;PsArray.h&gt;</code><br>
<code>#include &lt;PsHashMap.h&gt;</code><br>
<code>#include &lt;PsAlignedMalloc.h&gt;</code><br>
<code>#include &quot;<a class="el" href="_callbacks_8h-source.html">NvCloth/Callbacks.h</a>&quot;</code><br>

<p>
<a href="_allocator_8h-source.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
This file together with <a class="el" href="_callbacks_8h.html" title="All functions to initialize and use user provided callbacks are declared in this...">Callbacks.h</a> define most memory management interfaces for internal use. 
<p>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
