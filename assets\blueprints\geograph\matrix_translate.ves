var node0 = ::blueprint::nodes::input::Input()

node0.var_name = "matrixes"
node0.var_type = "array"

_editor.add_node(node0, -534.44491496483, 317.01669967898)

var node1 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node1, -218.95905519947, 269.96050959699)

var node2 = ::scenegraph::nodes::transform3d::Transform3d()

_editor.add_node(node2, -509.33123779297, 152.38043212891)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "translate"
node3.var_type = "num3"

_editor.add_node(node3, -664.90970033873, 152.77845145017)

var node4 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node4, -381.54380243713, 184.38900772583)

var node5 = ::blueprint::nodes::output::Output()

node5.var_name = "matrixes"
node5.var_type = "array"

_editor.add_node(node5, -71.647620530394, 292.20351652343)

var node6 = ::blueprint::nodes::list_flatten::ListFlatten()

_editor.add_node(node6, -390.53263089279, 318.22082416145)

Blueprint.connect(node3, "var", node2, "translate")
Blueprint.connect(node2, "mat", node4, "a")
Blueprint.connect(node1, "curr_item", node4, "b")
Blueprint.connect(node0, "var", node6, "list")
Blueprint.connect(node6, "list", node1, "items")
Blueprint.connect(node4, "v", node1, "eval")
Blueprint.connect(node1, "result", node5, "var")
