var node0 = ::geograph::nodes::resample::Resample()
node0.query_param("length").value = 205.86209106445

_editor.add_node(node0, -220.72456405334, 350.91609174087)

var node1 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node1, 12.490905811909, 100.39695407786)

var node2 = ::blueprint::nodes::list_shift::ListShift()
node2.query_param("wrap").value = false

_editor.add_node(node2, -474.05458123933, -2.7340252096595)

var node3 = ::blueprint::nodes::integer::Integer()

node3.value = 1

_editor.add_node(node3, -606.247964918, -36.615770473919)

var node4 = ::scenegraph::nodes::transform::Transform()

_editor.add_node(node4, -274.54437128398, -265.29074341636)

var node5 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node5, -314.08594717099, 98.573541192831)

var node6 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node6, -306.72809593139, -15.74959930305)

var node7 = ::geograph::nodes::line::Line()
node7.query_param("x0").value = 0
node7.query_param("y0").value = 0
node7.query_param("x1").value = 100
node7.query_param("y1").value = 100

_editor.add_node(node7, -133.17827700169, 26.784526990681)

var node8 = ::blueprint::nodes::length::Length()

_editor.add_node(node8, -135.38607238079, 123.7382623809)

var node9 = ::blueprint::nodes::list_add_f::ListAddF()

_editor.add_node(node9, 285.29915150741, 110.86012365396)

var node10 = ::blueprint::nodes::list_back::ListBack()

_editor.add_node(node10, 154.72371038791, 83.603577182041)

var node11 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node11, -145.74922777563, -175.07808631169)

var node12 = ::blueprint::nodes::length::Length()

_editor.add_node(node12, -212.7259338626, 269.43419518591)

var node13 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node13, -452.52117347719, -306.26530103344)

var node14 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node14, 337.18702708151, -116.1564782706)

var node15 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node15, 26.592963163803, -184.48180766785)

var node16 = ::scenegraph::nodes::transform::Transform()

_editor.add_node(node16, 199.32021647078, -193.5727374401)

var node17 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node17, 25.507694897201, -291.40386044989)

var node18 = ::blueprint::nodes::store::Store()

node18.var_name = "points"

_editor.add_node(node18, -78.053371956629, 362.71489681935)

var node19 = ::blueprint::nodes::load::Load()

node19.var_name = "points"

_editor.add_node(node19, -344.3078040879, 270.05340170504)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "points"

_editor.add_node(node20, -607.8246119414, 18.919905588921)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "points"

_editor.add_node(node21, -480.12611457099, 128.20923691875)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "points"

_editor.add_node(node22, -133.66621686213, -266.71264122959)

var node23 = ::blueprint::nodes::store::Store()

node23.var_name = "count"

_editor.add_node(node23, -74.782423818359, 262.65585214062)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "count"

_editor.add_node(node24, 194.4030258049, -108.56229789052)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "count"

_editor.add_node(node25, -277.18075945567, -176.7910732474)

var node26 = ::blueprint::nodes::store::Store()

node26.var_name = "angle_list"

_editor.add_node(node26, 436.88816484464, 109.36888772946)

var node27 = ::blueprint::nodes::load::Load()

node27.var_name = "angle_list"

_editor.add_node(node27, -622.88867306387, -277.44194844086)

var node28 = ::blueprint::nodes::output::Output()

node28.var_name = "result"
node28.var_type = "array"

_editor.add_node(node28, 474, -96.5)

var node29 = ::blueprint::nodes::input::Input()

node29.var_name = "spr"
node29.var_type = "sprite"

_editor.add_node(node29, -425.4153277667, -213.70042729093)

var node30 = ::blueprint::nodes::input::Input()

node30.var_name = "polyline"
node30.var_type = "geo"

_editor.add_node(node30, -366.22535201461, 401.67290708152)

var node31 = ::blueprint::nodes::input::Input()

node31.var_name = "sample_len"
node31.var_type = "num"

_editor.add_node(node31, -365.01610245909, 337.45459695153)

Blueprint.connect(node30, "var", node0, "polyline")
Blueprint.connect(node31, "var", node0, "length")
Blueprint.connect(node0, "points", node18, "var")
Blueprint.connect(node18, "var", node22, "var")
Blueprint.connect(node22, "var", node17, "items")
Blueprint.connect(node14, "curr_item", node17, "index")
Blueprint.connect(node18, "var", node21, "var")
Blueprint.connect(node21, "var", node5, "items")
Blueprint.connect(node1, "curr_item", node5, "index")
Blueprint.connect(node18, "var", node20, "var")
Blueprint.connect(node18, "var", node19, "var")
Blueprint.connect(node19, "var", node12, "v")
Blueprint.connect(node12, "v", node23, "var")
Blueprint.connect(node23, "var", node25, "var")
Blueprint.connect(node23, "var", node24, "var")
Blueprint.connect(node20, "var", node2, "list")
Blueprint.connect(node3, "v", node2, "offset")
Blueprint.connect(node2, "list", node8, "v")
Blueprint.connect(node2, "list", node6, "items")
Blueprint.connect(node1, "curr_item", node6, "index")
Blueprint.connect(node5, "item", node7, "p0")
Blueprint.connect(node6, "item", node7, "p1")
Blueprint.connect(node8, "v", node1, "count")
Blueprint.connect(node7, "angle", node1, "eval")
Blueprint.connect(node1, "result", node10, "list")
Blueprint.connect(node1, "result", node9, "list")
Blueprint.connect(node10, "back", node9, "add")
Blueprint.connect(node9, "list", node26, "var")
Blueprint.connect(node26, "var", node27, "var")
Blueprint.connect(node27, "var", node13, "items")
Blueprint.connect(node11, "curr_item", node13, "index")
Blueprint.connect(node29, "var", node4, "spr")
Blueprint.connect(node13, "item", node4, "rotate")
Blueprint.connect(node25, "var", node11, "count")
Blueprint.connect(node4, "spr", node11, "eval")
Blueprint.connect(node11, "result", node15, "items")
Blueprint.connect(node14, "curr_item", node15, "index")
Blueprint.connect(node15, "item", node16, "spr")
Blueprint.connect(node17, "item", node16, "translate")
Blueprint.connect(node24, "var", node14, "count")
Blueprint.connect(node16, "spr", node14, "eval")
Blueprint.connect(node14, "result", node28, "var")
