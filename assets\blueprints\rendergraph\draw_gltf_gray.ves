var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node0.skip = false

_editor.add_node(node0, 11.240167232071, -13.523568493438)

var node1 = ::rendergraph::nodes::shader::Shader()

node1.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;
layout (location = 2) in vec2 aTexCoords0;
layout (location = 3) in vec2 aTexCoords1;

out VS_OUT {
	vec3 Normal;
	vec2 TexCoords;
    vec3 frag_pos;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;
	int texcoord;
};

void main()
{
	vs_out.Normal = aNormal;

	if (texcoord == 0) {
    	vs_out.TexCoords = aTexCoords0;
	} else {
    	vs_out.TexCoords = aTexCoords1;	
	}

	vs_out.frag_pos = vec3(model * vec4(aPos, 1.0));
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"
node1.tcs = ""
node1.tes = ""
node1.gs = ""
node1.fs = "
#version 330 core
out vec4 FragColor;

in VS_OUT {
	vec3 Normal;
	vec2 TexCoords;
    vec3 frag_pos;
} fs_in;

uniform UBO
{
	vec3 light_pos;
	vec3 cam_pos;
};

uniform sampler2D diffuse_tex;

void main()
{
	// ambient
	vec3 ambient = vec3(0.25);

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(fs_in.Normal, light_dir), 0.0);
    vec3 diffuse = diff * vec3(1.0);

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(fs_in.Normal, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    FragColor = vec4(ambient + diffuse + specular, 1.0); 

//    FragColor = texture(diffuse_tex, fs_in.TexCoords);
}
"
node1.cs = ""
node1.render_gen()

_editor.add_node(node1, -197.37606979578, -120.5877095474)

var node2 = ::rendergraph::nodes::clear::Clear()

node2.masks = [ "color", "depth" ]
node2.values = { "color" : [ 0.5, 0.5, 0.5, 1 ] }

_editor.add_node(node2, -181.40594233567, 154.09172663351)

var node3 = ::blueprint::nodes::perspective::Perspective()

node3.fovy = 45
node3.aspect = 0
node3.znear = 0.1
node3.zfar = 100

_editor.add_node(node3, -421.69599251269, -42.960075321102)

var node4 = ::blueprint::nodes::proxy::Proxy()

node4.real_name = "view_cam"
node4.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node4, -425.53384713513, 105.15559416076)

var node5 = ::blueprint::nodes::input::Input()

node5.var_name = "va"
node5.var_type = "va"

_editor.add_node(node5, -175.85512834516, -269.41153260327)

var node6 = ::blueprint::nodes::input::Input()

node6.var_name = "diffuse_tex"
node6.var_type = "unknown"

_editor.add_node(node6, -421.63649208833, -324.87800591707)

var node7 = ::blueprint::nodes::input::Input()

node7.var_name = "texcoord"
node7.var_type = "num"

_editor.add_node(node7, -420.79864763336, -255.18144252442)

var node8 = ::blueprint::nodes::number3::Number3()

node8.value.set(5.9766573905945, 5.6433238983154, 5.3099908828735)

_editor.add_node(node8, -422.78990378548, -166.05700607071)

Blueprint.connect(node4, "zoom", node3, "fovy")
Blueprint.connect(node4, "mat", node1, "view")
Blueprint.connect(node3, "mat", node1, "projection")
Blueprint.connect(node7, "var", node1, "texcoord")
Blueprint.connect(node8, "v3", node1, "light_pos")
Blueprint.connect(node4, "pos", node1, "cam_pos")
Blueprint.connect(node6, "var", node1, "diffuse_tex")
Blueprint.connect(node2, "next", node0, "prev")
Blueprint.connect(node1, "out", node0, "shader")
Blueprint.connect(node5, "var", node0, "va")
