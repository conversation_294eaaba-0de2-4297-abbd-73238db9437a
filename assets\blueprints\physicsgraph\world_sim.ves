var node0 = ::physicsgraph::nodes::world::World()

_editor.add_node(node0, -157.81050438782, 280.26598094874)

var node1 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node1, -349.78995702086, -135.33635859588)

var node2 = ::editorgraph::nodes::leave_runtime_mode::LeaveRuntimeMode()

_editor.add_node(node2, -40.658538691585, 110.35096226991)

var node3 = ::physicsgraph::nodes::simulation::Simulation()
node3.query_param("pause").value = false
node3.query_param("debug_draw").value = true

_editor.add_node(node3, -619.49346793846, -138.11168689049)

var node4 = ::rendergraph::nodes::pass::Pass()

node4.once = false

_editor.add_node(node4, -484.23282180776, -42.561446469749)

var node5 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node5, 93.853032962549, 87.00687872384)

var node6 = ::editorgraph::nodes::ticker::Ticker()

_editor.add_node(node6, -481.54250664396, -120.15186004182)

var node7 = ::blueprint::nodes::store::Store()

node7.var_name = "world"

_editor.add_node(node7, -19.10218332472, 281.17767701077)

var node8 = ::blueprint::nodes::load::Load()

node8.var_name = "world"

_editor.add_node(node8, -773.63773209275, -200.62662462022)

var node9 = ::physicsgraph::nodes::update_world::UpdateWorld()

_editor.add_node(node9, -480.04050789126, -177.25074378797)

var node10 = ::blueprint::nodes::clear_cache::ClearCache()

_editor.add_node(node10, -169.41733973493, 75.364860631059)

var node11 = ::blueprint::nodes::input::Input()

node11.var_name = "bodies"
node11.var_type = "array"

_editor.add_node(node11, -292.84317840844, 306.16341099462)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "joints"
node12.var_type = "array"

_editor.add_node(node12, -290.57224334044, 239.02951044024)

var node13 = ::blueprint::nodes::property::Property()

node13.var_name = "draw"
node13.var_type = "bool"

_editor.add_node(node13, -770.51046829224, -142.39850861033)

var node14 = ::blueprint::nodes::load::Load()

node14.var_name = "world"

_editor.add_node(node14, -171.58958833705, 27.411591845375)

var node15 = ::physicsgraph::nodes::simulation::Simulation()
node15.query_param("pause").value = false
node15.query_param("debug_draw").value = false

_editor.add_node(node15, -36.652597847124, 18.388987734174)

var node16 = ::blueprint::nodes::output::Output()

node16.var_name = "world"
node16.var_type = "world"

_editor.add_node(node16, 31.437260450247, -129.18290363918)

var node17 = ::blueprint::nodes::load::Load()

node17.var_name = "world"

_editor.add_node(node17, -104.96861969321, -131.88087698811)

Blueprint.connect(node11, "var", node0, "bodies")
Blueprint.connect(node12, "var", node0, "joints")
Blueprint.connect(node0, "world", node7, "var")
Blueprint.connect(node7, "var", node17, "var")
Blueprint.connect(node17, "var", node16, "var")
Blueprint.connect(node7, "var", node14, "var")
Blueprint.connect(node7, "var", node8, "var")
Blueprint.connect(node8, "var", node3, "world")
Blueprint.connect(node13, "var", node3, "draw")
Blueprint.connect(node3, "next", node9, "prev")
Blueprint.connect(node8, "var", node9, "world")
Blueprint.connect(node3, "next", node4, "prev")
Blueprint.connect(node10, "next", node15, "prev")
Blueprint.connect(node14, "var", node15, "world")
Blueprint.connect(node6, "event", node1, "event")
Blueprint.connect(node9, "next", node1, "action")
Blueprint.connect(node2, "event", node5, "event")
Blueprint.connect(node15, "next", node5, "action")
