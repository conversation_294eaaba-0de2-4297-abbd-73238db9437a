<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Extensions</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Extensions</h1><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Classes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Use NvClothCreateFabricCooker() to create an implemented instance.  <a href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">References all the data required to create a fabric.  <a href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">nv::cloth::ClothFabricPhase</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">References a set of constraints that can be solved in parallel.  <a href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html">nv::cloth::ClothFabricPhaseType</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Describe type of phase in cloth fabric.  <a href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987">nv::cloth::ClothFabricDesc::ClothFabricDesc</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">constructor sets to default.  <a href="#g25a9034d02b0edfaee83e58213288987"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g09aa011d5780d368d58864791f2ff512">nv::cloth::ClothFabricPhase::ClothFabricPhase</a> (ClothFabricPhaseType::Enum type=ClothFabricPhaseType::eINVALID, physx::PxU32 index=0)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gb65c431a270115915e78a73c37489dee">nv::cloth::ClothFabricDesc::isValid</a> () const </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if the descriptor is valid.  <a href="#gb65c431a270115915e78a73c37489dee"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gf7a3e36d6f91e96f595c90a191bdf4a6">NV_CLOTH_API</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a> *) NvClothCreateSimpleTetherCooker()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g852139ee66f6f599c1041ab961286e8c">NV_CLOTH_API</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html">nv::cloth::ClothMeshQuadifier</a> *) NvClothCreateMeshQuadifier()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g927e2eff017f040fb3ed01823e46fc4a">NV_CLOTH_API</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a> *) NvClothCreateFabricCooker()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98">nv::cloth::ClothFabricDesc::setToDefault</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">(re)sets the structure to the default.  <a href="#gc0dd7bb3155e63161744b3fc07132a98"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Variables</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">NV_CLOTH_API(<a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> <br class="typebreak">
*) NvClothCreateFactoryDX11(nv <br class="typebreak">
NV_CLOTH_API(void) <br class="typebreak">
NvClothDestroyFactory(n&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gba25c25fbcf0684a083841a6ddea89d6">NV_CLOTH_API</a> )(bool) NvClothCompiledWithCudaSupport()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if this dll was compiled with cuda support.  <a href="#gba25c25fbcf0684a083841a6ddea89d6"></a><br></td></tr>
</table>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="g25a9034d02b0edfaee83e58213288987"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::ClothFabricDesc" ref="g25a9034d02b0edfaee83e58213288987" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE nv::cloth::ClothFabricDesc::ClothFabricDesc           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inherited]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
constructor sets to default. 
<p>

</div>
</div><p>
<a class="anchor" name="g09aa011d5780d368d58864791f2ff512"></a><!-- doxytag: member="nv::cloth::ClothFabricPhase::ClothFabricPhase" ref="g09aa011d5780d368d58864791f2ff512" args="(ClothFabricPhaseType::Enum type=ClothFabricPhaseType::eINVALID, physx::PxU32 index=0)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE nv::cloth::ClothFabricPhase::ClothFabricPhase           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a>&nbsp;</td>
          <td class="paramname"> <em>type</em> = <code>ClothFabricPhaseType::eINVALID</code>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">physx::PxU32&nbsp;</td>
          <td class="paramname"> <em>index</em> = <code>0</code></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [inherited]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="gb65c431a270115915e78a73c37489dee"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::isValid" ref="gb65c431a270115915e78a73c37489dee" args="() const " -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE bool nv::cloth::ClothFabricDesc::isValid           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inherited]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if the descriptor is valid. 
<p>
<dl class="return" compact><dt><b>Returns:</b></dt><dd>True if the current settings are valid </dd></dl>

</div>
</div><p>
<a class="anchor" name="gf7a3e36d6f91e96f595c90a191bdf4a6"></a><!-- doxytag: member="ClothTetherCooker.h::NV_CLOTH_API" ref="gf7a3e36d6f91e96f595c90a191bdf4a6" args="(nv::cloth::ClothTetherCooker *) NvClothCreateSimpleTetherCooker()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a> *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="g852139ee66f6f599c1041ab961286e8c"></a><!-- doxytag: member="ClothMeshQuadifier.h::NV_CLOTH_API" ref="g852139ee66f6f599c1041ab961286e8c" args="(nv::cloth::ClothMeshQuadifier *) NvClothCreateMeshQuadifier()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html">nv::cloth::ClothMeshQuadifier</a> *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="g927e2eff017f040fb3ed01823e46fc4a"></a><!-- doxytag: member="ClothFabricCooker.h::NV_CLOTH_API" ref="g927e2eff017f040fb3ed01823e46fc4a" args="(nv::cloth::ClothFabricCooker *) NvClothCreateFabricCooker()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a> *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="gc0dd7bb3155e63161744b3fc07132a98"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::setToDefault" ref="gc0dd7bb3155e63161744b3fc07132a98" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE void nv::cloth::ClothFabricDesc::setToDefault           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inherited]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
(re)sets the structure to the default. 
<p>

</div>
</div><p>
<hr><h2>Variable Documentation</h2>
<a class="anchor" name="gba25c25fbcf0684a083841a6ddea89d6"></a><!-- doxytag: member="Factory.h::NV_CLOTH_API" ref="gba25c25fbcf0684a083841a6ddea89d6" args=")(bool) NvClothCompiledWithCudaSupport()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if this dll was compiled with cuda support. 
<p>

</div>
</div><p>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
