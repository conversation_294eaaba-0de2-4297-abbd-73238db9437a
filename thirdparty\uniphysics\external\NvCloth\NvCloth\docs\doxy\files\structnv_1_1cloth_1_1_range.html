<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::Range&lt; T &gt; Struct Template Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Range&lt; T &gt; Struct Template Reference</h1><!-- doxytag: class="nv::cloth::Range" --><code>#include &lt;<a class="el" href="_range_8h-source.html">Range.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_range-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">T &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#6eea0965791c328ef945c3c9ec16637b">back</a> () const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">T *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#45a26e7bbcaffef1a5c22262a86ad145">begin</a> () const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#f44707a5b73331a43e4f03ec08cb7601">empty</a> () const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">T *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#639b15c01cb026a8c6f9689f20ed84c1">end</a> () const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">T &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#5d7d8a09e16cb3e3a0137563571588dc">front</a> () const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">T &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#194cc89be14aa7944b95fd8bf0a948fd">operator[]</a> (uint32_t i) const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#a5b319fd912310273acea0f178560c65">popBack</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#09a3da916a813cc0760cfcf93bb5c907">popFront</a> ()</td></tr>

<tr><td class="memTemplParams" nowrap colspan="2">template&lt;typename S &gt; </td></tr>
<tr><td class="memTemplItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#4c5a521f9b7114617506396667d75a4e">Range</a> (const <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; S &gt; &amp;other)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#cc2a8d8c4dac26809deefca1ef8f68e8">Range</a> (T *begin, T *end)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Construct a range (array like container) using existing memory.  <a href="#cc2a8d8c4dac26809deefca1ef8f68e8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7">Range</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Construct an empty range.  <a href="#9a25cc8511d0d9d8b4147ca7592eebc7"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_range.html#0ef526ff1b8eef5c117ad0e892ab5d24">size</a> () const </td></tr>

</table>
<h3>template&lt;class T&gt;<br>
 struct nv::cloth::Range&lt; T &gt;</h3>

<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="9a25cc8511d0d9d8b4147ca7592eebc7"></a><!-- doxytag: member="nv::cloth::Range::Range" ref="9a25cc8511d0d9d8b4147ca7592eebc7" args="()" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Construct an empty range. 
<p>

</div>
</div><p>
<a class="anchor" name="cc2a8d8c4dac26809deefca1ef8f68e8"></a><!-- doxytag: member="nv::cloth::Range::Range" ref="cc2a8d8c4dac26809deefca1ef8f68e8" args="(T *begin, T *end)" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T&gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>           </td>
          <td>(</td>
          <td class="paramtype">T *&nbsp;</td>
          <td class="paramname"> <em>begin</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T *&nbsp;</td>
          <td class="paramname"> <em>end</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Construct a range (array like container) using existing memory. 
<p>
<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a> doesn't take ownership of this memory. Interface works similar to std::vector. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>begin</em>&nbsp;</td><td>start of the memory </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>end</em>&nbsp;</td><td>end of the memory range, point to one element past the last valid element. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="4c5a521f9b7114617506396667d75a4e"></a><!-- doxytag: member="nv::cloth::Range::Range" ref="4c5a521f9b7114617506396667d75a4e" args="(const Range&lt; S &gt; &amp;other)" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
<div class="memtemplate">
template&lt;typename S &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; S &gt; &amp;&nbsp;</td>
          <td class="paramname"> <em>other</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="6eea0965791c328ef945c3c9ec16637b"></a><!-- doxytag: member="nv::cloth::Range::back" ref="6eea0965791c328ef945c3c9ec16637b" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">T &amp; <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::back           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="45a26e7bbcaffef1a5c22262a86ad145"></a><!-- doxytag: member="nv::cloth::Range::begin" ref="45a26e7bbcaffef1a5c22262a86ad145" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">T * <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::begin           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="f44707a5b73331a43e4f03ec08cb7601"></a><!-- doxytag: member="nv::cloth::Range::empty" ref="f44707a5b73331a43e4f03ec08cb7601" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">bool <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::empty           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="639b15c01cb026a8c6f9689f20ed84c1"></a><!-- doxytag: member="nv::cloth::Range::end" ref="639b15c01cb026a8c6f9689f20ed84c1" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">T * <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::end           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="5d7d8a09e16cb3e3a0137563571588dc"></a><!-- doxytag: member="nv::cloth::Range::front" ref="5d7d8a09e16cb3e3a0137563571588dc" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">T &amp; <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::front           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="194cc89be14aa7944b95fd8bf0a948fd"></a><!-- doxytag: member="nv::cloth::Range::operator[]" ref="194cc89be14aa7944b95fd8bf0a948fd" args="(uint32_t i) const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">T &amp; <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::operator[]           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>i</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="a5b319fd912310273acea0f178560c65"></a><!-- doxytag: member="nv::cloth::Range::popBack" ref="a5b319fd912310273acea0f178560c65" args="()" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::popBack           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="09a3da916a813cc0760cfcf93bb5c907"></a><!-- doxytag: member="nv::cloth::Range::popFront" ref="09a3da916a813cc0760cfcf93bb5c907" args="()" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">void <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::popFront           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="0ef526ff1b8eef5c117ad0e892ab5d24"></a><!-- doxytag: member="nv::cloth::Range::size" ref="0ef526ff1b8eef5c117ad0e892ab5d24" args="() const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range</a>&lt; T &gt;::size           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_range_8h-source.html">Range.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
