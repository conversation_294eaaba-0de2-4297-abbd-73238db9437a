var node0 = ::geograph::nodes::draw_geometry::DrawGeometry()
node0.query_param("skip").value = false

_editor.add_node(node0, 255.16091591346, 14.927740045858)

var node1 = ::geograph::nodes::draw_geometry::DrawGeometry()
node1.query_param("skip").value = false

_editor.add_node(node1, 416.6345110873, 15.14731884447)

var node2 = ::blueprint::nodes::subgraph::Subgraph()
node2.load_from_file(_editor, "translate_selected.ves")

_editor.add_node(node2, 583.96549173718, -282.35988413231)

var node3 = ::blueprint::nodes::subgraph::Subgraph()
node3.load_from_file(_editor, "left_down_select.ves")

_editor.add_node(node3, 217.3439166534, -280.42580375229)

var node4 = ::blueprint::nodes::array::Array()
node4.query_param("serialize").value = false

_editor.add_node(node4, -550.40009151757, -273.83226579859)

var node5 = ::geograph::nodes::draw_geometry::DrawGeometry()
node5.query_param("skip").value = false

_editor.add_node(node5, 723.41383296737, 16.62198413537)

var node6 = ::blueprint::nodes::number3::Number3()

node6.value.set(0, 0, 0.70140242576599)

_editor.add_node(node6, 563.72476701163, -82.33834415123)

var node7 = ::blueprint::nodes::merge::Merge()

_editor.add_node(node7, -472.42662235993, -13.486681884918)

var node8 = ::geograph::nodes::a_a_b_b::AABB()

_editor.add_node(node8, 105.5791852324, 26.106245391235)

var node9 = ::blueprint::nodes::input::Input()

node9.var_name = "min"
node9.var_type = "num2"

_editor.add_node(node9, -617.09110186491, 21.32293560773)

var node10 = ::blueprint::nodes::input::Input()

node10.var_name = "max"
node10.var_type = "num2"

_editor.add_node(node10, -617.64515937225, -42.40184376281)

var node11 = ::blueprint::nodes::store::Store()

node11.var_name = "selected"

_editor.add_node(node11, -405.50445335612, -271.81859378256)

var node12 = ::blueprint::nodes::load::Load()

node12.var_name = "selected"

_editor.add_node(node12, 30.73572352844, -309.13035958374)

var node13 = ::blueprint::nodes::load::Load()

node13.var_name = "selected"

_editor.add_node(node13, 404.58995831129, -282.15817097951)

var node14 = ::blueprint::nodes::load::Load()

node14.var_name = "selected"

_editor.add_node(node14, 560.07081252054, 0.59010797581999)

var node15 = ::blueprint::nodes::store::Store()

node15.var_name = "points"

_editor.add_node(node15, -320.68178607302, 10.57739964004)

var node16 = ::blueprint::nodes::load::Load()

node16.var_name = "points"

_editor.add_node(node16, -38.051653729445, 25.285084759535)

var node17 = ::blueprint::nodes::load::Load()

node17.var_name = "points"

_editor.add_node(node17, 261.72295161247, -66.375275323124)

var node18 = ::blueprint::nodes::load::Load()

node18.var_name = "points"

_editor.add_node(node18, 27.706118774414, -257.55153808594)

var node19 = ::blueprint::nodes::input::Input()

node19.var_name = "cam_mat"
node19.var_type = "mat4"

_editor.add_node(node19, -548.02519780939, -360.65480735085)

var node20 = ::blueprint::nodes::store::Store()

node20.var_name = "cam_mat"

_editor.add_node(node20, -416.20701460405, -359.74571921609)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "cam_mat"

_editor.add_node(node21, 105.61118247292, -20.654806657271)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "cam_mat"

_editor.add_node(node22, 268.13879170722, -112.77311623438)

var node23 = ::blueprint::nodes::load::Load()

node23.var_name = "cam_mat"

_editor.add_node(node23, 566.20535047042, -156.20892330971)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "cam_mat"

_editor.add_node(node24, 30.945846811226, -355.36092976192)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "cam_mat"

_editor.add_node(node25, 404.85493772032, -326.91944215861)

Blueprint.connect(node19, "var", node20, "var")
Blueprint.connect(node20, "var", node25, "var")
Blueprint.connect(node20, "var", node24, "var")
Blueprint.connect(node20, "var", node23, "var")
Blueprint.connect(node20, "var", node22, "var")
Blueprint.connect(node20, "var", node21, "var")
Blueprint.connect(node9, "var", node7, "in0")
Blueprint.connect(node10, "var", node7, "in1")
Blueprint.connect(node7, "list", node15, "var")
Blueprint.connect(node15, "var", node18, "var")
Blueprint.connect(node15, "var", node17, "var")
Blueprint.connect(node15, "var", node16, "var")
Blueprint.connect(node16, "var", node8, "vertices")
Blueprint.connect(node8, "rect", node0, "geos")
Blueprint.connect(node21, "var", node0, "cam_mat")
Blueprint.connect(node0, "next", node1, "prev")
Blueprint.connect(node17, "var", node1, "geos")
Blueprint.connect(node22, "var", node1, "cam_mat")
Blueprint.connect(node4, "all", node11, "var")
Blueprint.connect(node11, "var", node14, "var")
Blueprint.connect(node1, "next", node5, "prev")
Blueprint.connect(node14, "var", node5, "geos")
Blueprint.connect(node6, "v3", node5, "color")
Blueprint.connect(node23, "var", node5, "cam_mat")
Blueprint.connect(node11, "var", node13, "var")
Blueprint.connect(node11, "var", node12, "var")
Blueprint.connect(node18, "var", node3, "geos")
Blueprint.connect(node12, "var", node3, "selected")
Blueprint.connect(node24, "var", node3, "cam_mat")
Blueprint.connect(node3, "next", node2, "prev")
Blueprint.connect(node13, "var", node2, "selected")
Blueprint.connect(node25, "var", node2, "cam_mat")
