var node0 = ::editorgraph::nodes::mouse_left_down::<PERSON><PERSON><PERSON><PERSON>D<PERSON>()

_editor.add_node(node0, -940.36761134036, 80.1624449563)

var node1 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node1, 319.94583723826, 185.57505958663)

var node2 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node2, -662.8500770852, -72.087512613281)

var node3 = ::geograph::nodes::is_contain::IsContain()

_editor.add_node(node3, -522.74533965898, -62.212771804063)

var node4 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node4, -52.159619762, 50.697039060878)

var node5 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node5, -350.96318799891, -73.836388609248)

var node6 = ::blueprint::nodes::list_add::ListAdd()
node6.query_param("unique").value = false

_editor.add_node(node6, -518.39803797214, -149.25177666562)

var node7 = ::blueprint::nodes::array::Array()
node7.query_param("serialize").value = false

_editor.add_node(node7, -1194.0238265934, -251.0785006746)

var node8 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node8, -295.83592588503, -228.82517940051)

var node9 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node9, 159.21287956939, -18.56937491875)

var node10 = ::blueprint::nodes::list_add::ListAdd()
node10.query_param("unique").value = true

_editor.add_node(node10, -274.36971907509, -380.46677580693)

var node11 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node11, -254.77273421375, 63.324398284904)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "geos"
node12.var_type = "array"

_editor.add_node(node12, -256.42593782948, 9.6764526064942)

var node13 = ::blueprint::nodes::input::Input()

node13.var_name = "selected"
node13.var_type = "array"

_editor.add_node(node13, -1192.8693056435, -325.30864557082)

var node14 = ::blueprint::nodes::store::Store()

node14.var_name = "tmp_list"

_editor.add_node(node14, -1053.8436834162, -251.08341702548)

var node15 = ::blueprint::nodes::load::Load()

node15.var_name = "tmp_list"

_editor.add_node(node15, -394.34242155883, 51.999084064115)

var node16 = ::blueprint::nodes::load::Load()

node16.var_name = "tmp_list"

_editor.add_node(node16, -660.48480040349, -141.21857706532)

var node17 = ::blueprint::nodes::load::Load()

node17.var_name = "tmp_list"

_editor.add_node(node17, -414.08121990801, -435.36113425101)

var node18 = ::blueprint::nodes::load::Load()

node18.var_name = "tmp_list"

_editor.add_node(node18, -201.03261041573, -65.681926097027)

var node19 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node19, -705.50327962607, -443.3580662648)

var node20 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node20, -550.92278748833, -377.19031577671)

var node21 = ::editorgraph::nodes::is_key_pressed::IsKeyPressed()

node21.key = "ctrl"

_editor.add_node(node21, -702.44641740079, -378.07569854342)

var node22 = ::blueprint::nodes::store::Store()

node22.var_name = "selection"

_editor.add_node(node22, -1058.5935618962, -334.91304670717)

var node23 = ::blueprint::nodes::load::Load()

node23.var_name = "selection"

_editor.add_node(node23, -435.88357472867, -239.19476946596)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "selection"

_editor.add_node(node24, -837.77623152089, -454.8825653497)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "selection"

_editor.add_node(node25, -416.28867920766, -390.26950276698)

var node26 = ::blueprint::nodes::boolean::Boolean()

node26.value = false

_editor.add_node(node26, -1199.1238014915, -425.11101185192)

var node27 = ::blueprint::nodes::store::Store()

node27.var_name = "success"

_editor.add_node(node27, -1060.1069572937, -455.0153762772)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "success"

_editor.add_node(node28, -160.7197075095, -236.53779291434)

var node29 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node29, -30.79321295941, -201.9198278804)

var node30 = ::blueprint::nodes::boolean::Boolean()

node30.value = false

_editor.add_node(node30, -164.52726242329, -181.63431807589)

var node31 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node31, 6.3407178712825, -368.2662490661)

var node32 = ::blueprint::nodes::load::Load()

node32.var_name = "success"

_editor.add_node(node32, -130.39295334733, -452.02563214536)

var node33 = ::blueprint::nodes::boolean::Boolean()

node33.value = true

_editor.add_node(node33, -129.77200312967, -396.14271296505)

var node34 = ::blueprint::nodes::output::Output()

node34.var_name = "success"
node34.var_type = "bool"

_editor.add_node(node34, -1059.3107543436, -398.58994459118)

var node35 = ::blueprint::nodes::is_empty::IsEmpty()

_editor.add_node(node35, -49.564910888672, -57.045883178711)

var node36 = ::blueprint::nodes::input::Input()

node36.var_name = "cam_mat"
node36.var_type = "mat4"

_editor.add_node(node36, -1198.1514892578, -538.16296386719)

var node37 = ::blueprint::nodes::store::Store()

node37.var_name = "cam_mat"

_editor.add_node(node37, -1060.1514892578, -537.16296386719)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "cam_mat"

_editor.add_node(node38, -801.15148925781, -88.162963867188)

Blueprint.connect(node36, "var", node37, "var")
Blueprint.connect(node37, "var", node38, "var")
Blueprint.connect(node26, "v", node34, "var")
Blueprint.connect(node26, "v", node27, "var")
Blueprint.connect(node27, "var", node32, "var")
Blueprint.connect(node27, "var", node28, "var")
Blueprint.connect(node21, "pressed", node20, "cond")
Blueprint.connect(node19, "next", node20, "false")
Blueprint.connect(node13, "var", node22, "var")
Blueprint.connect(node22, "var", node25, "var")
Blueprint.connect(node22, "var", node24, "var")
Blueprint.connect(node24, "var", node19, "list")
Blueprint.connect(node22, "var", node23, "var")
Blueprint.connect(node23, "var", node8, "list")
Blueprint.connect(node8, "next", node29, "prev")
Blueprint.connect(node30, "v", node29, "src")
Blueprint.connect(node28, "var", node29, "dst")
Blueprint.connect(node7, "all", node14, "var")
Blueprint.connect(node14, "var", node18, "var")
Blueprint.connect(node18, "var", node35, "items")
Blueprint.connect(node14, "var", node17, "var")
Blueprint.connect(node20, "next", node10, "prev")
Blueprint.connect(node25, "var", node10, "list")
Blueprint.connect(node17, "var", node10, "add")
Blueprint.connect(node10, "next", node31, "prev")
Blueprint.connect(node33, "v", node31, "src")
Blueprint.connect(node32, "var", node31, "dst")
Blueprint.connect(node14, "var", node16, "var")
Blueprint.connect(node16, "var", node6, "list")
Blueprint.connect(node4, "out", node6, "add")
Blueprint.connect(node14, "var", node15, "var")
Blueprint.connect(node15, "var", node11, "list")
Blueprint.connect(node11, "next", node4, "prev")
Blueprint.connect(node12, "var", node4, "in")
Blueprint.connect(node5, "next", node4, "do")
Blueprint.connect(node4, "next", node9, "prev")
Blueprint.connect(node35, "empty", node9, "cond")
Blueprint.connect(node29, "next", node9, "true")
Blueprint.connect(node31, "next", node9, "false")
Blueprint.connect(node0, "pos", node2, "screen")
Blueprint.connect(node36, "var", node2, "cam_mat")
Blueprint.connect(node4, "out", node3, "geo")
Blueprint.connect(node2, "world", node3, "pos")
Blueprint.connect(node3, "out", node5, "cond")
Blueprint.connect(node6, "next", node5, "true")
Blueprint.connect(node0, "event", node1, "event")
Blueprint.connect(node9, "next", node1, "action")
