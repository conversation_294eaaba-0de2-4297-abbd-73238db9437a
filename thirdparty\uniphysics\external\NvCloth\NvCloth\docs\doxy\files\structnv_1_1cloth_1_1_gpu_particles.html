<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::GpuParticles Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html">GpuParticles</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::GpuParticles Struct Reference</h1><!-- doxytag: class="nv::cloth::GpuParticles" --><code>#include &lt;<a class="el" href="_cloth_8h-source.html">Cloth.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_gpu_particles-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">ID3D11Buffer *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">mBuffer</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxVec4 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">mCurrent</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxVec4 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">mPrevious</a></td></tr>

</table>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="c51319ddd95590ff62430e3f74c1ecc2"></a><!-- doxytag: member="nv::cloth::GpuParticles::mBuffer" ref="c51319ddd95590ff62430e3f74c1ecc2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">ID3D11Buffer* <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">nv::cloth::GpuParticles::mBuffer</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="880bde551348e2ee87e3b94ffceafd71"></a><!-- doxytag: member="nv::cloth::GpuParticles::mCurrent" ref="880bde551348e2ee87e3b94ffceafd71" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxVec4* <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">nv::cloth::GpuParticles::mCurrent</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="fc1d82619147076f4b9c0e8d9da93a69"></a><!-- doxytag: member="nv::cloth::GpuParticles::mPrevious" ref="fc1d82619147076f4b9c0e8d9da93a69" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxVec4* <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">nv::cloth::GpuParticles::mPrevious</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_8h-source.html">Cloth.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
