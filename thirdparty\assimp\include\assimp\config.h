#pragma once

// Basic Assimp configuration for <PERSON><PERSON> build
// This is a minimal config.h to resolve compilation issues

// Configuration constants
#define AI_CONFIG_GLOBAL_SCALE_FACTOR_KEY "GLOBAL_SCALE_FACTOR"
#define AI_CONFIG_APP_SCALE_KEY "APP_SCALE_KEY"

#define ASSIMP_BUILD_NO_EXPORT
#define ASSIMP_BUILD_NO_3DS_IMPORTER
#define ASSIMP_BUILD_NO_AC_IMPORTER
#define ASSIMP_BUILD_NO_ASE_IMPORTER
#define ASSIMP_BUILD_NO_ASSBIN_IMPORTER
#define ASSIMP_BUILD_NO_ASSXML_IMPORTER
#define ASSIMP_BUILD_NO_B3D_IMPORTER
#define ASSIMP_BUILD_NO_BVH_IMPORTER
#define ASSIMP_BUILD_NO_COLLADA_IMPORTER
#define ASSIMP_BUILD_NO_DXF_IMPORTER
#define ASSIMP_BUILD_NO_CSM_IMPORTER
#define ASSIMP_BUILD_NO_HMP_IMPORTER
#define ASSIMP_BUILD_NO_IRRMESH_IMPORTER
#define ASSIMP_BUILD_NO_IRR_IMPORTER
#define ASSIMP_BUILD_NO_LWO_IMPORTER
#define ASSIMP_BUILD_NO_LWS_IMPORTER
#define ASSIMP_BUILD_NO_MD2_IMPORTER
#define ASSIMP_BUILD_NO_MD3_IMPORTER
#define ASSIMP_BUILD_NO_MD5_IMPORTER
#define ASSIMP_BUILD_NO_MDC_IMPORTER
#define ASSIMP_BUILD_NO_MDL_IMPORTER
#define ASSIMP_BUILD_NO_NFF_IMPORTER
#define ASSIMP_BUILD_NO_NDO_IMPORTER
#define ASSIMP_BUILD_NO_OFF_IMPORTER
#define ASSIMP_BUILD_NO_Q3D_IMPORTER
#define ASSIMP_BUILD_NO_Q3BSP_IMPORTER
#define ASSIMP_BUILD_NO_RAW_IMPORTER
#define ASSIMP_BUILD_NO_SIB_IMPORTER
#define ASSIMP_BUILD_NO_SMD_IMPORTER
#define ASSIMP_BUILD_NO_STL_IMPORTER
#define ASSIMP_BUILD_NO_TERRAGEN_IMPORTER
#define ASSIMP_BUILD_NO_3D_IMPORTER
#define ASSIMP_BUILD_NO_X_IMPORTER
#define ASSIMP_BUILD_NO_X3D_IMPORTER
#define ASSIMP_BUILD_NO_MS3D_IMPORTER
#define ASSIMP_BUILD_NO_COB_IMPORTER
#define ASSIMP_BUILD_NO_BLEND_IMPORTER
#define ASSIMP_BUILD_NO_Q3D_IMPORTER
#define ASSIMP_BUILD_NO_NDO_IMPORTER
#define ASSIMP_BUILD_NO_IFC_IMPORTER
#define ASSIMP_BUILD_NO_XGL_IMPORTER
#define ASSIMP_BUILD_NO_FBX_IMPORTER
#define ASSIMP_BUILD_NO_ASSBIN_IMPORTER
#define ASSIMP_BUILD_NO_GLTF_IMPORTER
#define ASSIMP_BUILD_NO_C4D_IMPORTER
#define ASSIMP_BUILD_NO_3MF_IMPORTER
#define ASSIMP_BUILD_NO_X3D_IMPORTER
#define ASSIMP_BUILD_NO_MMD_IMPORTER

// Keep essential importers
#undef ASSIMP_BUILD_NO_OBJ_IMPORTER
#undef ASSIMP_BUILD_NO_PLY_IMPORTER
#undef ASSIMP_BUILD_NO_FBX_IMPORTER
#undef ASSIMP_BUILD_NO_GLTF_IMPORTER

// Version info
#define ASSIMP_VERSION_MAJOR 5
#define ASSIMP_VERSION_MINOR 0
#define ASSIMP_VERSION_PATCH 1
#define ASSIMP_VERSION_REVISION 0

// Platform specific
#ifdef _WIN32
#define ASSIMP_API __declspec(dllexport)
#else
#define ASSIMP_API
#endif

// Compiler specific
#ifdef _MSC_VER
#pragma warning(disable: 4251)
#pragma warning(disable: 4275)
#endif
