<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::PhaseConfig Member List</h1>This is the complete list of members for <a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">mCompressionLimit</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">mPadding</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">mPhaseIndex</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">mStiffness</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">mStiffnessMultiplier</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">mStretchLimit</a></td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#44574dd7cc0f67ec460bb0c9bdeb0819">PhaseConfig</a>(uint16_t index=uint16_t(-1))</td><td><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td><code> [inline]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
