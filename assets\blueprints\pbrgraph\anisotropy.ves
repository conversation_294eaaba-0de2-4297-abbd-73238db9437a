var node0 = ::blueprint::nodes::combine::Combine()

_editor.add_node(node0, 1553.9590427172, -33.004772444539)

var node1 = ::pbrgraph::nodes::gamma_correction::GammaCorrection()

_editor.add_node(node1, 1379.6954114563, -51.121574622679)

var node2 = ::blueprint::nodes::add::Add()

_editor.add_node(node2, 979.5602209247, -28.190523787289)

var node3 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node3, 836.56258047349, -3.4492614836592)

var node4 = ::blueprint::nodes::commentary::Commentary()

node4.set_size(1222.9333496094, 201.2188873291)
node4.title = "Layers"

_editor.add_node(node4, 1169.4719153622, 84.098389461659)

var node5 = ::pbrgraph::nodes::d__g_g_x__anisotropic::D_GGX_Anisotropic()

_editor.add_node(node5, -15.169216549961, 484.0289893506)

var node6 = ::pbrgraph::nodes::v__smith_g_g_x_correlated__anisotropic::V_SmithGGXCorrelated_Anisotropic()

_editor.add_node(node6, -16.255018813201, 248.3332851337)

var node7 = ::pbrgraph::nodes::f__schlick::F_Schlick()
node7.query_param("f0").value.set(0.04, 0.04, 0.04)
node7.query_param("f90").value = 1

_editor.add_node(node7, -9.398681016851, 81.39174266614)

var node8 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node8, 139.14554044649, 382.1372186234)

var node9 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node9, 282.61390547471, 335.5375783405)

var node10 = ::shadergraph::nodes::normal::Normal()

_editor.add_node(node10, -1215.7717240557, 599.8224422164)

var node11 = ::shadergraph::nodes::world_pos::WorldPos()

_editor.add_node(node11, -1217.4479938616, 649.33320950373)

var node12 = ::blueprint::nodes::commentary::Commentary()

node12.set_size(823.55413818359, 803.66668701172)
node12.title = "Dirs"

_editor.add_node(node12, -913.28149153415, 737.327272547)

var node13 = ::shadergraph::nodes::uniform::Uniform()
node13.query_param("unif_name").value = "u_light_pos"

_editor.add_node(node13, -1064.5334199794, 417.18899056881)

var node14 = ::shadergraph::nodes::uniform::Uniform()
node14.query_param("unif_name").value = "u_cam_pos"

_editor.add_node(node14, -1066.6526173406, 465.70422633591)

var node15 = ::blueprint::nodes::subgraph::Subgraph()
node15.load_from_file(_editor, "lighting_dirs.ves")

_editor.add_node(node15, -794.07786814402, 544.36169639107)

var node16 = ::blueprint::nodes::subgraph::Subgraph()
node16.load_from_file(_editor, "../shadergraph/calc_normal.ves")

_editor.add_node(node16, -1026.1727957996, 576.60961764659)

var node17 = ::blueprint::nodes::subgraph::Subgraph()
node17.load_from_file(_editor, "surface_output.ves")

_editor.add_node(node17, -807.8349466827, -332.81371685223)

var node18 = ::blueprint::nodes::store::Store()

node18.var_name = "NoV"

_editor.add_node(node18, -611.7601784293, 680.3131591694)

var node19 = ::blueprint::nodes::store::Store()

node19.var_name = "NoH"

_editor.add_node(node19, -608.84006462614, 593.81481750946)

var node20 = ::blueprint::nodes::store::Store()

node20.var_name = "NoL"

_editor.add_node(node20, -610.54781235878, 636.17605708827)

var node21 = ::blueprint::nodes::store::Store()

node21.var_name = "VoH"

_editor.add_node(node21, -608.21233458319, 550.71577555933)

var node22 = ::blueprint::nodes::store::Store()

node22.var_name = "roughness"

_editor.add_node(node22, -616.65757996177, -285.08042536076)

var node23 = ::blueprint::nodes::store::Store()

node23.var_name = "metallic"

_editor.add_node(node23, -615.30051807797, -327.52981792232)

var node24 = ::blueprint::nodes::store::Store()

node24.var_name = "occlusion"

_editor.add_node(node24, -614.72827449358, -370.34001535446)

var node25 = ::blueprint::nodes::store::Store()

node25.var_name = "emission"

_editor.add_node(node25, -614.59449856437, -456.07756068135)

var node26 = ::blueprint::nodes::store::Store()

node26.var_name = "albedo"

_editor.add_node(node26, -616.89429393477, -414.26785151977)

var node27 = ::blueprint::nodes::store::Store()

node27.var_name = "normal"

_editor.add_node(node27, -615.78508526127, -203.04590461568)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "normal"

_editor.add_node(node28, -1214.9986278038, 513.67456263864)

var node29 = ::blueprint::nodes::store::Store()

node29.var_name = "normal_uv"

_editor.add_node(node29, -613.88941650777, -244.57521819578)

var node30 = ::blueprint::nodes::load::Load()

node30.var_name = "normal_uv"

_editor.add_node(node30, -1214.9954864826, 556.98288503362)

var node31 = ::blueprint::nodes::commentary::Commentary()

node31.set_size(590.51251220703, 330.57708740234)
node31.title = "Surface"

_editor.add_node(node31, -802.6848848234, -147.32670461922)

var node32 = ::blueprint::nodes::load::Load()

node32.var_name = "occlusion"

_editor.add_node(node32, 694.4054801111, -15.933027446939)

var node33 = ::blueprint::nodes::load::Load()

node33.var_name = "emission"

_editor.add_node(node33, 844.80986846588, -61.142357199379)

var node34 = ::blueprint::nodes::number::Number()

node34.value = 1.0141940116882

_editor.add_node(node34, 1385.0222856863, 4.4425979329714)

var node35 = ::blueprint::nodes::store::Store()

node35.var_name = "V"

_editor.add_node(node35, -604.54290180936, 445.69556820517)

var node36 = ::blueprint::nodes::load::Load()

node36.var_name = "V"

_editor.add_node(node36, -989.60109704033, 118.0307834368)

var node37 = ::blueprint::nodes::store::Store()

node37.var_name = "L"

_editor.add_node(node37, -602.00966490694, 400.16242704362)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "L"

_editor.add_node(node38, -987.38156676823, 69.828714925398)

var node39 = ::blueprint::nodes::load::Load()

node39.var_name = "NoV"

_editor.add_node(node39, -171.37938174436, 214.9345898017)

var node40 = ::blueprint::nodes::load::Load()

node40.var_name = "NoL"

_editor.add_node(node40, -170.55291957406, 168.65360664129)

var node41 = ::blueprint::nodes::store::Store()

node41.var_name = "H"

_editor.add_node(node41, -603.48520401275, 353.55306996998)

var node42 = ::blueprint::nodes::load::Load()

node42.var_name = "NoH"

_editor.add_node(node42, -183.13086047695, 379.17864481839)

var node43 = ::blueprint::nodes::load::Load()

node43.var_name = "VoH"

_editor.add_node(node43, -167.48924963389, 94.47425901633)

var node44 = ::shadergraph::nodes::mix::Mix()

_editor.add_node(node44, -168.22532800319, 15.007876281155)

var node45 = ::blueprint::nodes::number3::Number3()

node45.value.set(0, 0, 0)

_editor.add_node(node45, -315.79105042328, 73.65960216203)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "albedo"

_editor.add_node(node46, -310.66627799739, -6.116474709124)

var node47 = ::blueprint::nodes::load::Load()

node47.var_name = "metallic"

_editor.add_node(node47, -310.05464161104, -53.892134324305)

var node48 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node48, 427.1818918113, 313.98743879406)

var node49 = ::blueprint::nodes::load::Load()

node49.var_name = "NoL"

_editor.add_node(node49, 283.5389403252, 274.97816710646)

var node50 = ::blueprint::nodes::store::Store()

node50.var_name = "N"

_editor.add_node(node50, -606.70992123378, 490.26779714145)

var node51 = ::blueprint::nodes::subgraph::Subgraph()
node51.load_from_file(_editor, "anisotropic_dirs.ves")

_editor.add_node(node51, -790.04088615677, 169.2816641996)

var node52 = ::shadergraph::nodes::uniform::Uniform()
node52.query_param("unif_name").value = "anisotropy_dir"

_editor.add_node(node52, -986.07432899375, 219.22132254425)

var node53 = ::shadergraph::nodes::uniform::Uniform()
node53.query_param("unif_name").value = "anisotropy"

_editor.add_node(node53, -986.09265189622, 268.18223936438)

var node54 = ::blueprint::nodes::store::Store()

node54.var_name = "at"

_editor.add_node(node54, -606.02603418975, 275.1313438742)

var node55 = ::blueprint::nodes::store::Store()

node55.var_name = "ab"

_editor.add_node(node55, -607.22736030121, 229.0760974069)

var node56 = ::blueprint::nodes::load::Load()

node56.var_name = "at"

_editor.add_node(node56, -184.54659667087, 551.4426043465)

var node57 = ::blueprint::nodes::load::Load()

node57.var_name = "ab"

_editor.add_node(node57, -184.8869494008, 505.4592112163)

var node58 = ::blueprint::nodes::load::Load()

node58.var_name = "at"

_editor.add_node(node58, -180.58037518021, 311.9383052633)

var node59 = ::blueprint::nodes::load::Load()

node59.var_name = "ab"

_editor.add_node(node59, -176.4751118285, 266.2671626824)

var node60 = ::blueprint::nodes::load::Load()

node60.var_name = "roughness"

_editor.add_node(node60, -986.11804748138, 23.581669815598)

var node61 = ::blueprint::nodes::store::Store()

node61.var_name = "ToH"

_editor.add_node(node61, -603.62236957223, 109.9528754289)

var node62 = ::blueprint::nodes::store::Store()

node62.var_name = "BoH"

_editor.add_node(node62, -607.88297816805, 64.609667521698)

var node63 = ::blueprint::nodes::load::Load()

node63.var_name = "ToH"

_editor.add_node(node63, -182.36605885438, 463.3429836629)

var node64 = ::blueprint::nodes::load::Load()

node64.var_name = "BoH"

_editor.add_node(node64, -182.36607162545, 420.51801717489)

var node65 = ::blueprint::nodes::load::Load()

node65.var_name = "H"

_editor.add_node(node65, -987.0970744961, -26.323734240732)

var node66 = ::blueprint::nodes::load::Load()

node66.var_name = "N"

_editor.add_node(node66, -984.64226391492, 164.7034443719)

var node67 = ::blueprint::nodes::input::Input()

node67.var_name = "model"
node67.var_type = [ "table", "array" ]

_editor.add_node(node67, -1014.8451608922, -221.12950286099)

var node68 = ::blueprint::nodes::output::Output()

node68.var_name = "frag_out"
node68.var_type = "num4"

_editor.add_node(node68, 1697.7141249213, 21.730092187171)

var node69 = ::blueprint::nodes::input::Input()

node69.var_name = "cam_pos"
node69.var_type = "num3"

_editor.add_node(node69, -1212.9001149232, 453.84423985076)

var node70 = ::blueprint::nodes::input::Input()

node70.var_name = "light_pos"
node70.var_type = "vec3"

_editor.add_node(node70, -1210.7378653918, 405.81859422664)

var node71 = ::blueprint::nodes::input::Input()

node71.var_name = "anisotropic"
node71.var_type = "num"

_editor.add_node(node71, -1138.8422948572, 267.4972525146)

var node72 = ::blueprint::nodes::input::Input()

node72.var_name = "anisotropic_dir"
node72.var_type = "num3"

_editor.add_node(node72, -1137.1489174372, 221.21038298274)

var node73 = ::blueprint::nodes::commentary::Commentary()

node73.set_size(977.91925048828, 670.71929931641)
node73.title = "BRDF"

_editor.add_node(node73, 36.950022558589, 608.6844046094)

var node74 = ::blueprint::nodes::subgraph::Subgraph()
node74.load_from_file(_editor, "ibl_brdf.ves")

_editor.add_node(node74, -46.288723067621, -331.01755904266)

var node75 = ::blueprint::nodes::load::Load()

node75.var_name = "albedo"

_editor.add_node(node75, -264.90205539145, -204.17074926847)

var node76 = ::blueprint::nodes::load::Load()

node76.var_name = "metallic"

_editor.add_node(node76, -265.90930848184, -245.10890722859)

var node77 = ::blueprint::nodes::load::Load()

node77.var_name = "roughness"

_editor.add_node(node77, -263.84969378961, -288.69197916948)

var node78 = ::blueprint::nodes::load::Load()

node78.var_name = "occlusion"

_editor.add_node(node78, -263.86362702459, -329.33721161281)

var node79 = ::blueprint::nodes::load::Load()

node79.var_name = "N"

_editor.add_node(node79, -263.38401871999, -369.97715806635)

var node80 = ::blueprint::nodes::load::Load()

node80.var_name = "V"

_editor.add_node(node80, -262.79492933828, -412.19981262775)

var node81 = ::blueprint::nodes::load::Load()

node81.var_name = "F"

_editor.add_node(node81, -262.48767095875, -456.55318784491)

var node82 = ::rendergraph::nodes::pass::Pass()

node82.once = true

_editor.add_node(node82, 168.87509049951, -233.51735528245)

var node83 = ::blueprint::nodes::commentary::Commentary()

node83.set_size(624.99163818359, 357.49688720703)
node83.title = "IBL"

_editor.add_node(node83, -52.663529157231, -145.46956124609)

var node84 = ::blueprint::nodes::store::Store()

node84.var_name = "F"

_editor.add_node(node84, 142.75682330973, 80.287353469768)

var node85 = ::blueprint::nodes::add::Add()

_editor.add_node(node85, 1147.8088742091, -50.592745126772)

Blueprint.connect(node74, "next", node82, "do")
Blueprint.connect(node72, "var", node52, "v")
Blueprint.connect(node71, "var", node53, "v")
Blueprint.connect(node70, "var", node13, "v")
Blueprint.connect(node69, "var", node14, "v")
Blueprint.connect(node67, "var", node17, "model")
Blueprint.connect(node17, "normal_uv", node29, "var")
Blueprint.connect(node29, "var", node30, "var")
Blueprint.connect(node17, "normal", node27, "var")
Blueprint.connect(node27, "var", node28, "var")
Blueprint.connect(node17, "albedo", node26, "var")
Blueprint.connect(node26, "var", node75, "var")
Blueprint.connect(node26, "var", node46, "var")
Blueprint.connect(node17, "emission", node25, "var")
Blueprint.connect(node25, "var", node33, "var")
Blueprint.connect(node17, "occlusion", node24, "var")
Blueprint.connect(node24, "var", node78, "var")
Blueprint.connect(node24, "var", node32, "var")
Blueprint.connect(node17, "metallic", node23, "var")
Blueprint.connect(node23, "var", node76, "var")
Blueprint.connect(node23, "var", node47, "var")
Blueprint.connect(node17, "roughness", node22, "var")
Blueprint.connect(node22, "var", node77, "var")
Blueprint.connect(node22, "var", node60, "var")
Blueprint.connect(node45, "v3", node44, "x")
Blueprint.connect(node46, "var", node44, "y")
Blueprint.connect(node47, "var", node44, "a")
Blueprint.connect(node11, "pos", node16, "world_pos")
Blueprint.connect(node10, "normal", node16, "normal")
Blueprint.connect(node30, "var", node16, "tex_coords")
Blueprint.connect(node28, "var", node16, "normal_map")
Blueprint.connect(node16, "normal", node15, "normal")
Blueprint.connect(node11, "pos", node15, "world_pos")
Blueprint.connect(node14, "v", node15, "cam_pos")
Blueprint.connect(node13, "v", node15, "light_pos")
Blueprint.connect(node15, "N", node50, "var")
Blueprint.connect(node50, "var", node79, "var")
Blueprint.connect(node50, "var", node66, "var")
Blueprint.connect(node15, "H", node41, "var")
Blueprint.connect(node41, "var", node65, "var")
Blueprint.connect(node15, "L", node37, "var")
Blueprint.connect(node37, "var", node38, "var")
Blueprint.connect(node15, "V", node35, "var")
Blueprint.connect(node35, "var", node80, "var")
Blueprint.connect(node35, "var", node36, "var")
Blueprint.connect(node53, "v", node51, "anisotropy")
Blueprint.connect(node52, "v", node51, "anisotropy_dir")
Blueprint.connect(node11, "pos", node51, "world_pos")
Blueprint.connect(node10, "normal", node51, "normal")
Blueprint.connect(node30, "var", node51, "tex_coords")
Blueprint.connect(node66, "var", node51, "N")
Blueprint.connect(node36, "var", node51, "V")
Blueprint.connect(node38, "var", node51, "L")
Blueprint.connect(node60, "var", node51, "roughness")
Blueprint.connect(node65, "var", node51, "H")
Blueprint.connect(node51, "BoH", node62, "var")
Blueprint.connect(node62, "var", node64, "var")
Blueprint.connect(node51, "ToH", node61, "var")
Blueprint.connect(node61, "var", node63, "var")
Blueprint.connect(node51, "ab", node55, "var")
Blueprint.connect(node55, "var", node59, "var")
Blueprint.connect(node55, "var", node57, "var")
Blueprint.connect(node51, "at", node54, "var")
Blueprint.connect(node54, "var", node58, "var")
Blueprint.connect(node54, "var", node56, "var")
Blueprint.connect(node15, "VoH", node21, "var")
Blueprint.connect(node21, "var", node43, "var")
Blueprint.connect(node44, "mix", node7, "f0")
Blueprint.connect(node43, "var", node7, "VoH")
Blueprint.connect(node7, "ret", node84, "var")
Blueprint.connect(node84, "var", node81, "var")
Blueprint.connect(node75, "var", node74, "albedo")
Blueprint.connect(node76, "var", node74, "metallic")
Blueprint.connect(node77, "var", node74, "roughness")
Blueprint.connect(node78, "var", node74, "ao")
Blueprint.connect(node79, "var", node74, "N")
Blueprint.connect(node80, "var", node74, "V")
Blueprint.connect(node81, "var", node74, "F")
Blueprint.connect(node15, "NoL", node20, "var")
Blueprint.connect(node20, "var", node49, "var")
Blueprint.connect(node20, "var", node40, "var")
Blueprint.connect(node15, "NoH", node19, "var")
Blueprint.connect(node19, "var", node42, "var")
Blueprint.connect(node56, "var", node5, "at")
Blueprint.connect(node57, "var", node5, "ab")
Blueprint.connect(node63, "var", node5, "ToH")
Blueprint.connect(node64, "var", node5, "BoH")
Blueprint.connect(node42, "var", node5, "NoH")
Blueprint.connect(node15, "NoV", node18, "var")
Blueprint.connect(node18, "var", node39, "var")
Blueprint.connect(node58, "var", node6, "at")
Blueprint.connect(node59, "var", node6, "ab")
Blueprint.connect(node51, "ToV", node6, "ToV")
Blueprint.connect(node51, "BoV", node6, "BoV")
Blueprint.connect(node51, "ToL", node6, "ToL")
Blueprint.connect(node51, "BoL", node6, "BoL")
Blueprint.connect(node39, "var", node6, "NoV")
Blueprint.connect(node40, "var", node6, "NoL")
Blueprint.connect(node5, "ret", node8, "a")
Blueprint.connect(node6, "ret", node8, "b")
Blueprint.connect(node8, "v", node9, "a")
Blueprint.connect(node7, "ret", node9, "b")
Blueprint.connect(node9, "v", node48, "a")
Blueprint.connect(node48, "v", node3, "a")
Blueprint.connect(node32, "var", node3, "b")
Blueprint.connect(node3, "v", node2, "a")
Blueprint.connect(node33, "var", node2, "b")
Blueprint.connect(node2, "v", node85, "a")
Blueprint.connect(node74, "col", node85, "b")
Blueprint.connect(node85, "v", node1, "linear")
Blueprint.connect(node34, "v", node0, "w")
Blueprint.connect(node1, "non-linear", node0, "xyz")
Blueprint.connect(node0, "xyzw", node68, "var")
