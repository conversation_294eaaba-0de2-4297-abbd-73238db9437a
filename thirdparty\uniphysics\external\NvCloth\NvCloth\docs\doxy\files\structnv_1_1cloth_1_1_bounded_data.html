<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::BoundedData Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::BoundedData Struct Reference</h1><!-- doxytag: class="nv::cloth::BoundedData" --><!-- doxytag: inherits="nv::cloth::StridedData" --><code>#include &lt;<a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a>&gt;</code>
<p>
<div class="dynheader">
Inheritance diagram for nv::cloth::BoundedData:</div>
<div class="dynsection">

<p><center><img src="structnv_1_1cloth_1_1_bounded_data.png" usemap="#nv::cloth::BoundedData_map" border="0" alt=""></center>
<map name="nv::cloth::BoundedData_map">
<area href="structnv_1_1cloth_1_1_strided_data.html" alt="nv::cloth::StridedData" shape="rect" coords="0,0,141,24">
</map>
</div>

<p>
<a href="structnv_1_1cloth_1_1_bounded_data-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html#0be10dc81383e63b787821c8f4cc81c1">BoundedData</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a></td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="0be10dc81383e63b787821c8f4cc81c1"></a><!-- doxytag: member="nv::cloth::BoundedData::BoundedData" ref="0be10dc81383e63b787821c8f4cc81c1" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::BoundedData::BoundedData           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="e13bda6410e1f7a793d23c3492e1507b"></a><!-- doxytag: member="nv::cloth::BoundedData::count" ref="e13bda6410e1f7a793d23c3492e1507b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">nv::cloth::BoundedData::count</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
