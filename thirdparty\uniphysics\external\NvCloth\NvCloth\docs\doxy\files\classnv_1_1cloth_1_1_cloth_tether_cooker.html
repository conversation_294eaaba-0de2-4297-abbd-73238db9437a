<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothTetherCooker Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">ClothTetherCooker</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothTetherCooker Class Reference</h1><!-- doxytag: class="nv::cloth::ClothTetherCooker" --><code>#include &lt;<a class="el" href="_cloth_tether_cooker_8h-source.html">ClothTetherCooker.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth_tether_cooker-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#2bc514fcf01c15422f552f85756295d9">cook</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;desc)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Compute tether data from <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a> with simple distance measure.  <a href="#2bc514fcf01c15422f552f85756295d9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#e1670477f35e78f3ca1038a6093c1ac1">getCookerStatus</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns cooker status.  <a href="#e1670477f35e78f3ca1038a6093c1ac1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#683c0c2fbe6ba9127e55b20864b04035">getNbTethersPerParticle</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns number of tether anchors per particle.  <a href="#683c0c2fbe6ba9127e55b20864b04035"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#214d9ee202260d53da8ed8058994dc55">getTetherData</a> (physx::PxU32 *userTetherAnchors, physx::PxReal *userTetherLengths) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns computed tether data.  <a href="#214d9ee202260d53da8ed8058994dc55"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#fde8e2c9affc2ddf143feaab9c6a6a88">~ClothTetherCooker</a> ()</td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="fde8e2c9affc2ddf143feaab9c6a6a88"></a><!-- doxytag: member="nv::cloth::ClothTetherCooker::~ClothTetherCooker" ref="fde8e2c9affc2ddf143feaab9c6a6a88" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::ClothTetherCooker::~ClothTetherCooker           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="2bc514fcf01c15422f552f85756295d9"></a><!-- doxytag: member="nv::cloth::ClothTetherCooker::cook" ref="2bc514fcf01c15422f552f85756295d9" args="(const ClothMeshDesc &amp;desc)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::ClothTetherCooker::cook           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>desc</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Compute tether data from <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a> with simple distance measure. 
<p>
The tether constraint in NvCloth requires rest distance and anchor index to be precomputed during cooking time. This cooker computes a simple Euclidean distance to closest anchor point. The Euclidean distance measure works reasonably for flat cloth and flags and computation time is very fast. With this cooker, there is only one tether anchor point per particle. <dl class="see" compact><dt><b>See also:</b></dt><dd>ClothTetherGeodesicCooker for more accurate distance estimation. </dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>desc</em>&nbsp;</td><td>The cloth mesh descriptor prepared for cooking </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="e1670477f35e78f3ca1038a6093c1ac1"></a><!-- doxytag: member="nv::cloth::ClothTetherCooker::getCookerStatus" ref="e1670477f35e78f3ca1038a6093c1ac1" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::ClothTetherCooker::getCookerStatus           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns cooker status. 
<p>
This function returns cooker status after cooker computation is done. A non-zero return value indicates a failure. 
</div>
</div><p>
<a class="anchor" name="683c0c2fbe6ba9127e55b20864b04035"></a><!-- doxytag: member="nv::cloth::ClothTetherCooker::getNbTethersPerParticle" ref="683c0c2fbe6ba9127e55b20864b04035" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxU32 nv::cloth::ClothTetherCooker::getNbTethersPerParticle           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns number of tether anchors per particle. 
<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>Returned number indicates the maximum anchors. If some particles are assigned fewer anchors, the anchor indices will be physx::PxU32(-1) <p>
If there is no attached point in the input mesh descriptor, this will return 0 and no tether data will be generated. </dd></dl>

</div>
</div><p>
<a class="anchor" name="214d9ee202260d53da8ed8058994dc55"></a><!-- doxytag: member="nv::cloth::ClothTetherCooker::getTetherData" ref="214d9ee202260d53da8ed8058994dc55" args="(physx::PxU32 *userTetherAnchors, physx::PxReal *userTetherLengths) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::ClothTetherCooker::getTetherData           </td>
          <td>(</td>
          <td class="paramtype">physx::PxU32 *&nbsp;</td>
          <td class="paramname"> <em>userTetherAnchors</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">physx::PxReal *&nbsp;</td>
          <td class="paramname"> <em>userTetherLengths</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns computed tether data. 
<p>
This function returns anchor indices for each particle as well as desired distance between the tether anchor and the particle. The user buffers should be at least as large as number of particles. 
</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_tether_cooker_8h-source.html">ClothTetherCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
