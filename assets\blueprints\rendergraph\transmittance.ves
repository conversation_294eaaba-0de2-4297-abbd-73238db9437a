var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "front", "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less" }
node0.skip = false

_editor.add_node(node0, 116.282, -83.119200000001)

var node1 = ::rendergraph::nodes::clear::Clear()

node1.masks = [ "depth" ]
node1.values = { "color" : [ 0, 0, 0, 255 ] }

_editor.add_node(node1, -129.718, 93.880799999999)

var node2 = ::rendergraph::nodes::shader::Shader()

node2.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;

uniform UBO
{
	mat4 lightSpaceMatrix;
	mat4 model;	
};

void main()
{
    gl_Position = lightSpaceMatrix * model * vec4(aPos, 1.0);
}
"
node2.tcs = ""
node2.tes = ""
node2.gs = ""
node2.fs = "
#version 330 core
out vec4 FragColor;
void main()
{             
//     gl_FragDepth = gl_FragCoord.z;
}
"
node2.cs = ""
node2.render_gen()

_editor.add_node(node2, -146.26118818263, -40.079852215758)

var node3 = ::rendergraph::nodes::render_target::RenderTarget()

node3.width = 2048
node3.height = 2048

_editor.add_node(node3, -327.64895174481, -355.31899244416)

var node4 = ::rendergraph::nodes::texture::Texture()
node4.query_param("unif_name").value = "u_tex"
node4.gamma_correction = false
node4.init_texture(2048, 2048, "depth")

_editor.add_node(node4, -595.65959139026, -551.74617066551)

var node5 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node5, -335.48382568359, -40.799798066951)

var node6 = ::rendergraph::nodes::draw::Draw()

node6.set_prim_type("triangles")
node6.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "back", "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less" }
node6.skip = false

_editor.add_node(node6, 583.70380722722, -545.39065203247)

var node7 = ::rendergraph::nodes::shader::Shader()

node7.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec3 aNormal;

out VS_OUT 
{
    vec3 FragPos;
    vec3 Normal;
} vs_out;

uniform UBO
{
	mat4 projection;
	mat4 view;
	mat4 model;	
};

void main()
{
    vs_out.FragPos = vec3(model * vec4(aPos, 1.0));
    vs_out.Normal = transpose(inverse(mat3(model))) * aNormal;
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"
node7.tcs = ""
node7.tes = ""
node7.gs = ""
node7.fs = "
#version 420 core
out vec4 FragColor;

in VS_OUT {
    vec3 FragPos;
    vec3 Normal;
} fs_in;

//uniform sampler2DShadow shadowMap;
uniform sampler2D shadowMap;

uniform UBO
{
	vec3 lightPos;
	vec3 viewPos;
	mat4 lightSpaceMatrix;
	float zfar;	
};

float calc_shadow_pos()
{
	return 0.0;
}

void main()
{           
    vec3 color = vec3(0.5, 0.5, 0.5);
    vec3 normal = normalize(fs_in.Normal);
    vec3 lightColor = vec3(0.3);
    // ambient
    vec3 ambient = 0.3 * color;
    // diffuse
    vec3 lightDir = normalize(lightPos - fs_in.FragPos);
    float diff = max(dot(lightDir, normal), 0.0);
    vec3 diffuse = diff * lightColor;
    // specular
    vec3 viewDir = normalize(viewPos - fs_in.FragPos);
    vec3 reflectDir = reflect(-lightDir, normal);
    float spec = 0.0;
    vec3 halfwayDir = normalize(lightDir + viewDir);  
    spec = pow(max(dot(normal, halfwayDir), 0.0), 64.0);
    vec3 specular = spec * lightColor;    
    // calculate shadow
    vec3 lighting = (ambient + (diffuse + specular)) * color;    
    vec4 shrinked_pos = vec4(fs_in.FragPos - 0.002 * fs_in.Normal, 1.0);
    vec4 frag_pos_light_space = lightSpaceMatrix * shrinked_pos;
    //vec4 frag_pos_light_space = lightSpaceMatrix * vec4(fs_in.FragPos, 1.0);
    vec3 proj_coords = frag_pos_light_space.xyz / frag_pos_light_space.w;
    proj_coords = proj_coords * 0.5 + 0.5;
    float scale = 8.25 * 3;
    vec4 shadow_pos = vec4(proj_coords, 1.0);
    //float d1 = texture(shadowMap, vec3(shadow_pos.xy, shadow_pos.z)).r;
    float d1 = texture(shadowMap, shadow_pos.xy).r;
    float d2 = proj_coords.z;
    float d = scale * abs(d1 * zfar - d2);

    float dd = -d * d;
    vec3 profile = vec3(0.233, 0.455, 0.649) * exp(dd / 0.0064) +
                     vec3(0.1,   0.336, 0.344) * exp(dd / 0.0484) +
                     vec3(0.118, 0.198, 0.0)   * exp(dd / 0.187)  +
                     vec3(0.113, 0.007, 0.007) * exp(dd / 0.567)  +
                     vec3(0.358, 0.004, 0.0)   * exp(dd / 1.99)   +
                     vec3(0.078, 0.0,   0.0)   * exp(dd / 7.41);
    vec3 transmittance = profile * clamp(0.3 + dot(lightPos - fs_in.FragPos, -normal), 0.0, 1.0);
    //FragColor = vec4(transmittance + lighting * 3, 1.0);
    FragColor = vec4(profile, 1.0);    
}
"
node7.cs = ""
node7.render_gen()
node7.set_uniform("zfar", [ 1 ])

_editor.add_node(node7, 313.63013986634, -633.2442268813)

var node8 = ::blueprint::nodes::orthographic::Orthographic()

node8.left = 0
node8.right = 1
node8.bottom = 0
node8.top = 1
node8.near = 1
node8.far = -1

_editor.add_node(node8, -558.08553135401, 9.744824243971)

var node9 = ::blueprint::nodes::lookat::Lookat()

node9.eye.set(0, 0, 0.5)
node9.center.set(0, 0, 0)
node9.up.set(0, 1, 0)

_editor.add_node(node9, -571.29476274382, -125.00223213859)

var node10 = ::rendergraph::nodes::pass::Pass()

node10.once = false

_editor.add_node(node10, 300.56391158129, -25.009676686093)

var node11 = ::blueprint::nodes::number3::Number3()

node11.value.set(0.5, 0, 0)

_editor.add_node(node11, -735.0605538994, -190.80734506183)

var node12 = ::blueprint::nodes::number3::Number3()

node12.value.set(0, 0, 0.5)

_editor.add_node(node12, -735.74356735476, -82.891219114153)

var node13 = ::blueprint::nodes::perspective::Perspective()

node13.fovy = 45
node13.aspect = 0
node13.znear = 0.1
node13.zfar = 100

_editor.add_node(node13, 40.965354351958, -505.0140063073)

var node14 = ::blueprint::nodes::camera3d::Camera3d()

node14.cam.position.set(-0.10923194878468, 0.33230045651671, 0.61797473538041)
node14.cam.yaw = -91.3
node14.cam.pitch = -14.4
node14.cam.zoom = 45
node14.cam.update_vectors()
node14.speed = 0.01

_editor.add_node(node14, -124.76190942181, -648.41896347172)

var node15 = ::rendergraph::nodes::clear::Clear()

node15.masks = [ "color", "depth" ]
node15.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node15, 357.43530273438, -351.70159912109)

var node16 = ::blueprint::nodes::input::Input()

node16.var_name = "model"
node16.var_type = "model"

_editor.add_node(node16, -218.09797668457, -184.00326538086)

Blueprint.connect(node14, "zoom", node13, "fovy")
Blueprint.connect(node12, "v3", node9, "eye")
Blueprint.connect(node0, "next", node10, "do")
Blueprint.connect(node3, "fbo", node10, "fbo")
Blueprint.connect(node10, "next", node15, "prev")
Blueprint.connect(node8, "mat", node5, "a")
Blueprint.connect(node9, "mat", node5, "b")
Blueprint.connect(node5, "v", node2, "lightSpaceMatrix")
Blueprint.connect(node13, "mat", node7, "projection")
Blueprint.connect(node14, "mat", node7, "view")
Blueprint.connect(node12, "v3", node7, "lightPos")
Blueprint.connect(node14, "pos", node7, "viewPos")
Blueprint.connect(node5, "v", node7, "lightSpaceMatrix")
Blueprint.connect(node4, "tex", node7, "shadowMap")
Blueprint.connect(node15, "next", node6, "prev")
Blueprint.connect(node7, "out", node6, "shader")
Blueprint.connect(node16, "var", node6, "model")
Blueprint.connect(node4, "tex", node3, "depth")
Blueprint.connect(node1, "next", node0, "prev")
Blueprint.connect(node2, "out", node0, "shader")
Blueprint.connect(node16, "var", node0, "model")
