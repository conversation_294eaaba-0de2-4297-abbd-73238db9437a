var node0 = ::geograph::nodes::draw_geometry::DrawGeometry()
node0.query_param("skip").value = false

_editor.add_node(node0, 61.371550906983, 310.56716721547)

var node1 = ::blueprint::nodes::subgraph::Subgraph()
node1.load_from_file(_editor, "translate_selected.ves")

_editor.add_node(node1, 879.19632515019, 48.601408576216)

var node2 = ::blueprint::nodes::array::Array()
node2.query_param("serialize").value = false

_editor.add_node(node2, -900.18953895969, 117.48656488237)

var node3 = ::geograph::nodes::draw_geometry::DrawGeometry()
node3.query_param("skip").value = false

_editor.add_node(node3, 332.83907714468, 308.28525850186)

var node4 = ::blueprint::nodes::number3::Number3()

node4.value.set(0, 0, 0.70140242576599)

_editor.add_node(node4, 191.18156641058, 220.59465222878)

var node5 = ::blueprint::nodes::merge::Merge()

_editor.add_node(node5, -77.468170068428, 330.16621067707)

var node6 = ::blueprint::nodes::input::Input()

node6.var_name = "center"
node6.var_type = "num2"

_editor.add_node(node6, -896.90487587552, 304.94377169523)

var node7 = ::blueprint::nodes::input::Input()

node7.var_name = "outer"
node7.var_type = "num2"

_editor.add_node(node7, -895.23776233653, 227.15228505354)

var node8 = ::geograph::nodes::circle::Circle()
node8.query_param("x").value = 0
node8.query_param("y").value = 575.25775146484
node8.query_param("r").value = 254.67932128906
node8.query_param("fill").value = false
node8.query_param("color").value.set(0.54356819391251, 0, 0)

_editor.add_node(node8, 163.40861179951, 575.77156681332)

var node9 = ::blueprint::nodes::length::Length()

_editor.add_node(node9, 23.386343005478, 618.80462686837)

var node10 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node10, -123.0144834408, 607.44344542952)

var node11 = ::blueprint::nodes::output::Output()

node11.var_name = "circle"
node11.var_type = "geo"

_editor.add_node(node11, 317.33886555501, 663.84333789576)

var node12 = ::blueprint::nodes::store::Store()

node12.var_name = "center"

_editor.add_node(node12, -742.65552867543, 305.9887972745)

var node13 = ::blueprint::nodes::store::Store()

node13.var_name = "outer"

_editor.add_node(node13, -749.29189231179, 211.98875843394)

var node14 = ::blueprint::nodes::load::Load()

node14.var_name = "center"

_editor.add_node(node14, -217.44889491058, 362.73261557334)

var node15 = ::blueprint::nodes::load::Load()

node15.var_name = "outer"

_editor.add_node(node15, -216.6472836329, 309.46809727692)

var node16 = ::blueprint::nodes::load::Load()

node16.var_name = "center"

_editor.add_node(node16, -260.83734685724, 630.17064319957)

var node17 = ::blueprint::nodes::load::Load()

node17.var_name = "outer"

_editor.add_node(node17, -263.56462513317, 579.26151344993)

var node18 = ::blueprint::nodes::load::Load()

node18.var_name = "center"

_editor.add_node(node18, 22.799022327768, 667.44332608309)

var node19 = ::blueprint::nodes::store::Store()

node19.var_name = "selected"

_editor.add_node(node19, -754.37711159713, 119.4275098117)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "selected"

_editor.add_node(node20, 188.56159574921, 294.91904123203)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "selected"

_editor.add_node(node21, -507.06881227432, -235.24559996517)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "selected"

_editor.add_node(node22, 670.83077860777, 55.473377296541)

var node23 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node23, 530.93187033914, 79.663235196199)

var node24 = ::editorgraph::nodes::mouse_left_down::MouseLeftDown()

_editor.add_node(node24, -495.78345186171, -44.994216596339)

var node25 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node25, 94.034430560402, -118.13612242316)

var node26 = ::geograph::nodes::is_contain::IsContain()

_editor.add_node(node26, -92.096984218889, -149.46480860425)

var node27 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node27, -235.50296577149, -111.29404609783)

var node28 = ::blueprint::nodes::list_add::ListAdd()
node28.query_param("unique").value = true

_editor.add_node(node28, -363.75659314317, -253.38098457076)

var node29 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node29, -96.745478809143, -63.401183424531)

var node30 = ::blueprint::nodes::list_add::ListAdd()
node30.query_param("unique").value = true

_editor.add_node(node30, -95.842746698948, -253.21720694722)

var node31 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node31, 372.40524335291, -137.75318650447)

var node32 = ::geograph::nodes::is_contain::IsContain()

_editor.add_node(node32, 239.66928087975, -207.50855677581)

var node33 = ::blueprint::nodes::load::Load()

node33.var_name = "outer"

_editor.add_node(node33, 93.589664015563, -234.20247649877)

var node34 = ::blueprint::nodes::list_add::ListAdd()
node34.query_param("unique").value = false

_editor.add_node(node34, 244.19198874916, -294.0199502203)

var node35 = ::blueprint::nodes::load::Load()

node35.var_name = "selected"

_editor.add_node(node35, 95.866717038721, -281.61496522027)

var node36 = ::blueprint::nodes::load::Load()

node36.var_name = "outer"

_editor.add_node(node36, -226.29681346042, -314.08604847301)

var node37 = ::blueprint::nodes::load::Load()

node37.var_name = "selected"

_editor.add_node(node37, -234.94623609811, 20.44471264043)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "selected"

_editor.add_node(node38, -227.5082377284, -270.29659302767)

var node39 = ::blueprint::nodes::load::Load()

node39.var_name = "center"

_editor.add_node(node39, -506.84709331418, -283.51975395266)

var node40 = ::blueprint::nodes::load::Load()

node40.var_name = "center"

_editor.add_node(node40, -232.8888596395, -37.04620270991)

var node41 = ::blueprint::nodes::input::Input()

node41.var_name = "cam_mat"
node41.var_type = "mat4"

_editor.add_node(node41, -885.70138383345, 44.647781649499)

var node42 = ::blueprint::nodes::store::Store()

node42.var_name = "cam_mat"

_editor.add_node(node42, -751.22084960938, 45.58117675781)

var node43 = ::blueprint::nodes::load::Load()

node43.var_name = "cam_mat"

_editor.add_node(node43, -76.920886230468, 264.48121337891)

var node44 = ::blueprint::nodes::load::Load()

node44.var_name = "cam_mat"

_editor.add_node(node44, -386.30976721319, -135.80193558578)

var node45 = ::blueprint::nodes::load::Load()

node45.var_name = "cam_mat"

_editor.add_node(node45, 192.14159558771, 145.60358610618)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "cam_mat"

_editor.add_node(node46, 670.50269527556, 11.151602726057)

Blueprint.connect(node41, "var", node42, "var")
Blueprint.connect(node42, "var", node46, "var")
Blueprint.connect(node42, "var", node45, "var")
Blueprint.connect(node42, "var", node44, "var")
Blueprint.connect(node42, "var", node43, "var")
Blueprint.connect(node24, "pos", node27, "screen")
Blueprint.connect(node44, "var", node27, "cam_mat")
Blueprint.connect(node24, "event", node23, "event")
Blueprint.connect(node31, "next", node23, "action")
Blueprint.connect(node7, "var", node13, "var")
Blueprint.connect(node13, "var", node36, "var")
Blueprint.connect(node13, "var", node33, "var")
Blueprint.connect(node33, "var", node32, "geo")
Blueprint.connect(node27, "world", node32, "pos")
Blueprint.connect(node13, "var", node17, "var")
Blueprint.connect(node13, "var", node15, "var")
Blueprint.connect(node6, "var", node12, "var")
Blueprint.connect(node12, "var", node40, "var")
Blueprint.connect(node40, "var", node26, "geo")
Blueprint.connect(node27, "world", node26, "pos")
Blueprint.connect(node12, "var", node39, "var")
Blueprint.connect(node12, "var", node18, "var")
Blueprint.connect(node12, "var", node16, "var")
Blueprint.connect(node16, "var", node10, "a")
Blueprint.connect(node17, "var", node10, "b")
Blueprint.connect(node10, "v", node9, "v")
Blueprint.connect(node18, "var", node8, "center")
Blueprint.connect(node9, "v", node8, "raduis")
Blueprint.connect(node8, "geo", node11, "var")
Blueprint.connect(node12, "var", node14, "var")
Blueprint.connect(node14, "var", node5, "in0")
Blueprint.connect(node15, "var", node5, "in1")
Blueprint.connect(node5, "list", node0, "geos")
Blueprint.connect(node43, "var", node0, "cam_mat")
Blueprint.connect(node2, "all", node19, "var")
Blueprint.connect(node19, "var", node38, "var")
Blueprint.connect(node19, "var", node37, "var")
Blueprint.connect(node37, "var", node29, "list")
Blueprint.connect(node29, "next", node25, "prev")
Blueprint.connect(node26, "out", node25, "cond")
Blueprint.connect(node30, "next", node25, "true")
Blueprint.connect(node25, "next", node31, "prev")
Blueprint.connect(node32, "out", node31, "cond")
Blueprint.connect(node34, "next", node31, "true")
Blueprint.connect(node19, "var", node35, "var")
Blueprint.connect(node35, "var", node34, "list")
Blueprint.connect(node33, "var", node34, "add")
Blueprint.connect(node19, "var", node22, "var")
Blueprint.connect(node23, "next", node1, "prev")
Blueprint.connect(node22, "var", node1, "selected")
Blueprint.connect(node46, "var", node1, "cam_mat")
Blueprint.connect(node19, "var", node21, "var")
Blueprint.connect(node21, "var", node28, "list")
Blueprint.connect(node39, "var", node28, "add")
Blueprint.connect(node28, "next", node30, "prev")
Blueprint.connect(node38, "var", node30, "list")
Blueprint.connect(node36, "var", node30, "add")
Blueprint.connect(node19, "var", node20, "var")
Blueprint.connect(node0, "next", node3, "prev")
Blueprint.connect(node20, "var", node3, "geos")
Blueprint.connect(node4, "v3", node3, "color")
Blueprint.connect(node45, "var", node3, "cam_mat")
