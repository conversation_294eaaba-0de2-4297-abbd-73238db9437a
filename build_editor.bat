@echo off
echo Starting Tantien Editor Build...

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\Tools\VsDevCmd.bat"

REM Create lib directory if it doesn't exist
if not exist "platform\windows\lib" mkdir "platform\windows\lib"

REM Copy existing libraries to lib directory
copy "platform\windows\x64\Release\*.lib" "platform\windows\lib\"

echo Building editor project...
MSBuild "platform\windows\projects\editor.vcxproj" /p:Configuration=Release /p:Platform=x64 /v:minimal /m

if %ERRORLEVEL% NEQ 0 (
    echo Build failed with error code %ERRORLEVEL%
    pause
    exit /b %ERRORLEVEL%
)

echo Build completed successfully!
echo Looking for editor.exe...
dir /s "platform\windows\x64\Release\editor.exe"

if exist "platform\windows\x64\Release\editor.exe" (
    echo Editor executable found!
    echo Starting editor...
    cd platform\windows\x64\Release
    editor.exe rendergraph
) else (
    echo Editor executable not found. Checking build output...
    dir "platform\windows\x64\Release\"
)

pause
