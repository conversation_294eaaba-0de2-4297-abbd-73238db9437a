<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Solver Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#63e732712c5a43c44e6018cca6c1fb82">addCloth</a>(Cloth *cloth)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#ea0f778230b2a0f211bdb5d36d3b54f3">addCloths</a>(Range&lt; Cloth * &gt; cloths)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#d2efbe447bf07138c615973c349ab839">beginSimulation</a>(float dt)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#19d777a3882814910f8a024d92072d48">endSimulation</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#a3e121ffbccc07180e08a2387eb4f6ac">getClothList</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#6a72529d713f46dbd17a5b541aaec6df">getInterCollisionDistance</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#124dc836903890185934c6eaedec2079">getInterCollisionNbIterations</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#09e4be9b50229213a837d00a3f2f6a3f">getInterCollisionStiffness</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#de377e651711ebbb9e70f928cbb682e2">getNumCloths</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#54e393ba3b9fd5305385e2f57d3ca165">getSimulationChunkCount</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#12a60f43b537d78499e30508bd9a6d3c">hasError</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#0bc438b310a4989a96c426c83a1e0beb">operator=</a>(const Solver &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#1ed765a15ab2dabbb5186d14bc5f70b1">removeCloth</a>(Cloth *cloth)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#5043adf6727bf66b966de6393e7d67d9">setInterCollisionDistance</a>(float distance)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#31b3d4d36f2025f10cb04a32e28fada4">setInterCollisionFilter</a>(InterCollisionFilter filter)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#3046ea1153c1f9decfc161155cc9810b">setInterCollisionNbIterations</a>(uint32_t nbIterations)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#4f15accbaeff04edbebd31bf7dd9be3e">setInterCollisionStiffness</a>(float stiffness)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#62d209d861c8f5aa0523536d851de093">simulateChunk</a>(int idx)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#8955bbdbea66b33486f0588ab2f80c46">Solver</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [inline, protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#21b6b117db42d8a3206cee521e6af4b0">Solver</a>(const Solver &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_solver.html#4830e23f5fbaa9dfa7c8c0ce32fa85bd">~Solver</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td><code> [inline, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
