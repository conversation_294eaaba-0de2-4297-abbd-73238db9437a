#include "graph/Graph.h"
#include "graph/Node.h"
#include "graph/Edge.h"

namespace graph
{

void Graph::AddNode(const std::shared_ptr<Node>& node)
{
	m_nodes.push_back(node);
}

void Graph::AddEdge(size_t f_node, size_t t_node)
{
	if (f_node < m_nodes.size() && t_node < m_nodes.size())
	{
		m_nodes[f_node]->AddConnect(m_nodes[t_node]);

		// Create edge object and add to edges map
		auto edge = std::make_shared<Edge>();
		edge->SetFromNode(m_nodes[f_node]);
		edge->SetToNode(m_nodes[t_node]);
		m_edges[m_edges.size()] = edge;
	}
}

std::shared_ptr<Node> Graph::GetNode(size_t index) const
{
	if (index < m_nodes.size()) {
		return m_nodes[index];
	}
	return nullptr;
}

void Graph::ClearEdges(size_t node_idx)
{
	if (node_idx < m_nodes.size()) {
		// Remove edges involving this node
		auto it = m_edges.begin();
		while (it != m_edges.end()) {
			auto edge = it->second;
			if (edge->GetFromNode() == m_nodes[node_idx] ||
				edge->GetToNode() == m_nodes[node_idx]) {
				it = m_edges.erase(it);
			} else {
				++it;
			}
		}

		// Clear connections from the node itself
		if (m_nodes[node_idx]) {
			m_nodes[node_idx]->ClearConnections();
		}
	}
}

}