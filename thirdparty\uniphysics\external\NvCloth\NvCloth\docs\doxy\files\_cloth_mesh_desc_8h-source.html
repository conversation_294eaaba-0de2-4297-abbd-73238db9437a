<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: ClothMeshDesc.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>ClothMeshDesc.h</h1><a href="_cloth_mesh_desc_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.  </span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 
<a name="l00031"></a>00031 <span class="preprocessor">#ifndef NV_CLOTH_EXTENSIONS_CLOTHMESHDESC</span>
<a name="l00032"></a>00032 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_EXTENSIONS_CLOTHMESHDESC</span>
<a name="l00033"></a>00033 <span class="preprocessor"></span>
<a name="l00037"></a>00037 <span class="preprocessor">#include "foundation/PxVec3.h"</span>
<a name="l00038"></a>00038 
<a name="l00039"></a>00039 <span class="keyword">namespace </span>nv
<a name="l00040"></a>00040 {
<a name="l00041"></a>00041 <span class="keyword">namespace </span>cloth
<a name="l00042"></a>00042 {
<a name="l00043"></a>00043 
<a name="l00044"></a><a class="code" href="structnv_1_1cloth_1_1_strided_data.html">00044</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_strided_data.html">StridedData</a>
<a name="l00045"></a>00045 {
<a name="l00051"></a><a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba">00051</a>     physx::PxU32 <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a>;
<a name="l00052"></a><a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">00052</a>     <span class="keyword">const</span> <span class="keywordtype">void</span>* <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a>;
<a name="l00053"></a>00053 
<a name="l00054"></a><a class="code" href="structnv_1_1cloth_1_1_strided_data.html#06829ec148078b342bcf4bcdd11ff035">00054</a>     <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#06829ec148078b342bcf4bcdd11ff035">StridedData</a>() : <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a>( 0 ), <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a>( NULL ) {}
<a name="l00055"></a>00055 
<a name="l00056"></a>00056     <span class="keyword">template</span>&lt;<span class="keyword">typename</span> TDataType&gt;
<a name="l00057"></a><a class="code" href="structnv_1_1cloth_1_1_strided_data.html#127ee8d050b77cd58ccece6eb3495ccb">00057</a>     PX_INLINE <span class="keyword">const</span> TDataType&amp; <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#127ee8d050b77cd58ccece6eb3495ccb">at</a>( physx::PxU32 idx )<span class="keyword"> const</span>
<a name="l00058"></a>00058 <span class="keyword">    </span>{
<a name="l00059"></a>00059         physx::PxU32 theStride( <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a> );
<a name="l00060"></a>00060         <span class="keywordflow">if</span> ( theStride == 0 )
<a name="l00061"></a>00061             theStride = <span class="keyword">sizeof</span>( TDataType );
<a name="l00062"></a>00062         physx::PxU32 offset( theStride * idx );
<a name="l00063"></a>00063         <span class="keywordflow">return</span> *(<span class="keyword">reinterpret_cast&lt;</span><span class="keyword">const </span>TDataType*<span class="keyword">&gt;</span>( <span class="keyword">reinterpret_cast&lt;</span> <span class="keyword">const </span>physx::PxU8* <span class="keyword">&gt;</span>( <a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a> ) + offset ));
<a name="l00064"></a>00064     }
<a name="l00065"></a>00065 };
<a name="l00066"></a>00066 
<a name="l00067"></a><a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">00067</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> : <span class="keyword">public</span> <a class="code" href="structnv_1_1cloth_1_1_strided_data.html">StridedData</a>
<a name="l00068"></a>00068 {
<a name="l00069"></a><a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">00069</a>     physx::PxU32 <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a>;
<a name="l00070"></a><a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#0be10dc81383e63b787821c8f4cc81c1">00070</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#0be10dc81383e63b787821c8f4cc81c1">BoundedData</a>() : <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a>( 0 ) {}
<a name="l00071"></a>00071 };
<a name="l00072"></a>00072 
<a name="l00076"></a><a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html">00076</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html" title="Enum with flag values to be used in ClothMeshDesc.">MeshFlag</a>
<a name="l00077"></a>00077 {
<a name="l00078"></a><a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489">00078</a>     <span class="keyword">enum</span> <a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489">Enum</a>
<a name="l00079"></a>00079     {
<a name="l00080"></a><a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93">00080</a>         <a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93" title="Denotes the use of 16-bit vertex indices.">e16_BIT_INDICES</a>     =   (1&lt;&lt;1)  
<a name="l00081"></a>00081     };
<a name="l00082"></a>00082 };
<a name="l00083"></a>00083 
<a name="l00087"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">00087</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a>
<a name="l00088"></a>00088 {
<a name="l00089"></a>00089 <span class="keyword">public</span>:
<a name="l00090"></a>00090 
<a name="l00094"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************">00094</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> points;
<a name="l00095"></a>00095 
<a name="l00099"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598">00099</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> pointsStiffness;
<a name="l00100"></a>00100 
<a name="l00108"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de">00108</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> invMasses;
<a name="l00109"></a>00109 
<a name="l00124"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e">00124</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> triangles;
<a name="l00125"></a>00125 
<a name="l00140"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb">00140</a>     <a class="code" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> quads;
<a name="l00141"></a>00141 
<a name="l00145"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a">00145</a>     <span class="keywordtype">unsigned</span> <span class="keywordtype">int</span> flags;
<a name="l00146"></a>00146 
<a name="l00150"></a>00150     PX_INLINE <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a>();
<a name="l00154"></a>00154     PX_INLINE <span class="keywordtype">void</span> setToDefault();
<a name="l00159"></a>00159     PX_INLINE <span class="keywordtype">bool</span> isValid() <span class="keyword">const</span>;
<a name="l00160"></a>00160 };
<a name="l00161"></a>00161 
<a name="l00162"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e475fee21a2442dd86f30d836a6ad1af">00162</a> PX_INLINE <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e475fee21a2442dd86f30d836a6ad1af" title="constructor sets to default.">ClothMeshDesc::ClothMeshDesc</a>()
<a name="l00163"></a>00163 {
<a name="l00164"></a>00164     <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a" title="Flags bits, combined from values of the enum MeshFlag.">flags</a> = 0;
<a name="l00165"></a>00165 }
<a name="l00166"></a>00166 
<a name="l00167"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#d269d7bebc10b54088fc73e77c1372dd">00167</a> PX_INLINE <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#d269d7bebc10b54088fc73e77c1372dd" title="(re)sets the structure to the default.">ClothMeshDesc::setToDefault</a>()
<a name="l00168"></a>00168 {
<a name="l00169"></a>00169     *<span class="keyword">this</span> = <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e475fee21a2442dd86f30d836a6ad1af" title="constructor sets to default.">ClothMeshDesc</a>();
<a name="l00170"></a>00170 }
<a name="l00171"></a>00171 
<a name="l00172"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#3dbb3dc26cddbdf72c5455a485f0a4f4">00172</a> PX_INLINE <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#3dbb3dc26cddbdf72c5455a485f0a4f4" title="Returns true if the descriptor is valid.">ClothMeshDesc::isValid</a>()<span class="keyword"> const</span>
<a name="l00173"></a>00173 <span class="keyword"></span>{
<a name="l00174"></a>00174     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &lt; 3)   <span class="comment">// at least 1 triangle</span>
<a name="l00175"></a>00175         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00176"></a>00176     <span class="keywordflow">if</span> ((<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598" title="Pointer to first stiffness value in stiffnes per vertex array.">pointsStiffness</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> != <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a>) &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598" title="Pointer to first stiffness value in stiffnes per vertex array.">pointsStiffness</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> != 0)
<a name="l00177"></a>00177         <span class="keywordflow">return</span> <span class="keyword">false</span>; <span class="comment">// either all or none of the points can have stiffness information</span>
<a name="l00178"></a>00178     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &gt; 0xffff &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a" title="Flags bits, combined from values of the enum MeshFlag.">flags</a> &amp; <a class="code" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93" title="Denotes the use of 16-bit vertex indices.">MeshFlag::e16_BIT_INDICES</a>)
<a name="l00179"></a>00179         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00180"></a>00180     <span class="keywordflow">if</span> (!<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a>)
<a name="l00181"></a>00181         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00182"></a>00182     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a> &lt; <span class="keyword">sizeof</span>(physx::PxVec3))  <span class="comment">// should be at least one point</span>
<a name="l00183"></a>00183         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00184"></a>00184 
<a name="l00185"></a>00185     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de" title="Determines whether particle is simulated or static.">invMasses</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de" title="Determines whether particle is simulated or static.">invMasses</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a> &lt; <span class="keyword">sizeof</span>(<span class="keywordtype">float</span>))
<a name="l00186"></a>00186         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00187"></a>00187     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de" title="Determines whether particle is simulated or static.">invMasses</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de" title="Determines whether particle is simulated or static.">invMasses</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> != <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************" title="Pointer to first vertex point.">points</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a>)
<a name="l00188"></a>00188         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00189"></a>00189 
<a name="l00190"></a>00190     <span class="keywordflow">if</span> (!<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e" title="Pointer to the first triangle.">triangles</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &amp;&amp; !<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb" title="Pointer to the first quad.">quads</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a>)   <span class="comment">// no support for non-indexed mesh</span>
<a name="l00191"></a>00191         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00192"></a>00192     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e" title="Pointer to the first triangle.">triangles</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &amp;&amp; !<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e" title="Pointer to the first triangle.">triangles</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a>)
<a name="l00193"></a>00193         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00194"></a>00194     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb" title="Pointer to the first quad.">quads</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &amp;&amp; !<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb" title="Pointer to the first quad.">quads</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a>)
<a name="l00195"></a>00195         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00196"></a>00196 
<a name="l00197"></a>00197     physx::PxU32 indexSize = (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a" title="Flags bits, combined from values of the enum MeshFlag.">flags</a> &amp; MeshFlag::e16_BIT_INDICES) ? <span class="keyword">sizeof</span>(physx::PxU16) : <span class="keyword">sizeof</span>(physx::PxU32);
<a name="l00198"></a>00198     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e" title="Pointer to the first triangle.">triangles</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e" title="Pointer to the first triangle.">triangles</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a> &lt; indexSize*3) 
<a name="l00199"></a>00199         <span class="keywordflow">return</span> <span class="keyword">false</span>; 
<a name="l00200"></a>00200     <span class="keywordflow">if</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb" title="Pointer to the first quad.">quads</a>.<a class="code" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">count</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb" title="Pointer to the first quad.">quads</a>.<a class="code" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba" title="The offset in bytes between consecutive samples in the data.">stride</a> &lt; indexSize*4)
<a name="l00201"></a>00201         <span class="keywordflow">return</span> <span class="keyword">false</span>;
<a name="l00202"></a>00202 
<a name="l00203"></a>00203     <span class="keywordflow">return</span> <span class="keyword">true</span>;
<a name="l00204"></a>00204 }
<a name="l00205"></a>00205 
<a name="l00206"></a>00206 } <span class="comment">// namespace cloth</span>
<a name="l00207"></a>00207 } <span class="comment">// namespace nv</span>
<a name="l00208"></a>00208 
<a name="l00209"></a>00209 
<a name="l00211"></a>00211 <span class="preprocessor">#endif</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
