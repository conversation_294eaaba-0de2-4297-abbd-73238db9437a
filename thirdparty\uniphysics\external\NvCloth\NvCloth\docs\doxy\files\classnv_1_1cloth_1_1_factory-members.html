<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Factory Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#d7b0ba6b9fd6a304b6a2b2560a96b472">clone</a>(const Cloth &amp;cloth)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#942308b0a218726c7316382228771e7e">createCloth</a>(Range&lt; const physx::PxVec4 &gt; particles, Fabric &amp;fabric)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#b49c2a8f3dcdd015505fa18e8337bb7a">createFabric</a>(uint32_t numParticles, Range&lt; const uint32_t &gt; phaseIndices, Range&lt; const uint32_t &gt; sets, Range&lt; const float &gt; restvalues, Range&lt; const float &gt; stiffnessValues, Range&lt; const uint32_t &gt; indices, Range&lt; const uint32_t &gt; anchors, Range&lt; const float &gt; tetherLengths, Range&lt; const uint32_t &gt; triangles)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#28a6ad0896774886b214be9573fc3ca2">createSolver</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#8c4537f687c38c8055ef54299463b7d1">extractCollisionData</a>(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; spheres, Range&lt; uint32_t &gt; capsules, Range&lt; physx::PxVec4 &gt; planes, Range&lt; uint32_t &gt; convexes, Range&lt; physx::PxVec3 &gt; triangles) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#b745e0c37ad99ee0efde69fe0883c00c">extractFabricData</a>(const Fabric &amp;fabric, Range&lt; uint32_t &gt; phaseIndices, Range&lt; uint32_t &gt; sets, Range&lt; float &gt; restvalues, Range&lt; float &gt; stiffnessValues, Range&lt; uint32_t &gt; indices, Range&lt; uint32_t &gt; anchors, Range&lt; float &gt; tetherLengths, Range&lt; uint32_t &gt; triangles) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#51b24b732ffcf2fba082dc73ce0d6953">extractMotionConstraints</a>(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destConstraints) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#********************************">extractParticleAccelerations</a>(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destAccelerations) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#f268db5221c421f9167c5f0fbe894e19">extractRestPositions</a>(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destRestPositions) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#615dc5d0f0642e8ce9a2cb94d50c0ebe">extractSelfCollisionIndices</a>(const Cloth &amp;cloth, Range&lt; uint32_t &gt; destIndices) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#b52ffcc03824042c4eef65e5df80ee21">extractSeparationConstraints</a>(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destConstraints) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#41571a2afa49108e0bc03516cdd080c4">extractVirtualParticles</a>(const Cloth &amp;cloth, Range&lt; uint32_t[4]&gt; destIndices, Range&lt; physx::PxVec3 &gt; destWeights) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#0fe2d6c93b96ccbf2a38b5d7a0c55949">Factory</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [inline, protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#7568b20d666221ff9e5b28038c1c63c6">Factory</a>(const Factory &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#de78d96c7cd81520176d4bfd4e488b04">getPlatform</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#a9517fa38c45084af39183dd3dd34c8a">NV_CLOTH_CALL_CONV::NvClothDestroyFactory</a>(nv::cloth::Factory *)</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [friend]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#0daf294ea3c4c8713cbdfda320e46e16">operator=</a>(const Factory &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_factory.html#edab1136aacbffe558d8b00be6cb2257">~Factory</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td><code> [inline, protected, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
