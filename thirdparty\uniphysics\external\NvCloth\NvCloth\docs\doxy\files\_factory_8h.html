<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Factory.h File Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Factory.h File Reference</h1><code>#include &quot;<a class="el" href="_range_8h-source.html">NvCloth/Range.h</a>&quot;</code><br>
<code>#include &lt;foundation/PxVec4.h&gt;</code><br>
<code>#include &lt;foundation/PxVec3.h&gt;</code><br>
<code>#include &quot;<a class="el" href="_allocator_8h-source.html">NvCloth/Allocator.h</a>&quot;</code><br>

<p>
<a href="_factory_8h-source.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Classes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">abstract factory to create context-specific simulation components such as cloth, solver, collision, etc.  <a href="classnv_1_1cloth_1_1_factory.html#_details">More...</a><br></td></tr>
<tr><td colspan="2"><br><h2>Typedefs</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">typedef struct CUctx_st *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_factory_8h.html#f9f5bd81658f866613785b3a0bb7d7d9">CUcontext</a></td></tr>

<tr><td colspan="2"><br><h2>Enumerations</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">enum &nbsp;</td><td class="memItemRight" valign="bottom"><b>Platform</b> { <a class="el" href="namespacenv_1_1cloth.html#c7ba98882a245b1fdb5d45e707180a155787efd621d5ceee0b5eb75124063fd5">nv::cloth::CPU</a>, 
<a class="el" href="namespacenv_1_1cloth.html#c7ba98882a245b1fdb5d45e707180a1519fe9c14eb0cffc55c5818ff49263af6">nv::cloth::CUDA</a>, 
<a class="el" href="namespacenv_1_1cloth.html#c7ba98882a245b1fdb5d45e707180a153660e786ae25f27c44a45978bc1d90fe">nv::cloth::DX11</a>
 }</td></tr>

<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_factory_8h.html#f3c25e9c1f8c212ac7c1c734dbecd9a6">NV_CLOTH_API</a> (bool) NvClothCompiledWithDxSupport()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if this dll was compiled with DX support.  <a href="#f3c25e9c1f8c212ac7c1c734dbecd9a6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_factory_8h.html#00a0355aec1b4fbf9cc00c5ab61939f8">NV_CLOTH_API</a> (<a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> *) NvClothCreateFactoryCPU()</td></tr>

<tr><td colspan="2"><br><h2>Variables</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">NV_CLOTH_API(<a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> <br class="typebreak">
*) NvClothCreateFactoryDX11(nv <br class="typebreak">
NV_CLOTH_API(void) <br class="typebreak">
NvClothDestroyFactory(n&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gba25c25fbcf0684a083841a6ddea89d6">NV_CLOTH_API</a> )(bool) NvClothCompiledWithCudaSupport()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if this dll was compiled with cuda support.  <a href="group__extensions.html#gba25c25fbcf0684a083841a6ddea89d6"></a><br></td></tr>
</table>
<hr><h2>Typedef Documentation</h2>
<a class="anchor" name="f9f5bd81658f866613785b3a0bb7d7d9"></a><!-- doxytag: member="Factory.h::CUcontext" ref="f9f5bd81658f866613785b3a0bb7d7d9" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef struct CUctx_st* <a class="el" href="_factory_8h.html#f9f5bd81658f866613785b3a0bb7d7d9">CUcontext</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="f3c25e9c1f8c212ac7c1c734dbecd9a6"></a><!-- doxytag: member="Factory.h::NV_CLOTH_API" ref="f3c25e9c1f8c212ac7c1c734dbecd9a6" args="(bool) NvClothCompiledWithDxSupport()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if this dll was compiled with DX support. 
<p>

</div>
</div><p>
<a class="anchor" name="00a0355aec1b4fbf9cc00c5ab61939f8"></a><!-- doxytag: member="Factory.h::NV_CLOTH_API" ref="00a0355aec1b4fbf9cc00c5ab61939f8" args="(nv::cloth::Factory *) NvClothCreateFactoryCPU()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_API           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
