<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
  "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">


<html xmlns="http://www.w3.org/1999/xhtml">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="x-ua-compatible" content="IE=Edge"/>
    
    <title>Compiling &mdash; NvCloth 1.1.5 documentation</title>
    
    <link rel="stylesheet" href="../_static/default.css" type="text/css" />
    <link rel="stylesheet" href="../_static/pygments.css" type="text/css" />
    <link rel="stylesheet" href="../_static/breathe.css" type="text/css" />
    <link rel="stylesheet" href="../_static/application.css" type="text/css" />
    <link rel="stylesheet" href="../_static/styleguide.css" type="text/css" />
    
    <script type="text/javascript">
      var DOCUMENTATION_OPTIONS = {
        URL_ROOT:    '../',
        VERSION:     '1.1.5',
        COLLAPSE_INDEX: false,
        FILE_SUFFIX: '.html',
        HAS_SOURCE:  true
      };
    </script>
    <script type="text/javascript" src="../_static/jquery.js"></script>
    <script type="text/javascript" src="../_static/underscore.js"></script>
    <script type="text/javascript" src="../_static/doctools.js"></script>
    <script type="text/javascript" src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML"></script>
    <script type="text/javascript" src="../_static/bootstrap.js"></script>
    <script type="text/javascript" src="../_static/jquery.cookie.js"></script>
    <script type="text/javascript" src="../_static/jquery.storageapi.js"></script>
    <link rel="top" title="NvCloth 1.1.5 documentation" href="../index.html" />
    <link rel="next" title="Modules" href="../Modules/Index.html" />
    <link rel="prev" title="Release Notes" href="../ReleaseNotes/index.html" /> 
  </head>
  <body>
<nav class="navbar navbar-inverse navbar-default">
  <div class="row">
      <div class="navbar-brand">
             <img class="logo" src="../_static/developerzone_gameworks_logo.png" alt="Logo"/>
      </div>
<div id="searchbox" style="display: none; float:right; padding-top:4px; padding-right:4px">
    <form class="search form-inline" action="../search.html" method="get">
      <div class="form-group">
      <input type="text" name="q" class="form-control" />
      <input type="submit" value="Search" class="btn btn-primary" />
      </div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
  </div>
</nav>
<div class="masthead">
    <div class="row">
      <ul class="breadcrumb">
        <li><a href="../index.html">NvCloth 1.1.5 documentation</a></li> 
      </ul>
    </div>
</div>
<div class="row">
  <div class="col-md-3 bs-sidenav" style="white-space: nowrap; overflow: auto;">
<div class="bs-sidebar">
  <div id="sidebar_toc">
  <h4>Table Of Contents</h4>
  <ul class="current">
<li class="toctree-l1"><a class="reference internal" href="../ReleaseNotes/index.html">Release Notes</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id1">1.1.5</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id2">1.1.4</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id3">1.1.3</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id4">1.1.2</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id5">1.1.1</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id6">1.1.0</a></li>
<li class="toctree-l2"><a class="reference internal" href="../ReleaseNotes/index.html#id7">1.0.0</a></li>
</ul>
</li>
<li class="toctree-l1 current"><a class="current reference internal" href="">Compiling</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#windows">Windows</a></li>
<li class="toctree-l2"><a class="reference internal" href="#linux">Linux</a></li>
<li class="toctree-l2"><a class="reference internal" href="#mac">Mac</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ios">iOS</a></li>
<li class="toctree-l2"><a class="reference internal" href="#android">Android</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../Modules/Index.html">Modules</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../Modules/Index.html#nvcloth">NvCloth</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Modules/Index.html#nvcloth-extensions">NvCloth extensions</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../UserGuide/Index.html">User Guide</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../UserGuide/Index.html#setup">Setup</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#initializing-the-library">Initializing the Library</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#factory">Factory</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#fabric-cloth">Fabric &amp; Cloth</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../UserGuide/Index.html#fabric">Fabric</a></li>
<li class="toctree-l4"><a class="reference internal" href="../UserGuide/Index.html#cloth">Cloth</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#solver">Solver</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#retrieving-simulation-data">Retrieving simulation data</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../UserGuide/Index.html#usage">Usage</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#common-cloth-properties">Common cloth properties</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#tethers">Tethers</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#collision-detection">Collision detection</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#local-space-simulation">Local space simulation</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#drag-lift-and-wind">Drag lift and wind</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#distance-motion-constraints">Distance/Motion constraints</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#attaching-cloth-to-animated-characters">Attaching cloth to animated characters</a></li>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#unit-scaling">Unit scaling</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../UserGuide/Index.html#troubleshooting">Troubleshooting</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../UserGuide/Index.html#parts-of-cloth-disappearing-for-single-frame">Parts of cloth disappearing (for single frame)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../CopyRight/Index.html">NVIDIA Copyright Notice</a></li>
<li class="toctree-l1"><a class="reference internal" href="../Solver/Index.html">Internal solver function/algorithm documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#overview">Overview</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#particle-invmass-w-component">Particle invMass w component</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#slack">Slack</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#log-stiffness">Log Stiffness</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#integration">Integration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#wind-simulation">Wind simulation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#distance-constraints">Distance constraints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#tether-constraints">Tether constraints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#edge-constraints">Edge constraints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#separation-constraints">Separation constraints</a></li>
<li class="toctree-l2"><a class="reference internal" href="../Solver/Index.html#fabric-data-structure">Fabric data structure</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../CollisionDetection/Index.html">Internal collision detection documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../CollisionDetection/Index.html#overview-of-the-different-modules">Overview of the different modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html">Sphere Capsule collision detection</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#sphere-capsule-generation">Sphere/ Capsule generation</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#sphere-acceleration-structure">Sphere acceleration structure</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#collideparticles">collideParticles()</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#capsule-collision-detection">Capsule collision detection</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#cone-collision-detection">Cone collision detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#sphere-collision-detection">Sphere collision detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#sphere-ccd">Sphere CCD</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#cone-ccd">Cone CCD</a></li>
</ul>
</li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SphereCapsuleCollision.html#calculatefrictionimpulse">calculateFrictionImpulse()</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../CollisionDetection/SelfCollision.html">Self Collision</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#acceleration-structure">Acceleration structure</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#grid-setup">Grid setup</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#particle-sorting">Particle sorting</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#key-range-sweep">Key range sweep</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/SelfCollision.html#collision-detection-and-response">Collision detection and response</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../CollisionDetection/InterCollision.html">Inter Collision</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/InterCollision.html#overview">Overview</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/InterCollision.html#broad-phase-collision-detection">Broad phase collision detection</a></li>
<li class="toctree-l4"><a class="reference internal" href="../CollisionDetection/InterCollision.html#acceleration-structure">Acceleration structure</a><ul>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/InterCollision.html#id1">Broad phase collision detection</a></li>
<li class="toctree-l5"><a class="reference internal" href="../CollisionDetection/InterCollision.html#differences-with-self-collision">Differences with self collision</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../CollisionDetection/Index.html#todo">Todo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../Cooking/Index.html">Internal cooking documentation</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../Cooking/Index.html#overview-of-the-different-modules">Overview of the different modules</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../Cooking/TripletScheduler.html">TripletScheduler</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../Cooking/TripletScheduler.html#adjacencyquerier">AdjacencyQuerier</a></li>
<li class="toctree-l4"><a class="reference internal" href="../Cooking/TripletScheduler.html#id1">TripletScheduler</a></li>
<li class="toctree-l4"><a class="reference internal" href="../Cooking/TripletScheduler.html#tripletscheduler-simd">TripletScheduler::simd()</a></li>
<li class="toctree-l4"><a class="reference internal" href="../Cooking/TripletScheduler.html#tripletscheduler-warp">TripletScheduler::warp()</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>

  </div>
  <h4>Previous topic</h4>
  <p class="topless"><a href="../ReleaseNotes/index.html"
                        title="previous chapter">Release Notes</a></p>
  <h4>Next topic</h4>
  <p class="topless"><a href="../Modules/Index.html"
                        title="next chapter">Modules</a></p>
<div id="searchbox" style="display: none">
  <h4>Quick search</h4>
    <form class="search form-inline" action="../search.html" method="get">
      <div class="form-group">
      <input type="text" name="q" class="form-control" />
      <input type="submit" value="Search" class="btn btn-primary" />
      </div>
      <input type="hidden" name="check_keywords" value="yes" />
      <input type="hidden" name="area" value="default" />
    </form>
    <p class="searchtip" style="font-size: 90%">
    Enter search terms or a module, class or function name.
    </p>
</div>
<script type="text/javascript">$('#searchbox').show(0);</script>
</div>
  </div>
    <div class="document col-md-8">
          <div class="body">
            
  <div class="section" id="compiling">
<h1>Compiling<a class="headerlink" href="#compiling" title="Permalink to this headline">¶</a></h1>
<p>Cmake is automatically obtained using nvidia&#8217;s packman scripts for all platforms (except for Android).
Internet connection is required for this.</p>
<div class="section" id="windows">
<h2>Windows<a class="headerlink" href="#windows" title="Permalink to this headline">¶</a></h2>
<p>For windows: Download and install the following dependencies:</p>
<ul class="simple">
<li>Visual studio 12 or later (2013 or later)</li>
<li>Windows 8.1 sdk <a class="reference external" href="https://developer.microsoft.com/en-us/windows/downloads/windows-8-1-sdk">https://developer.microsoft.com/en-us/windows/downloads/windows-8-1-sdk</a></li>
<li>CUDA sdk <a class="reference external" href="https://developer.nvidia.com/cuda-downloads">https://developer.nvidia.com/cuda-downloads</a> (version 8 or later)</li>
</ul>
<p>Edit <tt class="code docutils literal"><span class="pre">scripts/locate_cuda.bat</span></tt> to point to the CUDA installation folder (that contains the bin, include and lib folder), edit <tt class="code docutils literal"><span class="pre">scripts/locate_win8sdk.bat</span></tt> to point to the windows SDK (<tt class="code docutils literal"><span class="pre">C:\Program</span> <span class="pre">Files</span> <span class="pre">(x86)\Windows</span> <span class="pre">Kits\8.1</span></tt> by default)</p>
<p>Run <tt class="code docutils literal"><span class="pre">CmakeGenerateAll.bat</span></tt> to generate the visual studio solution files in <tt class="code docutils literal"><span class="pre">compiler/vcXXwinXX-cmake/</span></tt>.
For example Visual Studio 2015 solution with 64-bit target processor will be placed to <tt class="code docutils literal"><span class="pre">compiler/vc14win64-cmake/</span></tt> folder.
Then open <tt class="code docutils literal"><span class="pre">NvCloth.sln</span></tt>, choose the release/debug configuration and build the solution. The library binaries will be put to the <tt class="code docutils literal"><span class="pre">bin</span></tt> folder next to the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.</p>
<p>The above project generation script has optional command line arguments to control whether CUDA/DX11 solvers should be enabled:</p>
<div class="highlight-python"><pre>CmakeGenerateProjects.bat &lt;0|1:use_cuda (default is 1)&gt; &lt;0|1:use_dx11 (default is 1)&gt;</pre>
</div>
<p>E.g.:</p>
<div class="highlight-python"><pre>CmakeGenerateProjects.bat 1 0</pre>
</div>
<p>will include CUDA solver into the compilation but exclude the DX11 solver.</p>
<p>Run <tt class="code docutils literal"><span class="pre">samples/CmakeGenerateProjects.bat</span></tt> to generate the visual studio solution files for the samples in <tt class="code docutils literal"><span class="pre">samples/compiler/vc14winXX-cmake/CmakeGenerateProjects.bat</span></tt>.
You need to build assimp first before building the samples. Run <tt class="code docutils literal"><span class="pre">E:\nx0\sw\devrel\libdev\NvCloth\trunk\samples\external\assimp-4.1.0</span></tt> and build <tt class="code docutils literal"><span class="pre">samples\external\assimp-4.1.0\compiler\vc14winXX-cmake\Assimp.sln</span></tt></p>
</div>
<div class="section" id="linux">
<h2>Linux<a class="headerlink" href="#linux" title="Permalink to this headline">¶</a></h2>
<p>Run <tt class="code docutils literal"><span class="pre">GenerateProjectsLinux.sh</span></tt> to generate make files in <tt class="code docutils literal"><span class="pre">compiler/linux64-XXXXX-cmake/</span></tt></p>
<p>Enable or disable CUDA support by simply changing the following line in the <tt class="code docutils literal"><span class="pre">BuildProjectsLinux.sh</span></tt> script included to the distribution:</p>
<div class="highlight-python"><pre>export USE_CUDA=0 # 0 for disable 1 for enable</pre>
</div>
<p>Run <tt class="code docutils literal"><span class="pre">BuildProjectsLinux.sh</span></tt> script. It will generate various makefile-based projects in the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.
Choose the one you need, e.g.:</p>
<div class="highlight-python"><pre>cd compiler/linux64-release-cmake</pre>
</div>
<p>then run the build process (using 5 compilation processes in parallel):</p>
<div class="highlight-python"><div class="highlight"><pre><span class="n">make</span> <span class="o">--</span><span class="n">jobs</span><span class="o">=</span><span class="mi">5</span>
</pre></div>
</div>
<p>The library binaries will be put to the <tt class="code docutils literal"><span class="pre">bin</span></tt> folder next to the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.</p>
<p><strong>Note:</strong> In case you run the Linux project generation scripts <tt class="code docutils literal"><span class="pre">GenerateProjectsLinux.sh</span></tt> and see an error like this:</p>
<div class="highlight-python"><pre>env: bash\r: No such file or directory</pre>
</div>
<p>this command will help to fix the line endings:</p>
<div class="highlight-python"><pre>sed $'s/\r$//' GenerateProjectsLinux.sh &gt; GenerateProjectsLinux.sh.fixed &amp;&amp; mv GenerateProjectsLinux.sh.fixed GenerateProjectsLinux.sh &amp;&amp; chmod +x GenerateProjectsLinux.sh</pre>
</div>
<p>or use your favorite text editor.</p>
</div>
<div class="section" id="mac">
<h2>Mac<a class="headerlink" href="#mac" title="Permalink to this headline">¶</a></h2>
<p>Running <tt class="code docutils literal"><span class="pre">GenerateProjectsOsx.sh</span></tt> will generate xcode-based projects (32-bit and 64-bit) in the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.
Choose the one you need, e.g. <tt class="code docutils literal"><span class="pre">cd</span> <span class="pre">compiler/osx64-cmake</span></tt>, open the project in XCode and build inside the IDE.
Alternatively you can use <cite>BuildProjectsOsx.sh</cite> to build everything from the command line.</p>
<p>The library binaries will be put to the <tt class="code docutils literal"><span class="pre">bin</span></tt> folder next to the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.</p>
<p><strong>Note:</strong> In case if you run the Mac project generation scripts GenerateProjectsOsx.sh and see an error like this:</p>
<div class="highlight-python"><pre>env: bash\r: No such file or directory</pre>
</div>
<p>this command will help to fix the line endings:</p>
<div class="highlight-python"><pre>sed $'s/\r$//' GenerateProjectsOsx.sh &gt; GenerateProjectsOsx.sh.fixed &amp;&amp; mv GenerateProjectsOsx.sh.fixed GenerateProjectsOsx.sh &amp;&amp; chmod +x GenerateProjectsOsx.sh</pre>
</div>
</div>
<div class="section" id="ios">
<h2>iOS<a class="headerlink" href="#ios" title="Permalink to this headline">¶</a></h2>
<p>Running <tt class="code docutils literal"><span class="pre">GenerateProjectsIOS.sh</span></tt> will generate xcode-based project in the <tt class="code docutils literal"><span class="pre">compiler/ios-cmake</span></tt> folder.
After the generation is done, just open the project in XCode and build inside the IDE.</p>
<p>The target iOS deployment version is 8.0 by default (can be changed inside the project generator script).</p>
<p>Alternatively you can use <cite>BuildProjectsIOS.sh</cite> to build everything from the command line.</p>
</div>
<div class="section" id="android">
<h2>Android<a class="headerlink" href="#android" title="Permalink to this headline">¶</a></h2>
<p>Download and install CMake 3.7 from <a class="reference external" href="https://cmake.org/download/">https://cmake.org/download/</a>. Edit <tt class="code docutils literal"><span class="pre">scripts/locate_cmake.bat</span></tt> to point to the cmake executable.</p>
<p>Download NDK from <a class="reference external" href="https://developer.android.com/ndk/downloads/index.html">https://developer.android.com/ndk/downloads/index.html</a> (tested versions are r15c, r13b, r12b). Set <tt class="code docutils literal"><span class="pre">ANDROID_NDK_ROOT</span></tt> environment variable to point to the root NDK folder.
Run project generation script:</p>
<div class="highlight-python"><pre>CmakeGenerateAndroid.bat release</pre>
</div>
<p>You need <tt class="code docutils literal"><span class="pre">cmake</span></tt> to be in your PATH.</p>
<p>Go to <tt class="code docutils literal"><span class="pre">compiler\android-arm64-v8a-release-cmake</span></tt> folder and run:</p>
<div class="highlight-python"><pre>cmake --build .  -- -j5</pre>
</div>
<p>The library binaries will be put to the <tt class="code docutils literal"><span class="pre">bin</span></tt> folder next to the <tt class="code docutils literal"><span class="pre">compiler</span></tt> folder.</p>
<p><strong>Note:</strong> To be able to build the Android binaries using CMake you need to make sure a certain folders in your NDK point to needed binaries.
Specifically, you need to create a symlink/copy/rename:</p>
<div class="highlight-python"><pre>&lt;path_to_android_ndk&gt;/toolchains/aarch64-linux-android-4.9/prebuilt/windows</pre>
</div>
<p>to:</p>
<div class="highlight-python"><pre>&lt;path_to_android_ndk&gt;/toolchains/aarch64-linux-android-4.9/prebuilt/windows-x86_64</pre>
</div>
<p>Newer NDK versions might not require doing that at all, but some versions do.</p>
</div>
</div>


          </div>
      <div class="clearer"></div>
    </div>
    <div class="col-md-1"></div>
</div>
<div class="masthead">
    <div class="row">
      <ul class="breadcrumb">
        <li><a href="../index.html">NvCloth 1.1.5 documentation</a></li> 
      </ul>
    </div>
</div>

<footer>
    <div class="footer-boilerplate">
        <div class="row">
            <div class="boilerplate">
                Copyright &copy; 2019, NVIDIA Corporation &nbsp; | &nbsp; <a href="http://www.nvidia.com/object/about-nvidia.html" onclick="s_objectID=&quot;http://www.nvidia.com/object/about-nvidia.html_1&quot;;return this.s_oc?this.s_oc(e):true">About NVIDIA </a>&nbsp; | &nbsp; <a href="http://www.nvidia.com/object/legal_info.html" onclick="s_objectID=&quot;http://www.nvidia.com/object/legal_info.html_1&quot;;return this.s_oc?this.s_oc(e):true">Legal Information </a>&nbsp; | &nbsp; <a href="http://www.nvidia.com/object/privacy_policy.html" onclick="s_objectID=&quot;http://www.nvidia.com/object/privacy_policy.html_1&quot;;return this.s_oc?this.s_oc(e):true">Privacy Policy </a>
            </div>
        </div>
    </div>
</div>
</footer>


<script type="text/x-mathjax-config">
  MathJax.Hub.Config({
    extensions: ["tex2jax.js"],
    jax: ["input/TeX", "output/HTML-CSS"],
    tex2jax: {
      processEscapes: true,
      skipTags: ["script","noscript","style","textarea"]
    },
    "HTML-CSS": { availableFonts: ["TeX"] },
    TeX: {
        Macros: {
          Lrg: ['\\displaystyle{#1}', 1, ""]
        }
      }
  });
</script>


<script type="text/javascript" async
  src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-MML-AM_CHTML">
</script>

<script>
var treestatename = 'GWDocsTreeState';
var protocol = location.href.split('/')[0].toLowerCase();
var storage;
if (protocol.substring(0,4) == 'http') {
  storage = $.cookieStorage;
  storage.setPath('/');
} else {
  storage = $.localStorage;
}

if (storage.isEmpty(treestatename)) {
  storage.set(treestatename, {});
}

var treestate = storage.get(treestatename);

$.each($("#sidebar_toc ul li"), toc_walker);

function toc_walker(key, value) {
    var handleSpan = $("<span></span>")
        .addClass("toc_handle").prependTo(value);
    handleSpan.attr("id", $(value).closest("div").attr("id") + "." + key);

    if($(value).has("ul li").size() > 0) {
        var id = handleSpan.attr("id");
        if (!(id in treestate)) {
          treestate[id] = false;
        }
        handleSpan.addClass("toc_expanded").click(function() {
            $(this).toggleClass("toc_expanded toc_collapsed").siblings("ul").toggle();
            treestate[$(this).attr('id')] = $(this).hasClass('toc_expanded');
            storage.set(treestatename, treestate);
        });
        if(!($(this).hasClass('current') || treestate[id])) {
            handleSpan.click();
        }
        if($(this).hasClass('current')) {
            treestate[handleSpan.attr('id')] = handleSpan.hasClass('toc_expanded');
            storage.set(treestatename, treestate);
        }
    }
}
</script>
  </body>
</html>