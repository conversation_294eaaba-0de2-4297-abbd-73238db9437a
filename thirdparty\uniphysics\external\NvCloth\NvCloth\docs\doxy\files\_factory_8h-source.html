<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Factory.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Factory.h</h1><a href="_factory_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include "<a class="code" href="_range_8h.html">NvCloth/Range.h</a>"</span>
<a name="l00033"></a>00033 <span class="preprocessor">#include &lt;foundation/PxVec4.h&gt;</span>
<a name="l00034"></a>00034 <span class="preprocessor">#include &lt;foundation/PxVec3.h&gt;</span>
<a name="l00035"></a>00035 <span class="preprocessor">#include "<a class="code" href="_allocator_8h.html" title="This file together with Callbacks.h define most memory management interfaces for...">NvCloth/Allocator.h</a>"</span>
<a name="l00036"></a>00036 
<a name="l00037"></a><a class="code" href="_factory_8h.html#f9f5bd81658f866613785b3a0bb7d7d9">00037</a> <span class="keyword">typedef</span> <span class="keyword">struct </span>CUctx_st* <a class="code" href="_factory_8h.html#f9f5bd81658f866613785b3a0bb7d7d9">CUcontext</a>;
<a name="l00038"></a>00038 
<a name="l00039"></a>00039 <span class="keyword">namespace </span>nv
<a name="l00040"></a>00040 {
<a name="l00041"></a>00041 <span class="keyword">namespace </span>cloth
<a name="l00042"></a>00042 {
<a name="l00043"></a>00043 <span class="keyword">class </span>DxContextManagerCallback;
<a name="l00044"></a>00044 <span class="keyword">class </span>Factory;
<a name="l00045"></a>00045 }
<a name="l00046"></a>00046 }
<a name="l00047"></a>00047 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<a class="code" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">nv::cloth::Factory</a>*) NvClothCreateFactoryCPU();
<a name="l00048"></a>00048 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(nv::cloth::Factory*) NvClothCreateFactoryCUDA(<a class="code" href="_factory_8h.html#f9f5bd81658f866613785b3a0bb7d7d9">CUcontext</a>);
<a name="l00049"></a>00049 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(nv::cloth::Factory*) NvClothCreateFactoryDX11(nv::cloth::DxContextManagerCallback*);
<a name="l00050"></a>00050 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<span class="keywordtype">void</span>) NvClothDestroyFactory(nv::cloth::Factory*);
<a name="l00051"></a>00051 
<a name="l00053"></a>00053 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<span class="keywordtype">bool</span>) NvClothCompiledWithCudaSupport();
<a name="l00055"></a>00055 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<span class="keywordtype">bool</span>) NvClothCompiledWithDxSupport();
<a name="l00056"></a>00056 
<a name="l00057"></a>00057 namespace nv
<a name="l00058"></a>00058 {
<a name="l00059"></a>00059 <span class="keyword">namespace </span>cloth
<a name="l00060"></a>00060 {
<a name="l00061"></a>00061 
<a name="l00062"></a>00062 <span class="keyword">class </span>Fabric;
<a name="l00063"></a>00063 <span class="keyword">class </span>Cloth;
<a name="l00064"></a>00064 <span class="keyword">class </span>Solver;
<a name="l00065"></a>00065 
<a name="l00066"></a>00066 <span class="keyword">enum struct</span> Platform
<a name="l00067"></a>00067 {
<a name="l00068"></a>00068     CPU,
<a name="l00069"></a>00069     CUDA,
<a name="l00070"></a>00070     DX11
<a name="l00071"></a>00071 };
<a name="l00072"></a>00072 
<a name="l00075"></a><a class="code" href="classnv_1_1cloth_1_1_factory.html">00075</a> <span class="keyword">class </span>Factory : <span class="keyword">public</span> UserAllocated
<a name="l00076"></a>00076 {
<a name="l00077"></a>00077   <span class="keyword">protected</span>:
<a name="l00078"></a><a class="code" href="classnv_1_1cloth_1_1_factory.html#0fe2d6c93b96ccbf2a38b5d7a0c55949">00078</a>     <a class="code" href="classnv_1_1cloth_1_1_factory.html#0fe2d6c93b96ccbf2a38b5d7a0c55949">Factory</a>() {}
<a name="l00079"></a>00079     Factory(<span class="keyword">const</span> Factory&amp;);
<a name="l00080"></a>00080     Factory&amp; operator = (<span class="keyword">const</span> Factory&amp;);
<a name="l00081"></a><a class="code" href="classnv_1_1cloth_1_1_factory.html#edab1136aacbffe558d8b00be6cb2257">00081</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_factory.html#edab1136aacbffe558d8b00be6cb2257">~Factory</a>() {}
<a name="l00082"></a>00082 
<a name="l00083"></a>00083     <span class="keyword">friend</span> <a class="code" href="_callbacks_8h.html#bd597bda23283ca6fe84282f6e2671dc">NV_CLOTH_IMPORT</a> <span class="keywordtype">void</span> NV_CLOTH_CALL_CONV ::NvClothDestroyFactory(<a class="code" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">nv::cloth::Factory</a>*);
<a name="l00084"></a>00084 
<a name="l00085"></a>00085   <span class="keyword">public</span>:
<a name="l00086"></a>00086     <span class="keyword">virtual</span> Platform getPlatform() <span class="keyword">const</span> = 0;
<a name="l00087"></a>00087 
<a name="l00098"></a>00098     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>* createFabric(uint32_t numParticles, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> phaseIndices, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> sets,
<a name="l00099"></a>00099                                  <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> restvalues, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> stiffnessValues, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> indices,
<a name="l00100"></a>00100                                  <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> anchors, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> tetherLengths,
<a name="l00101"></a>00101                                  <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> triangles) = 0;
<a name="l00102"></a>00102 
<a name="l00108"></a>00108     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* createCloth(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec4&gt;</a> particles, <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; fabric) = 0;
<a name="l00109"></a>00109 
<a name="l00113"></a>00113     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a>* createSolver() = 0;
<a name="l00114"></a>00114 
<a name="l00119"></a>00119     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* clone(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth) = 0;
<a name="l00120"></a>00120 
<a name="l00130"></a>00130     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractFabricData(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; fabric, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> phaseIndices, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> sets,
<a name="l00131"></a>00131                                    <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;float&gt;</a> restvalues, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;float&gt;</a> stiffnessValues, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> indices, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> anchors,
<a name="l00132"></a>00132                                    <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;float&gt;</a> tetherLengths, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> triangles) <span class="keyword">const</span> = 0;
<a name="l00133"></a>00133 
<a name="l00144"></a>00144     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractCollisionData(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> spheres, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> capsules,
<a name="l00145"></a>00145                                       <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> planes, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> convexes, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec3&gt;</a> triangles) <span class="keyword">const</span> = 0;
<a name="l00146"></a>00146 
<a name="l00153"></a>00153     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractMotionConstraints(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> destConstraints) <span class="keyword">const</span> = 0;
<a name="l00154"></a>00154 
<a name="l00160"></a>00160     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractSeparationConstraints(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> destConstraints) <span class="keyword">const</span> = 0;
<a name="l00161"></a>00161 
<a name="l00167"></a>00167     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractParticleAccelerations(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> destAccelerations) <span class="keyword">const</span> = 0;
<a name="l00168"></a>00168 
<a name="l00175"></a>00175     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractVirtualParticles(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;uint32_t[4]&gt; destIndices,
<a name="l00176"></a>00176                                          <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec3&gt;</a> destWeights) <span class="keyword">const</span> = 0;
<a name="l00177"></a>00177 
<a name="l00183"></a>00183     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractSelfCollisionIndices(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;uint32_t&gt;</a> destIndices) <span class="keyword">const</span> = 0;
<a name="l00184"></a>00184 
<a name="l00190"></a>00190     <span class="keyword">virtual</span> <span class="keywordtype">void</span> extractRestPositions(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> destRestPositions) <span class="keyword">const</span> = 0;
<a name="l00191"></a>00191 };
<a name="l00192"></a>00192 
<a name="l00193"></a>00193 } <span class="comment">// namespace cloth</span>
<a name="l00194"></a>00194 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
