var node0 = ::geograph::nodes::brush_to_gltf::BrushToGltf()
node0.query_param("adjacencies").value = false

_editor.add_node(node0, -20.676023581753, -291.10174525378)

var node1 = ::blueprint::nodes::fetch::Fetch()
node1.index = "node0"

_editor.add_node(node1, 356.86483479879, -474.88694907861)

var node2 = ::blueprint::nodes::fetch::Fetch()
node2.index = "va"

_editor.add_node(node2, 591.97377684355, -478.38435483226)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "geos"
node3.var_type = "array"

_editor.add_node(node3, 595.46840404795, 245.77793726673)

var node4 = ::rendergraph::nodes::draw::Draw()

node4.set_prim_type("triangles")
node4.render_state = { "stencil_test" : false, "rasterization" : "fill", "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "lequal", "clip_plane" : false }
node4.skip = false

_editor.add_node(node4, 811.40380359571, -312.01447758435)

var node5 = ::rendergraph::nodes::shader::Shader()
node5.query_param("inc_dir").value = ""

node5.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;

out VS_OUT {
    vec3 frag_pos;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;
};

void main()
{
	vs_out.frag_pos = vec3(model * vec4(aPos, 1.0));
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"
node5.tcs = ""
node5.tes = ""
node5.gs = ""
node5.fs = "
#version 330 core
out vec4 FragColor;

in VS_OUT {
    vec3 frag_pos;
} fs_in;

uniform UBO
{
	vec3 light_pos;
    vec3 light_col;
	vec3 cam_pos;
};

void main()
{
 	vec3 dFdxPos = dFdx( fs_in.frag_pos );
    vec3 dFdyPos = dFdy( fs_in.frag_pos );
    vec3 N = normalize( cross(dFdxPos,dFdyPos ));

	// ambient
	vec3 ambient = vec3(0.25) * light_col;

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * vec3(1.0);

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(N, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    FragColor = vec4(ambient + diffuse + specular, 1.0); 
}
"
node5.cs = ""
node5.render_gen()

_editor.add_node(node5, 602.43667455228, -169.4677095474)

var node6 = ::blueprint::nodes::perspective::Perspective()

node6.fovy = 45
node6.aspect = 0
node6.znear = 0.1
node6.zfar = 100

_editor.add_node(node6, 413.60400748731, -99.260075321102)

var node7 = ::blueprint::nodes::proxy::Proxy()

node7.real_name = "view_cam"
node7.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node7, 409.76615286487, 48.85559416076)

var node8 = ::blueprint::nodes::number3::Number3()

node8.value.set(5.9766573905945, 5.6433238983154, 5.3099908828735)

_editor.add_node(node8, 413.41918712361, -232.35700607071)

var node9 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node9, 1204.9441409146, 267.91360697397)

var node10 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node10, 188.36502375197, -214.470801618)

var node11 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node11, 1011.9389355872, 20.226619751981)

var node12 = ::geograph::nodes::draw_geometry::DrawGeometry()
node12.query_param("skip").value = false

_editor.add_node(node12, 783.1307712437, 32.615556424757)

var node13 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node13, 560.9265762203, 104.9157175414)

var node14 = ::blueprint::nodes::list_flatten::ListFlatten()

_editor.add_node(node14, 737.37665349787, 245.690715443)

var node15 = ::blueprint::nodes::cache::Cache()
node15.query_param("disable").value = false

_editor.add_node(node15, 889.19487970526, 242.96342052113)

var node16 = ::blueprint::nodes::input::Input()

node16.var_name = "light_col"
node16.var_type = "num3"

_editor.add_node(node16, 196.35289695046, -57.101640181109)

var node17 = ::blueprint::nodes::fetch::Fetch()
node17.index = "node1"

_editor.add_node(node17, 849.80643479879, -628.79997907861)

var node18 = ::blueprint::nodes::fetch::Fetch()
node18.index = "va"

_editor.add_node(node18, 1066.2153768436, -628.99738483226)

var node19 = ::rendergraph::nodes::draw::Draw()

node19.set_prim_type("lines")
node19.render_state = { "stencil_test" : false, "rasterization" : "fill", "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "lequal", "clip_plane" : false }
node19.skip = false

_editor.add_node(node19, 1275.6454035957, -676.62750758435)

var node20 = ::rendergraph::nodes::shader::Shader()
node20.query_param("inc_dir").value = ""

node20.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;

out VS_OUT {
    vec3 frag_pos;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;
};

void main()
{
	vs_out.frag_pos = vec3(model * vec4(aPos, 1.0));
    gl_Position = projection * view * model * vec4(aPos, 1.0);
}
"
node20.tcs = ""
node20.tes = ""
node20.gs = "
#version 330 core

layout (lines) in;                              // now we can access 2 vertices
layout (triangle_strip, max_vertices = 4) out;  // always (for now) producing 2 triangles (so 4 vertices)

uniform UBO
{
    vec2  viewport_size;
    float thickness;
};

void main()
{
    vec4 p1 = gl_in[0].gl_Position;
    vec4 p2 = gl_in[1].gl_Position;

    vec2 dir    = normalize((p2.xy/p2.w - p1.xy/p1.w) * viewport_size);
    vec2 offset = vec2(-dir.y, dir.x) * thickness / viewport_size;

    gl_Position = p1 + vec4(offset.xy * p1.w, 0.0, 0.0);
    EmitVertex();
    gl_Position = p1 - vec4(offset.xy * p1.w, 0.0, 0.0);
    EmitVertex();
    gl_Position = p2 + vec4(offset.xy * p2.w, 0.0, 0.0);
    EmitVertex();
    gl_Position = p2 - vec4(offset.xy * p2.w, 0.0, 0.0);
    EmitVertex();

    EndPrimitive();
}
"
node20.fs = "
#version 330 core
out vec4 FragColor;

uniform UBO
{
    vec3 line_color;
};

void main()
{
    FragColor = vec4(line_color, 1.0); 
}
"
node20.cs = ""
node20.render_gen()

_editor.add_node(node20, 1075.1314388523, -319.0427549474)

var node21 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node21, 856.35450601119, -99.975903894771)

var node22 = ::blueprint::nodes::number::Number()

node22.value = 4

_editor.add_node(node22, 887.09005012145, -177.15644749819)

var node23 = ::blueprint::nodes::input::Input()

node23.var_name = "line_color"
node23.var_type = "num3"

_editor.add_node(node23, 846.49091796875, -505.39866333008)

Blueprint.connect(node15, "var", node9, "in")
Blueprint.connect(node11, "next", node9, "do")
Blueprint.connect(node9, "out", node12, "geos")
Blueprint.connect(node7, "mat", node12, "cam_mat")
Blueprint.connect(node7, "zoom", node6, "fovy")
Blueprint.connect(node7, "mat", node20, "view")
Blueprint.connect(node6, "mat", node20, "projection")
Blueprint.connect(node21, "size", node20, "viewport_size")
Blueprint.connect(node22, "v", node20, "thickness")
Blueprint.connect(node23, "var", node20, "line_color")
Blueprint.connect(node6, "mat", node13, "a")
Blueprint.connect(node7, "mat", node13, "b")
Blueprint.connect(node7, "mat", node5, "view")
Blueprint.connect(node6, "mat", node5, "projection")
Blueprint.connect(node8, "v3", node5, "light_pos")
Blueprint.connect(node16, "var", node5, "light_col")
Blueprint.connect(node7, "pos", node5, "cam_pos")
Blueprint.connect(node3, "var", node14, "list")
Blueprint.connect(node14, "list", node15, "var")
Blueprint.connect(node9, "out", node0, "brush")
Blueprint.connect(node0, "gltf", node17, "items")
Blueprint.connect(node17, "item", node18, "items")
Blueprint.connect(node0, "gltf", node10, "in")
Blueprint.connect(node10, "out", node11, "cond")
Blueprint.connect(node12, "next", node11, "true")
Blueprint.connect(node19, "next", node11, "false")
Blueprint.connect(node0, "gltf", node1, "items")
Blueprint.connect(node1, "item", node2, "items")
Blueprint.connect(node5, "out", node4, "shader")
Blueprint.connect(node2, "item", node4, "va")
Blueprint.connect(node4, "next", node19, "prev")
Blueprint.connect(node20, "out", node19, "shader")
Blueprint.connect(node18, "item", node19, "va")
