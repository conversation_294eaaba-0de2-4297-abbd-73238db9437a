var node0 = ::blueprint::nodes::output::Output()

node0.var_name = "NoV"
node0.var_type = "num"

_editor.add_node(node0, 192.19250256674, -86.118724298334)

var node1 = ::blueprint::nodes::subgraph::Subgraph()
node1.load_from_file(_editor, "../shadergraph/clamped_dot.ves")

_editor.add_node(node1, -3.1818181818184, -87.909090909091)

var node2 = ::blueprint::nodes::subgraph::Subgraph()
node2.load_from_file(_editor, "../shadergraph/clamped_dot.ves")

_editor.add_node(node2, -6.5454545454549, -202.90909090909)

var node3 = ::blueprint::nodes::subgraph::Subgraph()
node3.load_from_file(_editor, "../shadergraph/clamped_dot.ves")

_editor.add_node(node3, 13.181818181818, 43.363636363636)

var node4 = ::blueprint::nodes::output::Output()

node4.var_name = "NoL"
node4.var_type = "num"

_editor.add_node(node4, 182.93922254985, -203.92339486853)

var node5 = ::blueprint::nodes::output::Output()

node5.var_name = "NoH"
node5.var_type = "num"

_editor.add_node(node5, 205.60278874938, 48.132351447859)

var node6 = ::blueprint::nodes::subgraph::Subgraph()
node6.load_from_file(_editor, "../shadergraph/clamped_dot.ves")

_editor.add_node(node6, -13.181818181818, -337.36363636363)

var node7 = ::blueprint::nodes::output::Output()

node7.var_name = "VoH"
node7.var_type = "num"

_editor.add_node(node7, 186.44183973323, -322.32036228878)

var node8 = ::blueprint::nodes::subgraph::Subgraph()
node8.load_from_file(_editor, "../shadergraph/clamped_dot.ves")

_editor.add_node(node8, -5.9090909090911, -474.17328051362)

var node9 = ::blueprint::nodes::output::Output()

node9.var_name = "LoH"
node9.var_type = "num"

_editor.add_node(node9, 184.20705196573, -451.6806689233)

var node10 = ::blueprint::nodes::input::Input()

node10.var_name = "normal"
node10.var_type = "num3"

_editor.add_node(node10, -706.05795329447, 117.52047634661)

var node11 = ::blueprint::nodes::input::Input()

node11.var_name = "world_pos"
node11.var_type = "num3"

_editor.add_node(node11, -1093.7649405222, -221.4515747428)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "cam_pos"
node12.var_type = "num3"

_editor.add_node(node12, -1094.8859773366, 5.3572156403701)

var node13 = ::blueprint::nodes::input::Input()

node13.var_name = "light_pos"
node13.var_type = "num3"

_editor.add_node(node13, -1091.77258158, -112.06976277019)

var node14 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node14, -895.7847283649, -64.8481663518)

var node15 = ::shadergraph::nodes::normalize::Normalize()

_editor.add_node(node15, -735.67953457106, -55.15621032109)

var node16 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node16, -906.6624039176, -195.66429356599)

var node17 = ::shadergraph::nodes::normalize::Normalize()

_editor.add_node(node17, -738.89378869926, -189.05272584017)

var node18 = ::blueprint::nodes::add::Add()

_editor.add_node(node18, -883.5930688662, -378.77048108801)

var node19 = ::shadergraph::nodes::normalize::Normalize()

_editor.add_node(node19, -727.59306886617, -376.77048108801)

var node20 = ::blueprint::nodes::store::Store()

node20.var_name = "V"

_editor.add_node(node20, -564.56028463568, -55.93937835946)

var node21 = ::blueprint::nodes::store::Store()

node21.var_name = "L"

_editor.add_node(node21, -563.51233158333, -188.71210184901)

var node22 = ::blueprint::nodes::store::Store()

node22.var_name = "H"

_editor.add_node(node22, -566.81432287295, -377.36294569788)

var node23 = ::blueprint::nodes::load::Load()

node23.var_name = "V"

_editor.add_node(node23, -1071.7729952625, -326.12328510379)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "L"

_editor.add_node(node24, -1073.425864621, -393.89186312085)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "H"

_editor.add_node(node25, -205.71713813994, 28.445016305862)

var node26 = ::blueprint::nodes::load::Load()

node26.var_name = "H"

_editor.add_node(node26, -194.32945970267, -376.76856200163)

var node27 = ::blueprint::nodes::load::Load()

node27.var_name = "H"

_editor.add_node(node27, -193.97408780185, -521.38013224957)

var node28 = ::blueprint::nodes::store::Store()

node28.var_name = "N"

_editor.add_node(node28, -564.79831579292, 117.38965954489)

var node29 = ::blueprint::nodes::load::Load()

node29.var_name = "N"

_editor.add_node(node29, -205.44113789708, 92.27037762098)

var node30 = ::blueprint::nodes::load::Load()

node30.var_name = "N"

_editor.add_node(node30, -209.7841131037, -45.073424031912)

var node31 = ::blueprint::nodes::load::Load()

node31.var_name = "N"

_editor.add_node(node31, -199.4535345913, -169.61887857737)

var node32 = ::blueprint::nodes::load::Load()

node32.var_name = "V"

_editor.add_node(node32, -206.5029952625, -104.23964874015)

var node33 = ::blueprint::nodes::load::Load()

node33.var_name = "V"

_editor.add_node(node33, -193.95754071704, -312.33055783106)

var node34 = ::blueprint::nodes::load::Load()

node34.var_name = "L"

_editor.add_node(node34, -197.94098858794, -232.53715237705)

var node35 = ::blueprint::nodes::load::Load()

node35.var_name = "L"

_editor.add_node(node35, -192.81702164579, -457.99169783159)

var node36 = ::blueprint::nodes::output::Output()

node36.var_name = "N"
node36.var_type = "num3"

_editor.add_node(node36, -564.85606245561, 170.32626632816)

var node37 = ::blueprint::nodes::output::Output()

node37.var_name = "V"
node37.var_type = "num3"

_editor.add_node(node37, -565.68249663046, -6.5332722309226)

var node38 = ::blueprint::nodes::output::Output()

node38.var_name = "L"
node38.var_type = "num3"

_editor.add_node(node38, -564.85606396888, -137.9381929508)

var node39 = ::blueprint::nodes::output::Output()

node39.var_name = "H"
node39.var_type = "num3"

_editor.add_node(node39, -567.33541693569, -321.40926581769)

Blueprint.connect(node13, "var", node16, "a")
Blueprint.connect(node11, "var", node16, "b")
Blueprint.connect(node16, "v", node17, "v")
Blueprint.connect(node17, "v", node38, "var")
Blueprint.connect(node17, "v", node21, "var")
Blueprint.connect(node21, "var", node35, "var")
Blueprint.connect(node21, "var", node34, "var")
Blueprint.connect(node21, "var", node24, "var")
Blueprint.connect(node12, "var", node14, "a")
Blueprint.connect(node11, "var", node14, "b")
Blueprint.connect(node14, "v", node15, "v")
Blueprint.connect(node15, "v", node37, "var")
Blueprint.connect(node15, "v", node20, "var")
Blueprint.connect(node20, "var", node33, "var")
Blueprint.connect(node20, "var", node32, "var")
Blueprint.connect(node20, "var", node23, "var")
Blueprint.connect(node23, "var", node18, "a")
Blueprint.connect(node24, "var", node18, "b")
Blueprint.connect(node18, "v", node19, "v")
Blueprint.connect(node19, "v", node39, "var")
Blueprint.connect(node19, "v", node22, "var")
Blueprint.connect(node22, "var", node27, "var")
Blueprint.connect(node35, "var", node8, "a")
Blueprint.connect(node27, "var", node8, "b")
Blueprint.connect(node8, "dot", node9, "var")
Blueprint.connect(node22, "var", node26, "var")
Blueprint.connect(node33, "var", node6, "a")
Blueprint.connect(node26, "var", node6, "b")
Blueprint.connect(node6, "dot", node7, "var")
Blueprint.connect(node22, "var", node25, "var")
Blueprint.connect(node10, "var", node36, "var")
Blueprint.connect(node10, "var", node28, "var")
Blueprint.connect(node28, "var", node31, "var")
Blueprint.connect(node31, "var", node2, "a")
Blueprint.connect(node34, "var", node2, "b")
Blueprint.connect(node2, "dot", node4, "var")
Blueprint.connect(node28, "var", node30, "var")
Blueprint.connect(node30, "var", node1, "a")
Blueprint.connect(node32, "var", node1, "b")
Blueprint.connect(node1, "dot", node0, "var")
Blueprint.connect(node28, "var", node29, "var")
Blueprint.connect(node29, "var", node3, "a")
Blueprint.connect(node25, "var", node3, "b")
Blueprint.connect(node3, "dot", node5, "var")
