C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\buffer.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\buffer.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\chunk.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\chunk.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\compiler.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\compiler.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\core.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\core.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\debug.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\debug.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\memory.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\memory.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\object.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\object.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\optional\opt_io.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\opt_io.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\optional\opt_math.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\opt_math.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\optional\opt_random.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\opt_random.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\primitive.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\primitive.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\statistics.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\statistics.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\scanner.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\scanner.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\table.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\table.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\utils.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\utils.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\value.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\value.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\src\vm.c;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\vessel\build\msvc\projects\x64\Release\vessel\vm.obj
