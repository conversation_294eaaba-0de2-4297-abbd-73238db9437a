var node0 = ::blueprint::nodes::store::Store()

node0.var_name = "gear"

_editor.add_node(node0, 1156.3780326456, 516.78843122435)

var node1 = ::blueprint::nodes::subgraph::Subgraph()
node1.load_from_file(_editor, "../geograph/gear.ves")

_editor.add_node(node1, 66.807780471444, 461.37297775392)

var node2 = ::blueprint::nodes::input::Input()

node2.var_name = "radius"
node2.var_type = "num"

_editor.add_node(node2, -126.84638143829, 561.49936311956)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "count"
node3.var_type = "int"

_editor.add_node(node3, -123.7091243391, 496.09339296164)

var node4 = ::scenegraph::nodes::transform::Transform()

_editor.add_node(node4, 1016.4828015793, 488.21055756877)

var node5 = ::blueprint::nodes::load::Load()

node5.var_name = "gear"

_editor.add_node(node5, 763.57993552881, 308.84615047647)

var node6 = ::blueprint::nodes::output::Output()

node6.var_name = "gear"
node6.var_type = "sprite"

_editor.add_node(node6, 897.47258508511, 311.09035078478)

var node7 = ::physicsgraph::nodes::body::Body()
node7.query_param("type").value = "dynamic"
node7.query_param("gravity").value = 1
node7.query_param("density").value = 1.0230346918106
node7.query_param("restitution").value = 0
node7.query_param("friction").value = 0

_editor.add_node(node7, 233.30738708651, 174.7206299623)

var node8 = ::physicsgraph::nodes::revolute_joint::RevoluteJoint()
node8.query_param("limit").value = false
node8.query_param("lower").value = -0.1
node8.query_param("upper").value = 0.1
node8.query_param("motor").value = false
node8.query_param("torque").value = 10
node8.query_param("speed").value = 0

_editor.add_node(node8, 226.03527005118, -48.790720559557)

var node9 = ::blueprint::nodes::output::Output()

node9.var_name = "body"
node9.var_type = "body"

_editor.add_node(node9, 900.42393921724, 254.27318273027)

var node10 = ::blueprint::nodes::output::Output()

node10.var_name = "joint"
node10.var_type = "joint"

_editor.add_node(node10, 362.63605348758, 29.564169835916)

var node11 = ::blueprint::nodes::store::Store()

node11.var_name = "body"

_editor.add_node(node11, 372.26973726085, 224.27459939201)

var node12 = ::blueprint::nodes::load::Load()

node12.var_name = "body"

_editor.add_node(node12, 767.45897284665, 255.52906479283)

var node13 = ::blueprint::nodes::load::Load()

node13.var_name = "body"

_editor.add_node(node13, 91.305984662782, -12.331504157378)

var node14 = ::blueprint::nodes::load::Load()

node14.var_name = "gear"

_editor.add_node(node14, 25.490942309441, 260.83502843844)

var node15 = ::blueprint::nodes::input::Input()

node15.var_name = "teeth_type"
node15.var_type = "int"

_editor.add_node(node15, -122.60314571091, 431.14754252058)

var node16 = ::blueprint::nodes::input::Input()

node16.var_name = "teeth_size"
node16.var_type = "num"

_editor.add_node(node16, -121.44652357904, 365.60517882528)

var node17 = ::blueprint::nodes::input::Input()

node17.var_name = "color"
node17.var_type = "num3"

_editor.add_node(node17, -119.83364182066, 295.00883063776)

var node18 = ::blueprint::nodes::input::Input()

node18.var_name = "pos"
node18.var_type = "num2"

_editor.add_node(node18, 880.88359952921, 461.27602958056)

var node19 = ::blueprint::nodes::input::Input()

node19.var_name = "ground_body"
node19.var_type = "body"

_editor.add_node(node19, 91.948835462055, 45.788839560138)

var node20 = ::blueprint::nodes::store::Store()

node20.var_name = "shape"

_editor.add_node(node20, 251.1943505253, 493.74925395871)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "shape"

_editor.add_node(node21, 466.4284390236, 630.52783331774)

var node22 = ::physicsgraph::nodes::fixture::Fixture()

_editor.add_node(node22, 600.55641733292, 584.49234355392)

var node23 = ::scenegraph::nodes::sprite::Sprite()

_editor.add_node(node23, 732.15826305209, 585.94403743033)

var node24 = ::blueprint::nodes::input::Input()

node24.var_name = "category"
node24.var_type = "int"

_editor.add_node(node24, 468.40268182002, 576.20522851771)

var node25 = ::blueprint::nodes::input::Input()

node25.var_name = "not_collide"
node25.var_type = [ "int" ]

_editor.add_node(node25, 472.15925114716, 509.33823178123)

var node26 = ::blueprint::nodes::input::Input()

node26.var_name = "attach"
node26.var_type = "sprite"

_editor.add_node(node26, 733.76775260288, 504.50039063516)

var node27 = ::scenegraph::nodes::combine::Combine()

_editor.add_node(node27, 877.51452475471, 541.8593739124)

Blueprint.connect(node2, "var", node1, "radius")
Blueprint.connect(node3, "var", node1, "count")
Blueprint.connect(node15, "var", node1, "teeth_type")
Blueprint.connect(node16, "var", node1, "teeth_size")
Blueprint.connect(node17, "var", node1, "color")
Blueprint.connect(node1, "spr", node20, "var")
Blueprint.connect(node20, "var", node21, "var")
Blueprint.connect(node21, "var", node22, "shape")
Blueprint.connect(node24, "var", node22, "category")
Blueprint.connect(node25, "var", node22, "not_collide")
Blueprint.connect(node22, "fixture", node23, "symbol")
Blueprint.connect(node23, "spr", node27, "child0")
Blueprint.connect(node26, "var", node27, "child1")
Blueprint.connect(node27, "parent", node4, "spr")
Blueprint.connect(node18, "var", node4, "translate")
Blueprint.connect(node4, "spr", node0, "var")
Blueprint.connect(node0, "var", node14, "var")
Blueprint.connect(node14, "var", node7, "geos")
Blueprint.connect(node7, "body", node11, "var")
Blueprint.connect(node11, "var", node13, "var")
Blueprint.connect(node19, "var", node8, "body_a")
Blueprint.connect(node13, "var", node8, "body_b")
Blueprint.connect(node8, "joint", node10, "var")
Blueprint.connect(node11, "var", node12, "var")
Blueprint.connect(node12, "var", node9, "var")
Blueprint.connect(node0, "var", node5, "var")
Blueprint.connect(node5, "var", node6, "var")
