<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::DxContextManagerCallback Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">DxContextManagerCallback</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::DxContextManagerCallback Class Reference</h1><!-- doxytag: class="nv::cloth::DxContextManagerCallback" -->Callback interface to manage the DirectX context/device used for compute.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_dx_context_manager_callback_8h-source.html">DxContextManagerCallback.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_dx_context_manager_callback-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#4d8cafe2879665280b8e9e8daa18e95f">acquireContext</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Acquire the D3D context for the current thread.  <a href="#4d8cafe2879665280b8e9e8daa18e95f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual ID3D11DeviceContext *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#39f71451d6802462f724554a6d06004c">getContext</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Return the D3D context to use for compute work.  <a href="#39f71451d6802462f724554a6d06004c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual ID3D11Device *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#b3ca0c862df8de0e4022fcbfee5351a3">getDevice</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Return the D3D device to use for compute work.  <a href="#b3ca0c862df8de0e4022fcbfee5351a3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#df97ac38567b401fd34168e32cdc88cd">releaseContext</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Release the D3D context from the current thread.  <a href="#df97ac38567b401fd34168e32cdc88cd"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#a2ebcef21c55d3a59e01d344e6ee917a">synchronizeResources</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Return if exposed buffers (only cloth particles at the moment) are created with D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX.  <a href="#a2ebcef21c55d3a59e01d344e6ee917a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#8bdc176529c9e17936002067d13d4d47">~DxContextManagerCallback</a> ()</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Callback interface to manage the DirectX context/device used for compute. 
<p>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="8bdc176529c9e17936002067d13d4d47"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::~DxContextManagerCallback" ref="8bdc176529c9e17936002067d13d4d47" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::DxContextManagerCallback::~DxContextManagerCallback           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="4d8cafe2879665280b8e9e8daa18e95f"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::acquireContext" ref="4d8cafe2879665280b8e9e8daa18e95f" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::DxContextManagerCallback::acquireContext           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Acquire the D3D context for the current thread. 
<p>
Acquisitions are allowed to be recursive within a single thread. You can acquire the context multiple times so long as you release it the same count. 
</div>
</div><p>
<a class="anchor" name="39f71451d6802462f724554a6d06004c"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::getContext" ref="39f71451d6802462f724554a6d06004c" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual ID3D11DeviceContext* nv::cloth::DxContextManagerCallback::getContext           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Return the D3D context to use for compute work. 
<p>

</div>
</div><p>
<a class="anchor" name="b3ca0c862df8de0e4022fcbfee5351a3"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::getDevice" ref="b3ca0c862df8de0e4022fcbfee5351a3" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual ID3D11Device* nv::cloth::DxContextManagerCallback::getDevice           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Return the D3D device to use for compute work. 
<p>

</div>
</div><p>
<a class="anchor" name="df97ac38567b401fd34168e32cdc88cd"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::releaseContext" ref="df97ac38567b401fd34168e32cdc88cd" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::DxContextManagerCallback::releaseContext           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Release the D3D context from the current thread. 
<p>

</div>
</div><p>
<a class="anchor" name="a2ebcef21c55d3a59e01d344e6ee917a"></a><!-- doxytag: member="nv::cloth::DxContextManagerCallback::synchronizeResources" ref="a2ebcef21c55d3a59e01d344e6ee917a" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::DxContextManagerCallback::synchronizeResources           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Return if exposed buffers (only cloth particles at the moment) are created with D3D11_RESOURCE_MISC_SHARED_KEYEDMUTEX. 
<p>
The user is responsible to query and acquire the mutex of all corresponding buffers. todo: We should acquire the mutex locally if we continue to allow resource sharing across devices. 
</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_dx_context_manager_callback_8h-source.html">DxContextManagerCallback.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
