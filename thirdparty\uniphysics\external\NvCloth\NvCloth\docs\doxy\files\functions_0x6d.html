<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
      <li><a href="functions_rela.html"><span>Related&nbsp;Functions</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li class="current"><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="functions_0x7e.html#index_~"><span>~</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all class members with links to the classes they belong to:
<p>
<h3><a class="anchor" name="index_m">- m -</a></h3><ul>
<li>mAnchors
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">nv::cloth::CookedData</a>
<li>MappedRange()
: <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html#52e834449347f418cab023175a53f7dc">nv::cloth::MappedRange&lt; T &gt;</a>
<li>mBuffer
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">nv::cloth::GpuParticles</a>
<li>mCallback
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">nv::cloth::NvClothProfileScoped</a>
<li>mCompressionLimit
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">nv::cloth::PhaseConfig</a>
<li>mContextId
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">nv::cloth::NvClothProfileScoped</a>
<li>mCurrent
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">nv::cloth::GpuParticles</a>
<li>mDetached
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">nv::cloth::NvClothProfileScoped</a>
<li>mEventName
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">nv::cloth::NvClothProfileScoped</a>
<li>mIndices
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">nv::cloth::CookedData</a>
<li>mNumParticles
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">nv::cloth::CookedData</a>
<li>mPadding
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">nv::cloth::PhaseConfig</a>
<li>mPhaseIndex
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">nv::cloth::PhaseConfig</a>
<li>mPhaseIndices
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">nv::cloth::CookedData</a>
<li>mPhaseTypes
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">nv::cloth::CookedData</a>
<li>mPrevious
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">nv::cloth::GpuParticles</a>
<li>mProfilerData
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">nv::cloth::NvClothProfileScoped</a>
<li>mRefCount
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">nv::cloth::Fabric</a>
<li>mRestvalues
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">nv::cloth::CookedData</a>
<li>mSets
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">nv::cloth::CookedData</a>
<li>mStiffness
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">nv::cloth::PhaseConfig</a>
<li>mStiffnessMultiplier
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">nv::cloth::PhaseConfig</a>
<li>mStiffnessValues
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">nv::cloth::CookedData</a>
<li>mStretchLimit
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">nv::cloth::PhaseConfig</a>
<li>mTetherLengths
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">nv::cloth::CookedData</a>
<li>mTriangles
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">nv::cloth::CookedData</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
