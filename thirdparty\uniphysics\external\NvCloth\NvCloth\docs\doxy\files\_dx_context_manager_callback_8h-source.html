<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: DxContextManagerCallback.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>DxContextManagerCallback.h</h1><a href="_dx_context_manager_callback_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="keyword">struct </span>ID3D11Device;
<a name="l00033"></a>00033 <span class="keyword">struct </span>ID3D11DeviceContext;
<a name="l00034"></a>00034 
<a name="l00035"></a>00035 <span class="keyword">namespace </span>nv
<a name="l00036"></a>00036 {
<a name="l00037"></a>00037 <span class="keyword">namespace </span>cloth
<a name="l00038"></a>00038 {
<a name="l00039"></a>00039 
<a name="l00043"></a><a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">00043</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html" title="Callback interface to manage the DirectX context/device used for compute.">DxContextManagerCallback</a>
<a name="l00044"></a>00044 {
<a name="l00045"></a>00045 <span class="keyword">public</span>:
<a name="l00046"></a><a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#8bdc176529c9e17936002067d13d4d47">00046</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#8bdc176529c9e17936002067d13d4d47">~DxContextManagerCallback</a>() {}
<a name="l00054"></a>00054     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#4d8cafe2879665280b8e9e8daa18e95f" title="Acquire the D3D context for the current thread.">acquireContext</a>() = 0;
<a name="l00055"></a>00055 
<a name="l00059"></a>00059     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#df97ac38567b401fd34168e32cdc88cd" title="Release the D3D context from the current thread.">releaseContext</a>() = 0;
<a name="l00060"></a>00060 
<a name="l00064"></a>00064     <span class="keyword">virtual</span> ID3D11Device* <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#b3ca0c862df8de0e4022fcbfee5351a3" title="Return the D3D device to use for compute work.">getDevice</a>() <span class="keyword">const</span> = 0;
<a name="l00065"></a>00065 
<a name="l00069"></a>00069     <span class="keyword">virtual</span> ID3D11DeviceContext* <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#39f71451d6802462f724554a6d06004c" title="Return the D3D context to use for compute work.">getContext</a>() <span class="keyword">const</span> = 0;
<a name="l00070"></a>00070 
<a name="l00080"></a>00080     <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#a2ebcef21c55d3a59e01d344e6ee917a" title="Return if exposed buffers (only cloth particles at the moment) are created with D3D11_RESOURCE_MISC_...">synchronizeResources</a>() <span class="keyword">const</span> = 0;
<a name="l00081"></a>00081 };
<a name="l00082"></a>00082 
<a name="l00083"></a>00083 }
<a name="l00084"></a>00084 }
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
