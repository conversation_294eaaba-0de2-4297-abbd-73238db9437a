<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Cloth Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8b2a9bc21d7c04bd0e656b911282000b">clearInertia</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2f5b55bbff3741ffd107d67bb63b2adf">clearInterpolation</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#82ab50da85a99a76060c7b9463fdf386">clearMotionConstraints</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6bd3a4e6c557ff981303f111db9d8aaa">clearParticleAccelerations</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2a1776072b165064eddb3719633b291f">clearSeparationConstraints</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ac8169cc296ebabd715f51ece660a2e5">clone</a>(Factory &amp;factory) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6991f178368b6de52fe4dce86f10910f">Cloth</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [inline, protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#974b0d9774b1a9d22e3d53626a32bed9">Cloth</a>(const Cloth &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f901e20a52f80678a0e4aad59bdd8286">enableContinuousCollision</a>(bool)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#56dd08034230d00dd332e01e65075ad6">getAccelerationFilterWidth</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#294f5e23749618c8e90f35bd851270f3">getAngularDrag</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#079c1d3a32dd4657631820ac01a1f3bb">getAngularInertia</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6b0c89213073d0a58f2309b4c0526c7d">getBoundingBoxCenter</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#4b16c7cedaecc35b5d722040b28f7bdf">getBoundingBoxScale</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f87e077bafe91061772683416c849484">getCentrifugalInertia</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c5c1bf8f32e9add7d6978cd80344a829">getCollisionMassScale</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d">getCurrentParticles</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2459fe06a066953e230e6271e4dd87b0">getCurrentParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e12abf9173dbcbb09690f229b8c8b7dd">getDamping</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#16b04df700089098bb956fcdc30e77b4">getDragCoefficient</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#14174ed06c234119fd42bbddbaabc5f1">getFabric</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dfb665fce893853e21ddbd3241685d7f">getFactory</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#31aeac4d22831073a79d2b6da53c17ae">getFluidDensity</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a2daf756406fd64e1b7b2174eb040367">getFriction</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8d31c57a119fb853d4ceb1e197b2351a">getGpuParticles</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#92a76707f82caf33088f23983d5ede03">getGravity</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2dbaaaa013d7c69902c9d5eaa98f6af9">getLiftCoefficient</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#fa87c1d6ab87c5d7edbd48b5eb755659">getLinearDrag</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6">getLinearInertia</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#12449a7e62ac5d66149510fe01c51126">getMotionConstraintBias</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8f26feaee081f503440e077477d51d24">getMotionConstraints</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c0a1cd8a04b0e44580f53185bd3a7438">getMotionConstraintScale</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#56bb155eacd1b0b2d1dc4803ff7c02a7">getMotionConstraintStiffness</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c9253d7ea3e5bb8b7389c6718d1d14e7">getNumCapsules</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#72d40e0b094a67c5a75c3a442aff4d88">getNumConvexes</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#639565810f9f83088f870643c957bee3">getNumMotionConstraints</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#22f4390c87ae7e40704b7e346b6c3dc4">getNumParticleAccelerations</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#cf0e6ac1e540ae6d2f7a9450a42fcb18">getNumParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#63c8731061e061c5d69c43c83a1f7213">getNumPlanes</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6549b36b76a8269864c695d3b77aae63">getNumRestPositions</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#44b430eff88a119b5242e5ed87722ee0">getNumSelfCollisionIndices</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7951527b51d5e4c523c179c5c7f34d3c">getNumSeparationConstraints</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#4b6b1d7fd2adfbc9d7ac66bbb9c418dc">getNumSpheres</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#9dc99e94a2d2698b7c165160dc850337">getNumTriangles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7b67c8d82763c26d18d52e864137f46f">getNumVirtualParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a08c88cf1855e76452a783c336d1102c">getNumVirtualParticleWeights</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#5b6086a1da8379361db57d0d3f6c8655">getParticleAccelerations</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b8dee849c57c802ed40234edeaa998be">getPreviousIterationDt</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#9b22cae129eb4d9677fdea24fa5ec486">getPreviousParticles</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8324ff7a6a8bcf3702f3ed51e431f89b">getPreviousParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#78235f2aa83c32ccf35b6da0e221fe8e">getRotation</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#064c442c7588405581006d72aa3d88f9">getSelfCollisionDistance</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#209dae86682049c944b9c2ba41aeb0bf">getSelfCollisionStiffness</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2c02b9284fb998282226b0a57209a7d3">getSeparationConstraints</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a3be62e917066f2f29f64320c8286893">getSleepAfterCount</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7a07e21a9b99dd3eab429569c77eac1c">getSleepPassCount</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6313c4680c883d3bb6e20632ebde1ab8">getSleepTestInterval</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2bd353debfdb900979395fe8870df1bf">getSleepThreshold</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01">getSolverFrequency</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6476ef704ed1733398ba87093bc8bb22">getStiffnessFrequency</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#********************************">getTetherConstraintScale</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#d423f35a4952860552430fea2796ce15">getTetherConstraintStiffness</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#519a015726fbc04a7bcf60afcfe3b0ca">getTranslation</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#91e02303afccc55bba87886c1187002b">getUserData</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc98811170dedd7f79c97a5ad289aeb2">getWindVelocity</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#21e5c91983c11511600cfefd68be49f9">isAsleep</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#5afecc558e319c61e047a2bb8b113b40">isContinuousCollisionEnabled</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#00d6c87135168af45d1b9694433f9036">lockParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0844b06815e4395bbc6f9c00d2d4bb24">operator=</a>(const Cloth &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#21da17df59669e7a2b670823400e740b">putToSleep</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#d041f7c80ecb193154e0ccce59e81867">setAcceleationFilterWidth</a>(uint32_t)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0878320c241cee9876c2ac3122d80cb8">setAngularDrag</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8">setAngularInertia</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ee30e619014cf93c518170b4b7a96df5">setCapsules</a>(Range&lt; const uint32_t &gt; capsules, uint32_t first, uint32_t last)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484">setCentrifugalInertia</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c75a30c4f8f02312b112e9650e886edb">setCollisionMassScale</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b05513e796029c7562a1ff6fb740e561">setConvexes</a>(Range&lt; const uint32_t &gt; convexMasks, uint32_t first, uint32_t last)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6">setDamping</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007">setDragCoefficient</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752">setFluidDensity</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715">setFriction</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916">setGravity</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193">setLiftCoefficient</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e97296e9eec127303d96b4febe90b43e">setLinearDrag</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c">setLinearInertia</a>(const physx::PxVec3 &amp;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#349196b772aa39e8f3575baaf5dc35d6">setMotionConstraintScaleBias</a>(float scale, float bias)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c8d1af1c6df5123d5c81331647b24a67">setMotionConstraintStiffness</a>(float stiffness)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ea95e56cb73720970f79903dcffc8360">setPhaseConfig</a>(Range&lt; const PhaseConfig &gt; configs)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#204fe4d80dd980b3fe45ec98270ebcd4">setPlanes</a>(Range&lt; const physx::PxVec4 &gt; planes, uint32_t first, uint32_t last)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#263e8beebed6fb96f06bf2688a15ad1c">setRestPositions</a>(Range&lt; const physx::PxVec4 &gt;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7">setRotation</a>(const physx::PxQuat &amp;rot)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8">setSelfCollisionDistance</a>(float distance)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e77122c9d483539afe4b944429d5d464">setSelfCollisionIndices</a>(Range&lt; const uint32_t &gt;)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1">setSelfCollisionStiffness</a>(float stiffness)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7ef6797d557a31d9380835d26a894f15">setSleepAfterCount</a>(uint32_t)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#de65cf56e6b20d5a44a222b9e28ecf2f">setSleepTestInterval</a>(uint32_t)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#1a34c62e0891496b949194556dc729f1">setSleepThreshold</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b17d1cc294a3792c5c35e4ab353fac29">setSolverFrequency</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#31daeab54984168c8940f421c908e80f">setSpheres</a>(Range&lt; const physx::PxVec4 &gt; spheres, uint32_t first, uint32_t last)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8a4512e945fa62ffd64d291686cc59a8">setStiffnessFrequency</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91">setTetherConstraintScale</a>(float scale)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c">setTetherConstraintStiffness</a>(float stiffness)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0">setTranslation</a>(const physx::PxVec3 &amp;trans)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e5d69e455fee1bdd9f92ef888e8d2514">setTriangles</a>(Range&lt; const physx::PxVec3 &gt; triangles, uint32_t first, uint32_t last)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc7593d195a36d040181fbaa0c21ead6">setTriangles</a>(Range&lt; const physx::PxVec3 &gt; triangles, Range&lt; const physx::PxVec3 &gt;, uint32_t first)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e7c0b099e90d409a65ee14d6f77e57c5">setUserData</a>(void *)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#285c31837f64c3cd60fce8ba269fe3f1">setVirtualParticles</a>(Range&lt; const uint32_t[4]&gt; indices, Range&lt; const physx::PxVec3 &gt; weights)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d">setWindVelocity</a>(physx::PxVec3)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#86e8ce29e3d64732d8940857115f397e">teleport</a>(const physx::PxVec3 &amp;delta)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ffc4d89e66969c5fcf3b4ac2af01fe9d">unlockParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#4294ed2b0a515600e9448264cc0377e3">wakeUp</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6de3f7e8b8d71624daa22cebf41b5679">~Cloth</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td><code> [inline, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
