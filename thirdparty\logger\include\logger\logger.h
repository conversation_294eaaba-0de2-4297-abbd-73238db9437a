#pragma once

#include <string>
#include <iostream>
#include <fstream>
#include <memory>

namespace logger {

enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARNING = 2,
    ERROR = 3,
    FATAL = 4
};

class Logger {
public:
    static Logger& Instance();
    
    void SetLevel(LogLevel level);
    LogLevel GetLevel() const;
    
    void SetOutput(const std::string& filename);
    void SetOutput(std::ostream& stream);
    
    void Log(LogLevel level, const std::string& message);
    void Debug(const std::string& message);
    void Info(const std::string& message);
    void Warning(const std::string& message);
    void Error(const std::string& message);
    void Fatal(const std::string& message);
    
private:
    Logger() = default;
    ~Logger() = default;
    Logger(const Logger&) = delete;
    Logger& operator=(const Logger&) = delete;
    
    LogLevel m_level = LogLevel::INFO;
    std::ostream* m_output = &std::cout;
    std::unique_ptr<std::ofstream> m_file_output;
};

// Convenience macros
#define LOG_DEBUG(msg) logger::Logger::Instance().Debug(msg)
#define LOG_INFO(msg) logger::Logger::Instance().Info(msg)
#define LOG_WARNING(msg) logger::Logger::Instance().Warning(msg)
#define LOG_ERROR(msg) logger::Logger::Instance().Error(msg)
#define LOG_FATAL(msg) logger::Logger::Instance().Fatal(msg)

} // namespace logger
