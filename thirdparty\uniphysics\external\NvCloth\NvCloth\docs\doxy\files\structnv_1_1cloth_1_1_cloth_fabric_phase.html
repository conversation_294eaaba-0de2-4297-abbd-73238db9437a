<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothFabricPhase Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">ClothFabricPhase</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothFabricPhase Struct Reference<br>
<small>
[<a class="el" href="group__extensions.html">Extensions</a>]</small>
</h1><!-- doxytag: class="nv::cloth::ClothFabricPhase" -->References a set of constraints that can be solved in parallel.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_cloth_fabric_phase-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g09aa011d5780d368d58864791f2ff512">ClothFabricPhase</a> (<a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a> type=ClothFabricPhaseType::eINVALID, physx::PxU32 index=0)</td></tr>

<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#f19b795dfc88f16a90a75621be1fbd0a">phaseType</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Type of constraints to solve.  <a href="#f19b795dfc88f16a90a75621be1fbd0a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#a9f1038529f5a7545c6f19c95be61015">setIndex</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Index of the set that contains the particle indices.  <a href="#a9f1038529f5a7545c6f19c95be61015"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
References a set of constraints that can be solved in parallel. 
<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> for an explanation of the concepts on phase and set. </dd></dl>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="f19b795dfc88f16a90a75621be1fbd0a"></a><!-- doxytag: member="nv::cloth::ClothFabricPhase::phaseType" ref="f19b795dfc88f16a90a75621be1fbd0a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a> <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#f19b795dfc88f16a90a75621be1fbd0a">nv::cloth::ClothFabricPhase::phaseType</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Type of constraints to solve. 
<p>

</div>
</div><p>
<a class="anchor" name="a9f1038529f5a7545c6f19c95be61015"></a><!-- doxytag: member="nv::cloth::ClothFabricPhase::setIndex" ref="a9f1038529f5a7545c6f19c95be61015" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#a9f1038529f5a7545c6f19c95be61015">nv::cloth::ClothFabricPhase::setIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Index of the set that contains the particle indices. 
<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
