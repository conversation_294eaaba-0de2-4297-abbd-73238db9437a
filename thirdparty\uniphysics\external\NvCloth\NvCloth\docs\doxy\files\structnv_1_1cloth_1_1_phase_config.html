<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::PhaseConfig Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_phase_config.html">PhaseConfig</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::PhaseConfig Struct Reference</h1><!-- doxytag: class="nv::cloth::PhaseConfig" --><code>#include &lt;<a class="el" href="_phase_config_8h-source.html">PhaseConfig.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_phase_config-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#44574dd7cc0f67ec460bb0c9bdeb0819">PhaseConfig</a> (uint16_t index=uint16_t(-1))</td></tr>

<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">mCompressionLimit</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">mPadding</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint16_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">mPhaseIndex</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">mStiffness</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">mStiffnessMultiplier</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">mStretchLimit</a></td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="44574dd7cc0f67ec460bb0c9bdeb0819"></a><!-- doxytag: member="nv::cloth::PhaseConfig::PhaseConfig" ref="44574dd7cc0f67ec460bb0c9bdeb0819" args="(uint16_t index=uint16_t(&#45;1))" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::PhaseConfig::PhaseConfig           </td>
          <td>(</td>
          <td class="paramtype">uint16_t&nbsp;</td>
          <td class="paramname"> <em>index</em> = <code>uint16_t(-1)</code>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="862b9a1c68a6f98eb84b1f2f2777640f"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mCompressionLimit" ref="862b9a1c68a6f98eb84b1f2f2777640f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">nv::cloth::PhaseConfig::mCompressionLimit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="a307116b5e6af2e2a97bf57f94e85a10"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mPadding" ref="a307116b5e6af2e2a97bf57f94e85a10" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">nv::cloth::PhaseConfig::mPadding</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="36a0e7d9261b54665b448f37fc8aa65f"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mPhaseIndex" ref="36a0e7d9261b54665b448f37fc8aa65f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint16_t <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">nv::cloth::PhaseConfig::mPhaseIndex</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="25a2498b7d86f3420cbe02914f442838"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mStiffness" ref="25a2498b7d86f3420cbe02914f442838" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">nv::cloth::PhaseConfig::mStiffness</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="5b9466d183a7bcc02468f5bb16b00336"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mStiffnessMultiplier" ref="5b9466d183a7bcc02468f5bb16b00336" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">nv::cloth::PhaseConfig::mStiffnessMultiplier</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="9de88a355594846c6818c4439e46899b"></a><!-- doxytag: member="nv::cloth::PhaseConfig::mStretchLimit" ref="9de88a355594846c6818c4439e46899b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">float <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">nv::cloth::PhaseConfig::mStretchLimit</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_phase_config_8h-source.html">PhaseConfig.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
