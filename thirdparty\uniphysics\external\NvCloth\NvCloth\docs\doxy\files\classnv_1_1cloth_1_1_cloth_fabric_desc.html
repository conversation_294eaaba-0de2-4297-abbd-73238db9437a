<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothFabricDesc Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">ClothFabricDesc</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothFabricDesc Class Reference<br>
<small>
[<a class="el" href="group__extensions.html">Extensions</a>]</small>
</h1><!-- doxytag: class="nv::cloth::ClothFabricDesc" -->References all the data required to create a fabric.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth_fabric_desc-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987">ClothFabricDesc</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">constructor sets to default.  <a href="group__extensions.html#g25a9034d02b0edfaee83e58213288987"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gb65c431a270115915e78a73c37489dee">isValid</a> () const </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if the descriptor is valid.  <a href="group__extensions.html#gb65c431a270115915e78a73c37489dee"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98">setToDefault</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">(re)sets the structure to the default.  <a href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxU32 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba">indices</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array of particle indices which specifies the pair of constrained vertices.  <a href="#be0c3211f0dff98d6bed2a5ba859cdba"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1">nbParticles</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The number of particles needed when creating a PxCloth instance from the fabric.  <a href="#27b5e237d6317729292527baded536e1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78">nbPhases</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The number of solver phases.  <a href="#2b0bdbc53cd541c268b1420443c6de78"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc">nbSets</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The number of sets in the fabric.  <a href="#de386e51c397d5ab229e73090f9a81fc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad">nbTethers</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Size of tetherAnchors and tetherLengths arrays, needs to be multiple of nbParticles.  <a href="#0f41befe55fe10d711513cf4aba0abad"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nbTriangles</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">ClothFabricPhase</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f">phases</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array defining which constraints to solve each phase.  <a href="#add526e57831da43c7a41de83349a38f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxReal *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd">restvalues</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array of rest values for each constraint.  <a href="#5c77a4ffedc077675afb330b4c6dc8cd"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxU32 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452">sets</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array with an index per set which points one entry beyond the last constraint of the set.  <a href="#58b1640666ca9ed22a3ee84e7e7d8452"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxU32 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83">tetherAnchors</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array of particle indices specifying the tether anchors.  <a href="#368ed028593849935d0d32a47ae21a83"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxReal *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a">tetherLengths</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Array of rest distance between tethered particle pairs.  <a href="#19005ea1d05eadafab1ed0f52cc14a4a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const physx::PxU32 *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">triangles</a></td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
References all the data required to create a fabric. 
<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#24e4bd5204366e6f8ddcfb7e27d4d19f" title="Returns the fabric descriptor to create the fabric.">ClothFabricCooker.getDescriptor()</a> </dd></dl>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="be0c3211f0dff98d6bed2a5ba859cdba"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::indices" ref="be0c3211f0dff98d6bed2a5ba859cdba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxU32* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba">nv::cloth::ClothFabricDesc::indices</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array of particle indices which specifies the pair of constrained vertices. 
<p>
See Fabric.getParticleIndices(). 
</div>
</div><p>
<a class="anchor" name="27b5e237d6317729292527baded536e1"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::nbParticles" ref="27b5e237d6317729292527baded536e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1">nv::cloth::ClothFabricDesc::nbParticles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of particles needed when creating a PxCloth instance from the fabric. 
<p>

</div>
</div><p>
<a class="anchor" name="2b0bdbc53cd541c268b1420443c6de78"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::nbPhases" ref="2b0bdbc53cd541c268b1420443c6de78" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78">nv::cloth::ClothFabricDesc::nbPhases</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of solver phases. 
<p>

</div>
</div><p>
<a class="anchor" name="de386e51c397d5ab229e73090f9a81fc"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::nbSets" ref="de386e51c397d5ab229e73090f9a81fc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc">nv::cloth::ClothFabricDesc::nbSets</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The number of sets in the fabric. 
<p>

</div>
</div><p>
<a class="anchor" name="0f41befe55fe10d711513cf4aba0abad"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::nbTethers" ref="0f41befe55fe10d711513cf4aba0abad" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad">nv::cloth::ClothFabricDesc::nbTethers</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Size of tetherAnchors and tetherLengths arrays, needs to be multiple of nbParticles. 
<p>

</div>
</div><p>
<a class="anchor" name="b8e7ddf2dc4b66a96151c313c1c68e81"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::nbTriangles" ref="b8e7ddf2dc4b66a96151c313c1c68e81" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nv::cloth::ClothFabricDesc::nbTriangles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="add526e57831da43c7a41de83349a38f"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::phases" ref="add526e57831da43c7a41de83349a38f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">ClothFabricPhase</a>* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f">nv::cloth::ClothFabricDesc::phases</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array defining which constraints to solve each phase. 
<p>
See Fabric.getPhases(). 
</div>
</div><p>
<a class="anchor" name="5c77a4ffedc077675afb330b4c6dc8cd"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::restvalues" ref="5c77a4ffedc077675afb330b4c6dc8cd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxReal* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd">nv::cloth::ClothFabricDesc::restvalues</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array of rest values for each constraint. 
<p>
See Fabric.getRestvalues(). 
</div>
</div><p>
<a class="anchor" name="58b1640666ca9ed22a3ee84e7e7d8452"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::sets" ref="58b1640666ca9ed22a3ee84e7e7d8452" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxU32* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452">nv::cloth::ClothFabricDesc::sets</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array with an index per set which points one entry beyond the last constraint of the set. 
<p>
See Fabric.getSets(). 
</div>
</div><p>
<a class="anchor" name="368ed028593849935d0d32a47ae21a83"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::tetherAnchors" ref="368ed028593849935d0d32a47ae21a83" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxU32* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83">nv::cloth::ClothFabricDesc::tetherAnchors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array of particle indices specifying the tether anchors. 
<p>
See Fabric.getTetherAnchors(). 
</div>
</div><p>
<a class="anchor" name="19005ea1d05eadafab1ed0f52cc14a4a"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::tetherLengths" ref="19005ea1d05eadafab1ed0f52cc14a4a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxReal* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a">nv::cloth::ClothFabricDesc::tetherLengths</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Array of rest distance between tethered particle pairs. 
<p>
See Fabric.getTetherLengths(). 
</div>
</div><p>
<a class="anchor" name="b6e6ab337d8803cc74328314432453f4"></a><!-- doxytag: member="nv::cloth::ClothFabricDesc::triangles" ref="b6e6ab337d8803cc74328314432453f4" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const physx::PxU32* <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">nv::cloth::ClothFabricDesc::triangles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
