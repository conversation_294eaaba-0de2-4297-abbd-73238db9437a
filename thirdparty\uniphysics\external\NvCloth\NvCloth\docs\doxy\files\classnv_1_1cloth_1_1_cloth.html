<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::Cloth Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Cloth Class Reference</h1><!-- doxytag: class="nv::cloth::Cloth" --><code>#include &lt;<a class="el" href="_cloth_8h-source.html">Cloth.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8b2a9bc21d7c04bd0e656b911282000b">clearInertia</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set inertia derived from <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">setTranslation()</a> and <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7" title="Set the rotation of the local space simulation after next call to simulate().">setRotation()</a> to zero (once).  <a href="#8b2a9bc21d7c04bd0e656b911282000b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2f5b55bbff3741ffd107d67bb63b2adf">clearInterpolation</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#82ab50da85a99a76060c7b9463fdf386">clearMotionConstraints</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Removes all motion constraints.  <a href="#82ab50da85a99a76060c7b9463fdf386"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6bd3a4e6c557ff981303f111db9d8aaa">clearParticleAccelerations</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2a1776072b165064eddb3719633b291f">clearSeparationConstraints</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ac8169cc296ebabd715f51ece660a2e5">clone</a> (<a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;factory) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Creates a duplicate of this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> instance.  <a href="#ac8169cc296ebabd715f51ece660a2e5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f901e20a52f80678a0e4aad59bdd8286">enableContinuousCollision</a> (bool)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set if we use ccd or not (disabled by default).  <a href="#f901e20a52f80678a0e4aad59bdd8286"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#56dd08034230d00dd332e01e65075ad6">getAccelerationFilterWidth</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#294f5e23749618c8e90f35bd851270f3">getAngularDrag</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#079c1d3a32dd4657631820ac01a1f3bb">getAngularInertia</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8" title="Similar to setLinearInertia(), but for angular inertia.">setAngularInertia()</a>.  <a href="#079c1d3a32dd4657631820ac01a1f3bb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual const physx::PxVec3 &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#********************************">getBoundingBoxCenter</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns current particle position bounds center in local space.  <a href="#********************************"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual const physx::PxVec3 &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#********************************">getBoundingBoxScale</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns current particle position bounds size in local space.  <a href="#********************************"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f87e077bafe91061772683416c849484">getCentrifugalInertia</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484" title="Similar to setLinearInertia(), but for centrifugal inertia.">setCentrifugalInertia()</a>.  <a href="#f87e077bafe91061772683416c849484"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c5c1bf8f32e9add7d6978cd80344a829">getCollisionMassScale</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt; const <br class="typebreak">
physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2459fe06a066953e230e6271e4dd87b0">getCurrentParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the simulation particles of the current frame, read only.  <a href="#2459fe06a066953e230e6271e4dd87b0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a><br class="typebreak">
&lt; physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d">getCurrentParticles</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the simulation particles of the current frame.  <a href="#c659f1fa3f6dcf0eef323dc6bef81b9d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e12abf9173dbcbb09690f229b8c8b7dd">getDamping</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6" title="Sets damping of local particle velocity (1/stiffnessFrequency).">setDamping()</a>.  <a href="#e12abf9173dbcbb09690f229b8c8b7dd"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#16b04df700089098bb956fcdc30e77b4">getDragCoefficient</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007" title="/brief Sets the air drag coefficient.">setDragCoefficient()</a>.  <a href="#16b04df700089098bb956fcdc30e77b4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#14174ed06c234119fd42bbddbaabc5f1">getFabric</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the fabric used to create this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>.  <a href="#14174ed06c234119fd42bbddbaabc5f1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dfb665fce893853e21ddbd3241685d7f">getFactory</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the <a class="el" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a> used to create this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>.  <a href="#dfb665fce893853e21ddbd3241685d7f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#31aeac4d22831073a79d2b6da53c17ae">getFluidDensity</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752" title="/brief Sets the fluid density used for air drag/lift calculations.">setFluidDensity()</a>.  <a href="#31aeac4d22831073a79d2b6da53c17ae"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a2daf756406fd64e1b7b2174eb040367">getFriction</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715" title="Set the cloth collision shape friction coefficient.">setFriction()</a>.  <a href="#a2daf756406fd64e1b7b2174eb040367"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html">GpuParticles</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8d31c57a119fb853d4ceb1e197b2351a">getGpuParticles</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns platform dependent pointers to the current GPU particle memory.  <a href="#8d31c57a119fb853d4ceb1e197b2351a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#92a76707f82caf33088f23983d5ede03">getGravity</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns gravity set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916" title="Sets gravity in global coordinates.">setGravity()</a>.  <a href="#92a76707f82caf33088f23983d5ede03"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2dbaaaa013d7c69902c9d5eaa98f6af9">getLiftCoefficient</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193" title="/brief Sets the air lift coefficient.">setLiftCoefficient()</a>.  <a href="#2dbaaaa013d7c69902c9d5eaa98f6af9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#fa87c1d6ab87c5d7edbd48b5eb755659">getLinearDrag</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6">getLinearInertia</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6" title="Returns value set with getLinearInertia().">getLinearInertia()</a>.  <a href="#203a19cd80d2c9897df7c02006a05cb6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#12449a7e62ac5d66149510fe01c51126">getMotionConstraintBias</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8f26feaee081f503440e077477d51d24">getMotionConstraints</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns reference to motion constraints (position, radius) The entire range must be written after calling this function.  <a href="#8f26feaee081f503440e077477d51d24"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c0a1cd8a04b0e44580f53185bd3a7438">getMotionConstraintScale</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#56bb155eacd1b0b2d1dc4803ff7c02a7">getMotionConstraintStiffness</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c9253d7ea3e5bb8b7389c6718d1d14e7">getNumCapsules</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of capsules (which is half the number of capsule indices).  <a href="#c9253d7ea3e5bb8b7389c6718d1d14e7"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#72d40e0b094a67c5a75c3a442aff4d88">getNumConvexes</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of convexMasks currently set.  <a href="#72d40e0b094a67c5a75c3a442aff4d88"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#639565810f9f83088f870643c957bee3">getNumMotionConstraints</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#22f4390c87ae7e40704b7e346b6c3dc4">getNumParticleAccelerations</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#cf0e6ac1e540ae6d2f7a9450a42fcb18">getNumParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of particles simulated by this fabric.  <a href="#cf0e6ac1e540ae6d2f7a9450a42fcb18"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#63c8731061e061c5d69c43c83a1f7213">getNumPlanes</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of planes currently set.  <a href="#63c8731061e061c5d69c43c83a1f7213"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6549b36b76a8269864c695d3b77aae63">getNumRestPositions</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#44b430eff88a119b5242e5ed87722ee0">getNumSelfCollisionIndices</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of self collision indices set.  <a href="#44b430eff88a119b5242e5ed87722ee0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7951527b51d5e4c523c179c5c7f34d3c">getNumSeparationConstraints</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#4b6b1d7fd2adfbc9d7ac66bbb9c418dc">getNumSpheres</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of spheres currently set.  <a href="#4b6b1d7fd2adfbc9d7ac66bbb9c418dc"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#9dc99e94a2d2698b7c165160dc850337">getNumTriangles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of triangles currently set.  <a href="#9dc99e94a2d2698b7c165160dc850337"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7b67c8d82763c26d18d52e864137f46f">getNumVirtualParticles</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a08c88cf1855e76452a783c336d1102c">getNumVirtualParticleWeights</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#5b6086a1da8379361db57d0d3f6c8655">getParticleAccelerations</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b8dee849c57c802ed40234edeaa998be">getPreviousIterationDt</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the delta time used for previous iteration.  <a href="#b8dee849c57c802ed40234edeaa998be"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt; const <br class="typebreak">
physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8324ff7a6a8bcf3702f3ed51e431f89b">getPreviousParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the simulation particles of the previous frame.  <a href="#8324ff7a6a8bcf3702f3ed51e431f89b"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a><br class="typebreak">
&lt; physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#9b22cae129eb4d9677fdea24fa5ec486">getPreviousParticles</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the simulation particles of the previous frame.  <a href="#9b22cae129eb4d9677fdea24fa5ec486"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual const physx::PxQuat &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#78235f2aa83c32ccf35b6da0e221fe8e">getRotation</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the current rotation value that was set using <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7" title="Set the rotation of the local space simulation after next call to simulate().">setRotation()</a>.  <a href="#78235f2aa83c32ccf35b6da0e221fe8e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#064c442c7588405581006d72aa3d88f9">getSelfCollisionDistance</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8" title="/brief Set the distance particles need to be separated from each other withing the...">setSelfCollisionDistance()</a>.  <a href="#064c442c7588405581006d72aa3d88f9"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#209dae86682049c944b9c2ba41aeb0bf">getSelfCollisionStiffness</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1" title="/brief Set the constraint stiffness for the self collision constraints.">setSelfCollisionStiffness()</a>.  <a href="#209dae86682049c944b9c2ba41aeb0bf"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2c02b9284fb998282226b0a57209a7d3">getSeparationConstraints</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a3be62e917066f2f29f64320c8286893">getSleepAfterCount</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7a07e21a9b99dd3eab429569c77eac1c">getSleepPassCount</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6313c4680c883d3bb6e20632ebde1ab8">getSleepTestInterval</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2bd353debfdb900979395fe8870df1bf">getSleepThreshold</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01">getSolverFrequency</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns gravity set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01" title="Returns gravity set with getSolverFrequency().*/.">getSolverFrequency()</a>.*/.  <a href="#a7737f7ba0dfca885cfc1f1a7f651b01"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6476ef704ed1733398ba87093bc8bb22">getStiffnessFrequency</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#********************************">getTetherConstraintScale</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91" title="Set Tether constraint scale.">setTetherConstraintScale()</a>.  <a href="#********************************"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#d423f35a4952860552430fea2796ce15">getTetherConstraintStiffness</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c" title="Set Tether constraint stiffness.">setTetherConstraintStiffness()</a>.  <a href="#d423f35a4952860552430fea2796ce15"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual const physx::PxVec3 &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#519a015726fbc04a7bcf60afcfe3b0ca">getTranslation</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the current translation value that was set using <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">setTranslation()</a>.  <a href="#519a015726fbc04a7bcf60afcfe3b0ca"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#91e02303afccc55bba87886c1187002b">getUserData</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual physx::PxVec3&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc98811170dedd7f79c97a5ad289aeb2">getWindVelocity</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d" title="/brief Set wind in global coordinates.">setWindVelocity()</a>.  <a href="#dc98811170dedd7f79c97a5ad289aeb2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#21e5c91983c11511600cfefd68be49f9">isAsleep</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#5afecc558e319c61e047a2bb8b113b40">isContinuousCollisionEnabled</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if we use ccd.  <a href="#5afecc558e319c61e047a2bb8b113b40"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#00d6c87135168af45d1b9694433f9036">lockParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Used internally to synchronize CPU and GPU particle memory.  <a href="#00d6c87135168af45d1b9694433f9036"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#21da17df59669e7a2b670823400e740b">putToSleep</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#d041f7c80ecb193154e0ccce59e81867">setAcceleationFilterWidth</a> (uint32_t)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0878320c241cee9876c2ac3122d80cb8">setAngularDrag</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8">setAngularInertia</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">setLinearInertia()</a>, but for angular inertia.  <a href="#e14358081c4d1f30c14f08f3c71e38b8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ee30e619014cf93c518170b4b7a96df5">setCapsules</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; capsules, uint32_t first, uint32_t last)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set indices for capsule collision detection.  <a href="#ee30e619014cf93c518170b4b7a96df5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484">setCentrifugalInertia</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">setLinearInertia()</a>, but for centrifugal inertia.  <a href="#ba9e3001d7c11d70526ef281febe8484"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c75a30c4f8f02312b112e9650e886edb">setCollisionMassScale</a> (float)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b05513e796029c7562a1ff6fb740e561">setConvexes</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; convexMasks, uint32_t first, uint32_t last)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Enable planes for collision.  <a href="#b05513e796029c7562a1ff6fb740e561"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6">setDamping</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sets damping of local particle velocity (1/stiffnessFrequency).  <a href="#f1e7d559fd4942d82432aeb6ab477cf6"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007">setDragCoefficient</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Sets the air drag coefficient.  <a href="#fce3065c14afac4e5cf6e93b5d60a007"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752">setFluidDensity</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Sets the fluid density used for air drag/lift calculations.  <a href="#23bb80bd7b7acd3caa9c2b792c41a752"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715">setFriction</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the cloth collision shape friction coefficient.  <a href="#c10abfe96f96b65c9b3a5f37fee68715"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916">setGravity</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sets gravity in global coordinates.  <a href="#0223c7c82b616a6df01d7a4ffb57d916"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193">setLiftCoefficient</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Sets the air lift coefficient.  <a href="#17b5a40330eb57bdc495a2eb0d713193"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e97296e9eec127303d96b4febe90b43e">setLinearDrag</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c">setLinearInertia</a> (const physx::PxVec3 &amp;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the portion of local frame linear acceleration applied to particles.  <a href="#eb427bb61aac45279fd32f3c0dc5b66c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#349196b772aa39e8f3575baaf5dc35d6">setMotionConstraintScaleBias</a> (float scale, float bias)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#c8d1af1c6df5123d5c81331647b24a67">setMotionConstraintStiffness</a> (float stiffness)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ea95e56cb73720970f79903dcffc8360">setPhaseConfig</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const <a class="el" href="structnv_1_1cloth_1_1_phase_config.html">PhaseConfig</a> &gt; configs)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#204fe4d80dd980b3fe45ec98270ebcd4">setPlanes</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt; planes, uint32_t first, uint32_t last)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Sets plane values to be used with convex collision detection.  <a href="#204fe4d80dd980b3fe45ec98270ebcd4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#263e8beebed6fb96f06bf2688a15ad1c">setRestPositions</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt;)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7">setRotation</a> (const physx::PxQuat &amp;rot)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the rotation of the local space simulation after next call to simulate().  <a href="#f86beb891c025a1e4cfd1135e9ad8ae7"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8">setSelfCollisionDistance</a> (float distance)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Set the distance particles need to be separated from each other withing the cloth.  <a href="#8677510130ff4438306d20a413abd5d8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e77122c9d483539afe4b944429d5d464">setSelfCollisionIndices</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set self collision indices.  <a href="#e77122c9d483539afe4b944429d5d464"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1">setSelfCollisionStiffness</a> (float stiffness)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Set the constraint stiffness for the self collision constraints.  <a href="#b9988307f35da068e3d2ff08b56d95a1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7ef6797d557a31d9380835d26a894f15">setSleepAfterCount</a> (uint32_t)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#de65cf56e6b20d5a44a222b9e28ecf2f">setSleepTestInterval</a> (uint32_t)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#1a34c62e0891496b949194556dc729f1">setSleepThreshold</a> (float)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#b17d1cc294a3792c5c35e4ab353fac29">setSolverFrequency</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set target solver iterations per second.  <a href="#b17d1cc294a3792c5c35e4ab353fac29"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#31daeab54984168c8940f421c908e80f">setSpheres</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt; spheres, uint32_t first, uint32_t last)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set spheres for collision detection.  <a href="#31daeab54984168c8940f421c908e80f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#8a4512e945fa62ffd64d291686cc59a8">setStiffnessFrequency</a> (float)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91">setTetherConstraintScale</a> (float scale)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set Tether constraint scale.  <a href="#2e2b3e5e4542417c61bbe65064b6ba91"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c">setTetherConstraintStiffness</a> (float stiffness)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set Tether constraint stiffness.  <a href="#7e8eadb5e98ea146ad2e079cfddeeb0c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0">setTranslation</a> (const physx::PxVec3 &amp;trans)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set the translation of the local space simulation after next call to simulate().  <a href="#080cb97581d6e37079b6f62a7abfced0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc7593d195a36d040181fbaa0c21ead6">setTriangles</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt; triangles, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt;, uint32_t first)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e5d69e455fee1bdd9f92ef888e8d2514">setTriangles</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt; triangles, uint32_t first, uint32_t last)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set triangles for collision.  <a href="#e5d69e455fee1bdd9f92ef888e8d2514"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#e7c0b099e90d409a65ee14d6f77e57c5">setUserData</a> (void *)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Set user data.  <a href="#e7c0b099e90d409a65ee14d6f77e57c5"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#285c31837f64c3cd60fce8ba269fe3f1">setVirtualParticles</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t[4]&gt; indices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt; weights)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d">setWindVelocity</a> (physx::PxVec3)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">/brief Set wind in global coordinates.  <a href="#dd843ef612805153bdf04f2229697e0d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#86e8ce29e3d64732d8940857115f397e">teleport</a> (const physx::PxVec3 &amp;delta)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Adjust the position of the cloth without affecting the dynamics (to call after a world origin shift, for example).  <a href="#86e8ce29e3d64732d8940857115f397e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#ffc4d89e66969c5fcf3b4ac2af01fe9d">unlockParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Used internally to synchronize CPU and GPU particle memory.  <a href="#ffc4d89e66969c5fcf3b4ac2af01fe9d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#4294ed2b0a515600e9448264cc0377e3">wakeUp</a> ()=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6de3f7e8b8d71624daa22cebf41b5679">~Cloth</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Protected Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#974b0d9774b1a9d22e3d53626a32bed9">Cloth</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#6991f178368b6de52fe4dce86f10910f">Cloth</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html#0844b06815e4395bbc6f9c00d2d4bb24">operator=</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;)</td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="6991f178368b6de52fe4dce86f10910f"></a><!-- doxytag: member="nv::cloth::Cloth::Cloth" ref="6991f178368b6de52fe4dce86f10910f" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Cloth::Cloth           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="974b0d9774b1a9d22e3d53626a32bed9"></a><!-- doxytag: member="nv::cloth::Cloth::Cloth" ref="974b0d9774b1a9d22e3d53626a32bed9" args="(const Cloth &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Cloth::Cloth           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="6de3f7e8b8d71624daa22cebf41b5679"></a><!-- doxytag: member="nv::cloth::Cloth::~Cloth" ref="6de3f7e8b8d71624daa22cebf41b5679" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::Cloth::~Cloth           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="8b2a9bc21d7c04bd0e656b911282000b"></a><!-- doxytag: member="nv::cloth::Cloth::clearInertia" ref="8b2a9bc21d7c04bd0e656b911282000b" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::clearInertia           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set inertia derived from <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">setTranslation()</a> and <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7" title="Set the rotation of the local space simulation after next call to simulate().">setRotation()</a> to zero (once). 
<p>

</div>
</div><p>
<a class="anchor" name="2f5b55bbff3741ffd107d67bb63b2adf"></a><!-- doxytag: member="nv::cloth::Cloth::clearInterpolation" ref="2f5b55bbff3741ffd107d67bb63b2adf" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::clearInterpolation           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="82ab50da85a99a76060c7b9463fdf386"></a><!-- doxytag: member="nv::cloth::Cloth::clearMotionConstraints" ref="82ab50da85a99a76060c7b9463fdf386" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::clearMotionConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Removes all motion constraints. 
<p>

</div>
</div><p>
<a class="anchor" name="6bd3a4e6c557ff981303f111db9d8aaa"></a><!-- doxytag: member="nv::cloth::Cloth::clearParticleAccelerations" ref="6bd3a4e6c557ff981303f111db9d8aaa" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::clearParticleAccelerations           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="2a1776072b165064eddb3719633b291f"></a><!-- doxytag: member="nv::cloth::Cloth::clearSeparationConstraints" ref="2a1776072b165064eddb3719633b291f" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::clearSeparationConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="ac8169cc296ebabd715f51ece660a2e5"></a><!-- doxytag: member="nv::cloth::Cloth::clone" ref="ac8169cc296ebabd715f51ece660a2e5" args="(Factory &amp;factory) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* nv::cloth::Cloth::clone           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>factory</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Creates a duplicate of this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> instance. 
<p>
Same as: <div class="fragment"><pre class="fragment">        <a class="code" href="classnv_1_1cloth_1_1_cloth.html#dfb665fce893853e21ddbd3241685d7f" title="Returns the Factory used to create this Cloth.">getFactory</a>().clone(*<span class="keyword">this</span>);
</pre></div> 
</div>
</div><p>
<a class="anchor" name="f901e20a52f80678a0e4aad59bdd8286"></a><!-- doxytag: member="nv::cloth::Cloth::enableContinuousCollision" ref="f901e20a52f80678a0e4aad59bdd8286" args="(bool)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::enableContinuousCollision           </td>
          <td>(</td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set if we use ccd or not (disabled by default). 
<p>

</div>
</div><p>
<a class="anchor" name="56dd08034230d00dd332e01e65075ad6"></a><!-- doxytag: member="nv::cloth::Cloth::getAccelerationFilterWidth" ref="56dd08034230d00dd332e01e65075ad6" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getAccelerationFilterWidth           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="294f5e23749618c8e90f35bd851270f3"></a><!-- doxytag: member="nv::cloth::Cloth::getAngularDrag" ref="294f5e23749618c8e90f35bd851270f3" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getAngularDrag           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="079c1d3a32dd4657631820ac01a1f3bb"></a><!-- doxytag: member="nv::cloth::Cloth::getAngularInertia" ref="079c1d3a32dd4657631820ac01a1f3bb" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getAngularInertia           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8" title="Similar to setLinearInertia(), but for angular inertia.">setAngularInertia()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="nv::cloth::Cloth::getBoundingBoxCenter" ref="********************************" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual const physx::PxVec3&amp; nv::cloth::Cloth::getBoundingBoxCenter           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns current particle position bounds center in local space. 
<p>

</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="nv::cloth::Cloth::getBoundingBoxScale" ref="********************************" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual const physx::PxVec3&amp; nv::cloth::Cloth::getBoundingBoxScale           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns current particle position bounds size in local space. 
<p>

</div>
</div><p>
<a class="anchor" name="f87e077bafe91061772683416c849484"></a><!-- doxytag: member="nv::cloth::Cloth::getCentrifugalInertia" ref="f87e077bafe91061772683416c849484" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getCentrifugalInertia           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484" title="Similar to setLinearInertia(), but for centrifugal inertia.">setCentrifugalInertia()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="c5c1bf8f32e9add7d6978cd80344a829"></a><!-- doxytag: member="nv::cloth::Cloth::getCollisionMassScale" ref="c5c1bf8f32e9add7d6978cd80344a829" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getCollisionMassScale           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="2459fe06a066953e230e6271e4dd87b0"></a><!-- doxytag: member="nv::cloth::Cloth::getCurrentParticles" ref="2459fe06a066953e230e6271e4dd87b0" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt;const physx::PxVec4&gt; nv::cloth::Cloth::getCurrentParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the simulation particles of the current frame, read only. 
<p>
Similar to the non-const version of this function. This version is preferred as it doesn't wake up the cloth to account for the possibility that particles were changed. 
</div>
</div><p>
<a class="anchor" name="c659f1fa3f6dcf0eef323dc6bef81b9d"></a><!-- doxytag: member="nv::cloth::Cloth::getCurrentParticles" ref="c659f1fa3f6dcf0eef323dc6bef81b9d" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt;physx::PxVec4&gt; nv::cloth::Cloth::getCurrentParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the simulation particles of the current frame. 
<p>
Each PxVec4 element contains the particle position in the XYZ components and the inverse mass in the W component. The returned memory may be overwritten (to change attachment point locations for animation for example). Setting the inverse mass to 0 locks the particle in place. 
</div>
</div><p>
<a class="anchor" name="e12abf9173dbcbb09690f229b8c8b7dd"></a><!-- doxytag: member="nv::cloth::Cloth::getDamping" ref="e12abf9173dbcbb09690f229b8c8b7dd" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getDamping           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6" title="Sets damping of local particle velocity (1/stiffnessFrequency).">setDamping()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="16b04df700089098bb956fcdc30e77b4"></a><!-- doxytag: member="nv::cloth::Cloth::getDragCoefficient" ref="16b04df700089098bb956fcdc30e77b4" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getDragCoefficient           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007" title="/brief Sets the air drag coefficient.">setDragCoefficient()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="14174ed06c234119fd42bbddbaabc5f1"></a><!-- doxytag: member="nv::cloth::Cloth::getFabric" ref="14174ed06c234119fd42bbddbaabc5f1" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; nv::cloth::Cloth::getFabric           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the fabric used to create this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="dfb665fce893853e21ddbd3241685d7f"></a><!-- doxytag: member="nv::cloth::Cloth::getFactory" ref="dfb665fce893853e21ddbd3241685d7f" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a>&amp; nv::cloth::Cloth::getFactory           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the <a class="el" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a> used to create this <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="31aeac4d22831073a79d2b6da53c17ae"></a><!-- doxytag: member="nv::cloth::Cloth::getFluidDensity" ref="31aeac4d22831073a79d2b6da53c17ae" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getFluidDensity           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752" title="/brief Sets the fluid density used for air drag/lift calculations.">setFluidDensity()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="a2daf756406fd64e1b7b2174eb040367"></a><!-- doxytag: member="nv::cloth::Cloth::getFriction" ref="a2daf756406fd64e1b7b2174eb040367" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getFriction           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715" title="Set the cloth collision shape friction coefficient.">setFriction()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="8d31c57a119fb853d4ceb1e197b2351a"></a><!-- doxytag: member="nv::cloth::Cloth::getGpuParticles" ref="8d31c57a119fb853d4ceb1e197b2351a" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html">GpuParticles</a> nv::cloth::Cloth::getGpuParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns platform dependent pointers to the current GPU particle memory. 
<p>

</div>
</div><p>
<a class="anchor" name="92a76707f82caf33088f23983d5ede03"></a><!-- doxytag: member="nv::cloth::Cloth::getGravity" ref="92a76707f82caf33088f23983d5ede03" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getGravity           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns gravity set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916" title="Sets gravity in global coordinates.">setGravity()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="2dbaaaa013d7c69902c9d5eaa98f6af9"></a><!-- doxytag: member="nv::cloth::Cloth::getLiftCoefficient" ref="2dbaaaa013d7c69902c9d5eaa98f6af9" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getLiftCoefficient           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193" title="/brief Sets the air lift coefficient.">setLiftCoefficient()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="fa87c1d6ab87c5d7edbd48b5eb755659"></a><!-- doxytag: member="nv::cloth::Cloth::getLinearDrag" ref="fa87c1d6ab87c5d7edbd48b5eb755659" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getLinearDrag           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="203a19cd80d2c9897df7c02006a05cb6"></a><!-- doxytag: member="nv::cloth::Cloth::getLinearInertia" ref="203a19cd80d2c9897df7c02006a05cb6" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getLinearInertia           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6" title="Returns value set with getLinearInertia().">getLinearInertia()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="12449a7e62ac5d66149510fe01c51126"></a><!-- doxytag: member="nv::cloth::Cloth::getMotionConstraintBias" ref="12449a7e62ac5d66149510fe01c51126" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getMotionConstraintBias           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="8f26feaee081f503440e077477d51d24"></a><!-- doxytag: member="nv::cloth::Cloth::getMotionConstraints" ref="8f26feaee081f503440e077477d51d24" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;physx::PxVec4&gt; nv::cloth::Cloth::getMotionConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns reference to motion constraints (position, radius) The entire range must be written after calling this function. 
<p>

</div>
</div><p>
<a class="anchor" name="c0a1cd8a04b0e44580f53185bd3a7438"></a><!-- doxytag: member="nv::cloth::Cloth::getMotionConstraintScale" ref="c0a1cd8a04b0e44580f53185bd3a7438" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getMotionConstraintScale           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="56bb155eacd1b0b2d1dc4803ff7c02a7"></a><!-- doxytag: member="nv::cloth::Cloth::getMotionConstraintStiffness" ref="56bb155eacd1b0b2d1dc4803ff7c02a7" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getMotionConstraintStiffness           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="c9253d7ea3e5bb8b7389c6718d1d14e7"></a><!-- doxytag: member="nv::cloth::Cloth::getNumCapsules" ref="c9253d7ea3e5bb8b7389c6718d1d14e7" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumCapsules           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of capsules (which is half the number of capsule indices). 
<p>

</div>
</div><p>
<a class="anchor" name="72d40e0b094a67c5a75c3a442aff4d88"></a><!-- doxytag: member="nv::cloth::Cloth::getNumConvexes" ref="72d40e0b094a67c5a75c3a442aff4d88" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumConvexes           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of convexMasks currently set. 
<p>

</div>
</div><p>
<a class="anchor" name="639565810f9f83088f870643c957bee3"></a><!-- doxytag: member="nv::cloth::Cloth::getNumMotionConstraints" ref="639565810f9f83088f870643c957bee3" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumMotionConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="22f4390c87ae7e40704b7e346b6c3dc4"></a><!-- doxytag: member="nv::cloth::Cloth::getNumParticleAccelerations" ref="22f4390c87ae7e40704b7e346b6c3dc4" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumParticleAccelerations           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="cf0e6ac1e540ae6d2f7a9450a42fcb18"></a><!-- doxytag: member="nv::cloth::Cloth::getNumParticles" ref="cf0e6ac1e540ae6d2f7a9450a42fcb18" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of particles simulated by this fabric. 
<p>

</div>
</div><p>
<a class="anchor" name="63c8731061e061c5d69c43c83a1f7213"></a><!-- doxytag: member="nv::cloth::Cloth::getNumPlanes" ref="63c8731061e061c5d69c43c83a1f7213" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumPlanes           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of planes currently set. 
<p>

</div>
</div><p>
<a class="anchor" name="6549b36b76a8269864c695d3b77aae63"></a><!-- doxytag: member="nv::cloth::Cloth::getNumRestPositions" ref="6549b36b76a8269864c695d3b77aae63" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumRestPositions           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="44b430eff88a119b5242e5ed87722ee0"></a><!-- doxytag: member="nv::cloth::Cloth::getNumSelfCollisionIndices" ref="44b430eff88a119b5242e5ed87722ee0" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumSelfCollisionIndices           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of self collision indices set. 
<p>

</div>
</div><p>
<a class="anchor" name="7951527b51d5e4c523c179c5c7f34d3c"></a><!-- doxytag: member="nv::cloth::Cloth::getNumSeparationConstraints" ref="7951527b51d5e4c523c179c5c7f34d3c" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumSeparationConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="4b6b1d7fd2adfbc9d7ac66bbb9c418dc"></a><!-- doxytag: member="nv::cloth::Cloth::getNumSpheres" ref="4b6b1d7fd2adfbc9d7ac66bbb9c418dc" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumSpheres           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of spheres currently set. 
<p>

</div>
</div><p>
<a class="anchor" name="9dc99e94a2d2698b7c165160dc850337"></a><!-- doxytag: member="nv::cloth::Cloth::getNumTriangles" ref="9dc99e94a2d2698b7c165160dc850337" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumTriangles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of triangles currently set. 
<p>

</div>
</div><p>
<a class="anchor" name="7b67c8d82763c26d18d52e864137f46f"></a><!-- doxytag: member="nv::cloth::Cloth::getNumVirtualParticles" ref="7b67c8d82763c26d18d52e864137f46f" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumVirtualParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="a08c88cf1855e76452a783c336d1102c"></a><!-- doxytag: member="nv::cloth::Cloth::getNumVirtualParticleWeights" ref="a08c88cf1855e76452a783c336d1102c" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getNumVirtualParticleWeights           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="5b6086a1da8379361db57d0d3f6c8655"></a><!-- doxytag: member="nv::cloth::Cloth::getParticleAccelerations" ref="5b6086a1da8379361db57d0d3f6c8655" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;physx::PxVec4&gt; nv::cloth::Cloth::getParticleAccelerations           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="b8dee849c57c802ed40234edeaa998be"></a><!-- doxytag: member="nv::cloth::Cloth::getPreviousIterationDt" ref="b8dee849c57c802ed40234edeaa998be" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getPreviousIterationDt           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the delta time used for previous iteration. 
<p>

</div>
</div><p>
<a class="anchor" name="8324ff7a6a8bcf3702f3ed51e431f89b"></a><!-- doxytag: member="nv::cloth::Cloth::getPreviousParticles" ref="8324ff7a6a8bcf3702f3ed51e431f89b" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt;const physx::PxVec4&gt; nv::cloth::Cloth::getPreviousParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the simulation particles of the previous frame. 
<p>
Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2459fe06a066953e230e6271e4dd87b0" title="Returns the simulation particles of the current frame, read only.">getCurrentParticles() const</a>. 
</div>
</div><p>
<a class="anchor" name="9b22cae129eb4d9677fdea24fa5ec486"></a><!-- doxytag: member="nv::cloth::Cloth::getPreviousParticles" ref="9b22cae129eb4d9677fdea24fa5ec486" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt;physx::PxVec4&gt; nv::cloth::Cloth::getPreviousParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the simulation particles of the previous frame. 
<p>
Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d" title="Returns the simulation particles of the current frame.">getCurrentParticles()</a>. 
</div>
</div><p>
<a class="anchor" name="78235f2aa83c32ccf35b6da0e221fe8e"></a><!-- doxytag: member="nv::cloth::Cloth::getRotation" ref="78235f2aa83c32ccf35b6da0e221fe8e" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual const physx::PxQuat&amp; nv::cloth::Cloth::getRotation           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the current rotation value that was set using <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7" title="Set the rotation of the local space simulation after next call to simulate().">setRotation()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="064c442c7588405581006d72aa3d88f9"></a><!-- doxytag: member="nv::cloth::Cloth::getSelfCollisionDistance" ref="064c442c7588405581006d72aa3d88f9" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getSelfCollisionDistance           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8" title="/brief Set the distance particles need to be separated from each other withing the...">setSelfCollisionDistance()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="209dae86682049c944b9c2ba41aeb0bf"></a><!-- doxytag: member="nv::cloth::Cloth::getSelfCollisionStiffness" ref="209dae86682049c944b9c2ba41aeb0bf" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getSelfCollisionStiffness           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1" title="/brief Set the constraint stiffness for the self collision constraints.">setSelfCollisionStiffness()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="2c02b9284fb998282226b0a57209a7d3"></a><!-- doxytag: member="nv::cloth::Cloth::getSeparationConstraints" ref="2c02b9284fb998282226b0a57209a7d3" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;physx::PxVec4&gt; nv::cloth::Cloth::getSeparationConstraints           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="a3be62e917066f2f29f64320c8286893"></a><!-- doxytag: member="nv::cloth::Cloth::getSleepAfterCount" ref="a3be62e917066f2f29f64320c8286893" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getSleepAfterCount           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="7a07e21a9b99dd3eab429569c77eac1c"></a><!-- doxytag: member="nv::cloth::Cloth::getSleepPassCount" ref="7a07e21a9b99dd3eab429569c77eac1c" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getSleepPassCount           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="6313c4680c883d3bb6e20632ebde1ab8"></a><!-- doxytag: member="nv::cloth::Cloth::getSleepTestInterval" ref="6313c4680c883d3bb6e20632ebde1ab8" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Cloth::getSleepTestInterval           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="2bd353debfdb900979395fe8870df1bf"></a><!-- doxytag: member="nv::cloth::Cloth::getSleepThreshold" ref="2bd353debfdb900979395fe8870df1bf" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getSleepThreshold           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="a7737f7ba0dfca885cfc1f1a7f651b01"></a><!-- doxytag: member="nv::cloth::Cloth::getSolverFrequency" ref="a7737f7ba0dfca885cfc1f1a7f651b01" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getSolverFrequency           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns gravity set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01" title="Returns gravity set with getSolverFrequency().*/.">getSolverFrequency()</a>.*/. 
<p>

</div>
</div><p>
<a class="anchor" name="6476ef704ed1733398ba87093bc8bb22"></a><!-- doxytag: member="nv::cloth::Cloth::getStiffnessFrequency" ref="6476ef704ed1733398ba87093bc8bb22" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getStiffnessFrequency           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="nv::cloth::Cloth::getTetherConstraintScale" ref="********************************" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getTetherConstraintScale           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91" title="Set Tether constraint scale.">setTetherConstraintScale()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="d423f35a4952860552430fea2796ce15"></a><!-- doxytag: member="nv::cloth::Cloth::getTetherConstraintStiffness" ref="d423f35a4952860552430fea2796ce15" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Cloth::getTetherConstraintStiffness           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c" title="Set Tether constraint stiffness.">setTetherConstraintStiffness()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="519a015726fbc04a7bcf60afcfe3b0ca"></a><!-- doxytag: member="nv::cloth::Cloth::getTranslation" ref="519a015726fbc04a7bcf60afcfe3b0ca" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual const physx::PxVec3&amp; nv::cloth::Cloth::getTranslation           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the current translation value that was set using <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">setTranslation()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="91e02303afccc55bba87886c1187002b"></a><!-- doxytag: member="nv::cloth::Cloth::getUserData" ref="91e02303afccc55bba87886c1187002b" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void* nv::cloth::Cloth::getUserData           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="dc98811170dedd7f79c97a5ad289aeb2"></a><!-- doxytag: member="nv::cloth::Cloth::getWindVelocity" ref="dc98811170dedd7f79c97a5ad289aeb2" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual physx::PxVec3 nv::cloth::Cloth::getWindVelocity           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d" title="/brief Set wind in global coordinates.">setWindVelocity()</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="21e5c91983c11511600cfefd68be49f9"></a><!-- doxytag: member="nv::cloth::Cloth::isAsleep" ref="21e5c91983c11511600cfefd68be49f9" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::Cloth::isAsleep           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="5afecc558e319c61e047a2bb8b113b40"></a><!-- doxytag: member="nv::cloth::Cloth::isContinuousCollisionEnabled" ref="5afecc558e319c61e047a2bb8b113b40" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::Cloth::isContinuousCollisionEnabled           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if we use ccd. 
<p>

</div>
</div><p>
<a class="anchor" name="00d6c87135168af45d1b9694433f9036"></a><!-- doxytag: member="nv::cloth::Cloth::lockParticles" ref="00d6c87135168af45d1b9694433f9036" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::lockParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Used internally to synchronize CPU and GPU particle memory. 
<p>

</div>
</div><p>
<a class="anchor" name="0844b06815e4395bbc6f9c00d2d4bb24"></a><!-- doxytag: member="nv::cloth::Cloth::operator=" ref="0844b06815e4395bbc6f9c00d2d4bb24" args="(const Cloth &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; nv::cloth::Cloth::operator=           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="21da17df59669e7a2b670823400e740b"></a><!-- doxytag: member="nv::cloth::Cloth::putToSleep" ref="21da17df59669e7a2b670823400e740b" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::putToSleep           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="d041f7c80ecb193154e0ccce59e81867"></a><!-- doxytag: member="nv::cloth::Cloth::setAcceleationFilterWidth" ref="d041f7c80ecb193154e0ccce59e81867" args="(uint32_t)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setAcceleationFilterWidth           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="0878320c241cee9876c2ac3122d80cb8"></a><!-- doxytag: member="nv::cloth::Cloth::setAngularDrag" ref="0878320c241cee9876c2ac3122d80cb8" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setAngularDrag           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="e14358081c4d1f30c14f08f3c71e38b8"></a><!-- doxytag: member="nv::cloth::Cloth::setAngularInertia" ref="e14358081c4d1f30c14f08f3c71e38b8" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setAngularInertia           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">setLinearInertia()</a>, but for angular inertia. 
<p>

</div>
</div><p>
<a class="anchor" name="ee30e619014cf93c518170b4b7a96df5"></a><!-- doxytag: member="nv::cloth::Cloth::setCapsules" ref="ee30e619014cf93c518170b4b7a96df5" args="(Range&lt; const uint32_t &gt; capsules, uint32_t first, uint32_t last)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setCapsules           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>capsules</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>last</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set indices for capsule collision detection. 
<p>
The indices define the spheres that form the end points between the capsule. Every two elements in capsules define one capsule. The values currently in range[first, last[ will be replaced with the content of capsules. Note that first and last are indices to whole capsules consisting of 2 indices each. So if you want to update the first two capsules (without changing the total number of capsules) you would use the following code: <div class="fragment"><pre class="fragment">        uint32_t capsules[4] = { 0,1,  1,2 }; <span class="comment">//Define indices for 2 capsules</span>
        <span class="comment">//updates the indices of the first 2 capsules in cloth</span>
        cloth-&gt;setCapsules(Range&lt;const uint32_t&gt;(capsules, capsules + 4), 0, 2);
</pre></div> 
</div>
</div><p>
<a class="anchor" name="ba9e3001d7c11d70526ef281febe8484"></a><!-- doxytag: member="nv::cloth::Cloth::setCentrifugalInertia" ref="ba9e3001d7c11d70526ef281febe8484" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setCentrifugalInertia           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">setLinearInertia()</a>, but for centrifugal inertia. 
<p>

</div>
</div><p>
<a class="anchor" name="c75a30c4f8f02312b112e9650e886edb"></a><!-- doxytag: member="nv::cloth::Cloth::setCollisionMassScale" ref="c75a30c4f8f02312b112e9650e886edb" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setCollisionMassScale           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="b05513e796029c7562a1ff6fb740e561"></a><!-- doxytag: member="nv::cloth::Cloth::setConvexes" ref="b05513e796029c7562a1ff6fb740e561" args="(Range&lt; const uint32_t &gt; convexMasks, uint32_t first, uint32_t last)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setConvexes           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>convexMasks</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>last</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Enable planes for collision. 
<p>
convexMasks must contain masks of the form (1&lt;&lt;planeIndex1)|(1&lt;&lt;planeIndex2)|...|(1&lt;&lt;planeIndexN). All planes masked in a single element of convexMasks form a single convex polyhedron. The values currently in range [first, last[ will be replaced with the content of convexMasks. 
</div>
</div><p>
<a class="anchor" name="f1e7d559fd4942d82432aeb6ab477cf6"></a><!-- doxytag: member="nv::cloth::Cloth::setDamping" ref="f1e7d559fd4942d82432aeb6ab477cf6" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setDamping           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Sets damping of local particle velocity (1/stiffnessFrequency). 
<p>
0 (default): velocity is unaffected, 1: velocity is zeroed 
</div>
</div><p>
<a class="anchor" name="fce3065c14afac4e5cf6e93b5d60a007"></a><!-- doxytag: member="nv::cloth::Cloth::setDragCoefficient" ref="fce3065c14afac4e5cf6e93b5d60a007" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setDragCoefficient           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Sets the air drag coefficient. 
<p>

</div>
</div><p>
<a class="anchor" name="23bb80bd7b7acd3caa9c2b792c41a752"></a><!-- doxytag: member="nv::cloth::Cloth::setFluidDensity" ref="23bb80bd7b7acd3caa9c2b792c41a752" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setFluidDensity           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Sets the fluid density used for air drag/lift calculations. 
<p>

</div>
</div><p>
<a class="anchor" name="c10abfe96f96b65c9b3a5f37fee68715"></a><!-- doxytag: member="nv::cloth::Cloth::setFriction" ref="c10abfe96f96b65c9b3a5f37fee68715" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setFriction           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set the cloth collision shape friction coefficient. 
<p>

</div>
</div><p>
<a class="anchor" name="0223c7c82b616a6df01d7a4ffb57d916"></a><!-- doxytag: member="nv::cloth::Cloth::setGravity" ref="0223c7c82b616a6df01d7a4ffb57d916" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setGravity           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Sets gravity in global coordinates. 
<p>

</div>
</div><p>
<a class="anchor" name="17b5a40330eb57bdc495a2eb0d713193"></a><!-- doxytag: member="nv::cloth::Cloth::setLiftCoefficient" ref="17b5a40330eb57bdc495a2eb0d713193" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setLiftCoefficient           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Sets the air lift coefficient. 
<p>

</div>
</div><p>
<a class="anchor" name="e97296e9eec127303d96b4febe90b43e"></a><!-- doxytag: member="nv::cloth::Cloth::setLinearDrag" ref="e97296e9eec127303d96b4febe90b43e" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setLinearDrag           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="eb427bb61aac45279fd32f3c0dc5b66c"></a><!-- doxytag: member="nv::cloth::Cloth::setLinearInertia" ref="eb427bb61aac45279fd32f3c0dc5b66c" args="(const physx::PxVec3 &amp;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setLinearInertia           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set the portion of local frame linear acceleration applied to particles. 
<p>
0: particles are unaffected, 1 (default): physically correct. 
</div>
</div><p>
<a class="anchor" name="349196b772aa39e8f3575baaf5dc35d6"></a><!-- doxytag: member="nv::cloth::Cloth::setMotionConstraintScaleBias" ref="349196b772aa39e8f3575baaf5dc35d6" args="(float scale, float bias)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setMotionConstraintScaleBias           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>scale</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>bias</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="c8d1af1c6df5123d5c81331647b24a67"></a><!-- doxytag: member="nv::cloth::Cloth::setMotionConstraintStiffness" ref="c8d1af1c6df5123d5c81331647b24a67" args="(float stiffness)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setMotionConstraintStiffness           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>stiffness</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="ea95e56cb73720970f79903dcffc8360"></a><!-- doxytag: member="nv::cloth::Cloth::setPhaseConfig" ref="ea95e56cb73720970f79903dcffc8360" args="(Range&lt; const PhaseConfig &gt; configs)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setPhaseConfig           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const <a class="el" href="structnv_1_1cloth_1_1_phase_config.html">PhaseConfig</a> &gt;&nbsp;</td>
          <td class="paramname"> <em>configs</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="204fe4d80dd980b3fe45ec98270ebcd4"></a><!-- doxytag: member="nv::cloth::Cloth::setPlanes" ref="204fe4d80dd980b3fe45ec98270ebcd4" args="(Range&lt; const physx::PxVec4 &gt; planes, uint32_t first, uint32_t last)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setPlanes           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>planes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>last</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Sets plane values to be used with convex collision detection. 
<p>
The planes are specified in the form ax + by + cz + d = 0, where elements in planes contain PxVec4(x,y,z,d). [x,y,z] is required to be normalized. The values currently in range [first, last[ will be replaced with the content of planes. Use setConvexes to enable planes for collision detection. 
</div>
</div><p>
<a class="anchor" name="263e8beebed6fb96f06bf2688a15ad1c"></a><!-- doxytag: member="nv::cloth::Cloth::setRestPositions" ref="263e8beebed6fb96f06bf2688a15ad1c" args="(Range&lt; const physx::PxVec4 &gt;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setRestPositions           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="f86beb891c025a1e4cfd1135e9ad8ae7"></a><!-- doxytag: member="nv::cloth::Cloth::setRotation" ref="f86beb891c025a1e4cfd1135e9ad8ae7" args="(const physx::PxQuat &amp;rot)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setRotation           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxQuat &amp;&nbsp;</td>
          <td class="paramname"> <em>rot</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set the rotation of the local space simulation after next call to simulate(). 
<p>
Similar to <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">Cloth::setTranslation()</a>. The applied force is proportional to the value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8" title="Similar to setLinearInertia(), but for angular inertia.">Cloth::setAngularInertia()</a> and <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484" title="Similar to setLinearInertia(), but for centrifugal inertia.">Cloth::setCentrifugalInertia()</a>. 
</div>
</div><p>
<a class="anchor" name="8677510130ff4438306d20a413abd5d8"></a><!-- doxytag: member="nv::cloth::Cloth::setSelfCollisionDistance" ref="8677510130ff4438306d20a413abd5d8" args="(float distance)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSelfCollisionDistance           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>distance</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Set the distance particles need to be separated from each other withing the cloth. 
<p>

</div>
</div><p>
<a class="anchor" name="e77122c9d483539afe4b944429d5d464"></a><!-- doxytag: member="nv::cloth::Cloth::setSelfCollisionIndices" ref="e77122c9d483539afe4b944429d5d464" args="(Range&lt; const uint32_t &gt;)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSelfCollisionIndices           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set self collision indices. 
<p>
Each index in the range indicates that the particle at that index should be used for self collision. If set to an empty range (default) all particles will be used. 
</div>
</div><p>
<a class="anchor" name="b9988307f35da068e3d2ff08b56d95a1"></a><!-- doxytag: member="nv::cloth::Cloth::setSelfCollisionStiffness" ref="b9988307f35da068e3d2ff08b56d95a1" args="(float stiffness)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSelfCollisionStiffness           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>stiffness</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Set the constraint stiffness for the self collision constraints. 
<p>

</div>
</div><p>
<a class="anchor" name="7ef6797d557a31d9380835d26a894f15"></a><!-- doxytag: member="nv::cloth::Cloth::setSleepAfterCount" ref="7ef6797d557a31d9380835d26a894f15" args="(uint32_t)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSleepAfterCount           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="de65cf56e6b20d5a44a222b9e28ecf2f"></a><!-- doxytag: member="nv::cloth::Cloth::setSleepTestInterval" ref="de65cf56e6b20d5a44a222b9e28ecf2f" args="(uint32_t)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSleepTestInterval           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="1a34c62e0891496b949194556dc729f1"></a><!-- doxytag: member="nv::cloth::Cloth::setSleepThreshold" ref="1a34c62e0891496b949194556dc729f1" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSleepThreshold           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="b17d1cc294a3792c5c35e4ab353fac29"></a><!-- doxytag: member="nv::cloth::Cloth::setSolverFrequency" ref="b17d1cc294a3792c5c35e4ab353fac29" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSolverFrequency           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set target solver iterations per second. 
<p>
At least 1 iteration per frame will be solved regardless of the value set. 
</div>
</div><p>
<a class="anchor" name="31daeab54984168c8940f421c908e80f"></a><!-- doxytag: member="nv::cloth::Cloth::setSpheres" ref="31daeab54984168c8940f421c908e80f" args="(Range&lt; const physx::PxVec4 &gt; spheres, uint32_t first, uint32_t last)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setSpheres           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>spheres</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>last</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set spheres for collision detection. 
<p>
Elements of spheres contain PxVec4(x,y,z,r) where [x,y,z] is the center and r the radius of the sphere. The values currently in range[first, last[ will be replaced with the content of spheres. <div class="fragment"><pre class="fragment">        cloth-&gt;setSpheres(Range&lt;const PxVec4&gt;(), 0, cloth-&gt;getNumSpheres()); <span class="comment">//Removes all spheres</span>
</pre></div> 
</div>
</div><p>
<a class="anchor" name="8a4512e945fa62ffd64d291686cc59a8"></a><!-- doxytag: member="nv::cloth::Cloth::setStiffnessFrequency" ref="8a4512e945fa62ffd64d291686cc59a8" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setStiffnessFrequency           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="2e2b3e5e4542417c61bbe65064b6ba91"></a><!-- doxytag: member="nv::cloth::Cloth::setTetherConstraintScale" ref="2e2b3e5e4542417c61bbe65064b6ba91" args="(float scale)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setTetherConstraintScale           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>scale</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set Tether constraint scale. 
<p>
1.0 is the original scale of the <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>. 0.0 disables tether constraints in the <a class="el" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a>. 
</div>
</div><p>
<a class="anchor" name="7e8eadb5e98ea146ad2e079cfddeeb0c"></a><!-- doxytag: member="nv::cloth::Cloth::setTetherConstraintStiffness" ref="7e8eadb5e98ea146ad2e079cfddeeb0c" args="(float stiffness)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setTetherConstraintStiffness           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>stiffness</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set Tether constraint stiffness. 
<p>
1.0 is the default. &lt;1.0 makes the constraints behave springy. 
</div>
</div><p>
<a class="anchor" name="080cb97581d6e37079b6f62a7abfced0"></a><!-- doxytag: member="nv::cloth::Cloth::setTranslation" ref="080cb97581d6e37079b6f62a7abfced0" args="(const physx::PxVec3 &amp;trans)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setTranslation           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname"> <em>trans</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set the translation of the local space simulation after next call to simulate(). 
<p>
This applies a force to make the cloth behave as if it was moved through space. This does not move the particles as they are in local space. Use the graphics transformation matrices to render the cloth in the proper location. The applied force is proportional to the value set with <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">Cloth::setLinearInertia()</a>. 
</div>
</div><p>
<a class="anchor" name="dc7593d195a36d040181fbaa0c21ead6"></a><!-- doxytag: member="nv::cloth::Cloth::setTriangles" ref="dc7593d195a36d040181fbaa0c21ead6" args="(Range&lt; const physx::PxVec3 &gt; triangles, Range&lt; const physx::PxVec3 &gt;, uint32_t first)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setTriangles           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname"> <em>triangles</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname">, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="e5d69e455fee1bdd9f92ef888e8d2514"></a><!-- doxytag: member="nv::cloth::Cloth::setTriangles" ref="e5d69e455fee1bdd9f92ef888e8d2514" args="(Range&lt; const physx::PxVec3 &gt; triangles, uint32_t first, uint32_t last)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setTriangles           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname"> <em>triangles</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>last</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set triangles for collision. 
<p>
Each triangle in the list is defined by of 3 vertices. The values currently in range [first, last[ will be replaced with the content of triangles. 
</div>
</div><p>
<a class="anchor" name="e7c0b099e90d409a65ee14d6f77e57c5"></a><!-- doxytag: member="nv::cloth::Cloth::setUserData" ref="e7c0b099e90d409a65ee14d6f77e57c5" args="(void *)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setUserData           </td>
          <td>(</td>
          <td class="paramtype">void *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Set user data. 
<p>
Not used internally. 
</div>
</div><p>
<a class="anchor" name="285c31837f64c3cd60fce8ba269fe3f1"></a><!-- doxytag: member="nv::cloth::Cloth::setVirtualParticles" ref="285c31837f64c3cd60fce8ba269fe3f1" args="(Range&lt; const uint32_t[4]&gt; indices, Range&lt; const physx::PxVec3 &gt; weights)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setVirtualParticles           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t[4]&gt;&nbsp;</td>
          <td class="paramname"> <em>indices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname"> <em>weights</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="dd843ef612805153bdf04f2229697e0d"></a><!-- doxytag: member="nv::cloth::Cloth::setWindVelocity" ref="dd843ef612805153bdf04f2229697e0d" args="(physx::PxVec3)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::setWindVelocity           </td>
          <td>(</td>
          <td class="paramtype">physx::PxVec3&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
/brief Set wind in global coordinates. 
<p>
Acts on the fabric's triangles. 
</div>
</div><p>
<a class="anchor" name="86e8ce29e3d64732d8940857115f397e"></a><!-- doxytag: member="nv::cloth::Cloth::teleport" ref="86e8ce29e3d64732d8940857115f397e" args="(const physx::PxVec3 &amp;delta)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::teleport           </td>
          <td>(</td>
          <td class="paramtype">const physx::PxVec3 &amp;&nbsp;</td>
          <td class="paramname"> <em>delta</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Adjust the position of the cloth without affecting the dynamics (to call after a world origin shift, for example). 
<p>

</div>
</div><p>
<a class="anchor" name="ffc4d89e66969c5fcf3b4ac2af01fe9d"></a><!-- doxytag: member="nv::cloth::Cloth::unlockParticles" ref="ffc4d89e66969c5fcf3b4ac2af01fe9d" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::unlockParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Used internally to synchronize CPU and GPU particle memory. 
<p>

</div>
</div><p>
<a class="anchor" name="4294ed2b0a515600e9448264cc0377e3"></a><!-- doxytag: member="nv::cloth::Cloth::wakeUp" ref="4294ed2b0a515600e9448264cc0377e3" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Cloth::wakeUp           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_8h-source.html">Cloth.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
