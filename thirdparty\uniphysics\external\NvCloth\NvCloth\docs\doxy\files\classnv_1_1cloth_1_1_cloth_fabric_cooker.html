<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothFabricCooker Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">ClothFabricCooker</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothFabricCooker Class Reference<br>
<small>
[<a class="el" href="group__extensions.html">Extensions</a>]</small>
</h1><!-- doxytag: class="nv::cloth::ClothFabricCooker" -->Use NvClothCreateFabricCooker() to create an implemented instance.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth_fabric_cooker-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#88737e0da4286e2138095a22e4f9cf96">cook</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;desc, physx::PxVec3 gravity, bool useGeodesicTether=true)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Cooks a triangle mesh to a <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html" title="References all the data required to create a fabric.">ClothFabricDesc</a>.  <a href="#88737e0da4286e2138095a22e4f9cf96"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">CookedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#63c669356e99a97a7ed90788aec9b8f4">getCookedData</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns fabric cooked data for creating fabrics.  <a href="#63c669356e99a97a7ed90788aec9b8f4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">ClothFabricDesc</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#24e4bd5204366e6f8ddcfb7e27d4d19f">getDescriptor</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the fabric descriptor to create the fabric.  <a href="#24e4bd5204366e6f8ddcfb7e27d4d19f"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#85494d38c8c720f16df476a9dd9df3be">save</a> (physx::PxOutputStream &amp;stream, bool platformMismatch) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Saves the fabric data to a platform and version dependent stream.  <a href="#85494d38c8c720f16df476a9dd9df3be"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#4fdce19d20d9db5a1e3db8b6595061de">~ClothFabricCooker</a> ()</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Use NvClothCreateFabricCooker() to create an implemented instance. <hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="4fdce19d20d9db5a1e3db8b6595061de"></a><!-- doxytag: member="nv::cloth::ClothFabricCooker::~ClothFabricCooker" ref="4fdce19d20d9db5a1e3db8b6595061de" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::ClothFabricCooker::~ClothFabricCooker           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="88737e0da4286e2138095a22e4f9cf96"></a><!-- doxytag: member="nv::cloth::ClothFabricCooker::cook" ref="88737e0da4286e2138095a22e4f9cf96" args="(const ClothMeshDesc &amp;desc, physx::PxVec3 gravity, bool useGeodesicTether=true)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::ClothFabricCooker::cook           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>desc</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">physx::PxVec3&nbsp;</td>
          <td class="paramname"> <em>gravity</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname"> <em>useGeodesicTether</em> = <code>true</code></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Cooks a triangle mesh to a <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html" title="References all the data required to create a fabric.">ClothFabricDesc</a>. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>desc</em>&nbsp;</td><td>The cloth mesh descriptor on which the generation of the cooked mesh depends. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>gravity</em>&nbsp;</td><td>A normalized vector which specifies the direction of gravity. This information allows the cooker to generate a fabric with higher quality simulation behavior. The gravity vector should point in the direction gravity will be pulling towards in the most common situation/at rest. e.g. For flags it might be beneficial to set the gravity horizontal if they are cooked in landscape orientation, as a flag will hang in portrait orientation at rest. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>useGeodesicTether</em>&nbsp;</td><td>A flag to indicate whether to compute geodesic distance for tether constraints. </td></tr>
  </table>
</dl>
<dl class="note" compact><dt><b>Note:</b></dt><dd>The geodesic option for tether only works for manifold input. For non-manifold input, a simple Euclidean distance will be used. For more detailed cooker status for such cases, try running ClothGeodesicTetherCooker directly. </dd></dl>

</div>
</div><p>
<a class="anchor" name="63c669356e99a97a7ed90788aec9b8f4"></a><!-- doxytag: member="nv::cloth::ClothFabricCooker::getCookedData" ref="63c669356e99a97a7ed90788aec9b8f4" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">CookedData</a> nv::cloth::ClothFabricCooker::getCookedData           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns fabric cooked data for creating fabrics. 
<p>

</div>
</div><p>
<a class="anchor" name="24e4bd5204366e6f8ddcfb7e27d4d19f"></a><!-- doxytag: member="nv::cloth::ClothFabricCooker::getDescriptor" ref="24e4bd5204366e6f8ddcfb7e27d4d19f" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">ClothFabricDesc</a> nv::cloth::ClothFabricCooker::getDescriptor           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the fabric descriptor to create the fabric. 
<p>

</div>
</div><p>
<a class="anchor" name="85494d38c8c720f16df476a9dd9df3be"></a><!-- doxytag: member="nv::cloth::ClothFabricCooker::save" ref="85494d38c8c720f16df476a9dd9df3be" args="(physx::PxOutputStream &amp;stream, bool platformMismatch) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::ClothFabricCooker::save           </td>
          <td>(</td>
          <td class="paramtype">physx::PxOutputStream &amp;&nbsp;</td>
          <td class="paramname"> <em>stream</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname"> <em>platformMismatch</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Saves the fabric data to a platform and version dependent stream. 
<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
