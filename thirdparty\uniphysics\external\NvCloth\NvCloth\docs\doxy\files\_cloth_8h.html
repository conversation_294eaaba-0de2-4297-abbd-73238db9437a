<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Cloth.h File Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Cloth.h File Reference</h1><code>#include &quot;<a class="el" href="_range_8h-source.html">NvCloth/Range.h</a>&quot;</code><br>
<code>#include &quot;<a class="el" href="_phase_config_8h-source.html">NvCloth/PhaseConfig.h</a>&quot;</code><br>
<code>#include &lt;foundation/PxVec3.h&gt;</code><br>
<code>#include &quot;<a class="el" href="_allocator_8h-source.html">NvCloth/Allocator.h</a>&quot;</code><br>

<p>
<a href="_cloth_8h-source.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Classes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html">nv::cloth::GpuParticles</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">nv::cloth::MappedRange&lt; T &gt;</a></td></tr>

<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">MappedRange&lt; const physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#de01dfaec34e24b894b355674dc4f4fe">nv::cloth::readCurrentParticles</a> (const Cloth &amp;cloth)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">MappedRange&lt; const physx::PxVec4 &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#6d165e6041ce38d96dbb64b274797091">nv::cloth::readPreviousParticles</a> (const Cloth &amp;cloth)</td></tr>

</table>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
