#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Tantien Engine Editor Demo
数据驱动的可视化游戏引擎演示程序
"""

import sys
import time
import os
from datetime import datetime

class TantienEditor:
    def __init__(self):
        self.version = "Demo v1.0"
        self.editors = {
            "rendergraph": "渲染图编辑器",
            "shadergraph": "着色器图编辑器", 
            "scenegraph": "场景图编辑器",
            "physicsgraph": "物理图编辑器",
            "pbrgraph": "PBR材质编辑器",
            "noisegraph": "噪声生成器",
            "terraingraph": "地形生成器",
            "sdfgraph": "SDF编辑器",
            "particlegraph": "粒子系统编辑器",
            "animgraph": "动画图编辑器",
            "audiograph": "音频图编辑器",
            "scriptgraph": "脚本图编辑器",
            "uigraph": "UI图编辑器",
            "flowgraph": "流程图编辑器"
        }
    
    def print_header(self):
        print("=" * 60)
        print("🎮 TANTIEN ENGINE EDITOR 🎮")
        print("数据驱动的可视化游戏引擎")
        print(f"版本: {self.version}")
        print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
    
    def print_system_info(self):
        print("\n📊 系统信息:")
        print(f"- 操作系统: {os.name}")
        print(f"- Python版本: {sys.version.split()[0]}")
        print(f"- 工作目录: {os.getcwd()}")
        print(f"- 可用编辑器: {len(self.editors)}个")
    
    def show_main_menu(self):
        print("\n🎨 可用的编辑器模式:")
        for i, (key, name) in enumerate(self.editors.items(), 1):
            print(f"{i:2d}. {key:<15} - {name}")
        print(f"{len(self.editors)+1:2d}. exit            - 退出程序")
    
    def run_editor(self, mode):
        if mode not in self.editors:
            print(f"❌ 未知的编辑器模式: {mode}")
            return
        
        print(f"\n🚀 启动编辑器: {self.editors[mode]}")
        print("=" * 50)
        
        if mode == "rendergraph":
            self.demo_rendergraph()
        elif mode == "shadergraph":
            self.demo_shadergraph()
        elif mode == "scenegraph":
            self.demo_scenegraph()
        elif mode == "physicsgraph":
            self.demo_physicsgraph()
        else:
            self.demo_generic(mode)
    
    def demo_rendergraph(self):
        print("🎨 渲染图编辑器演示")
        print("\n节点类型:")
        nodes = [
            ("输入节点", ["场景几何", "纹理资源", "光照数据", "相机参数"]),
            ("处理节点", ["光照计算", "阴影映射", "后处理效果", "色调映射"]),
            ("输出节点", ["帧缓冲", "屏幕显示", "文件导出"])
        ]
        
        for category, items in nodes:
            print(f"  📁 {category}:")
            for item in items:
                print(f"    • {item}")
        
        print("\n🔄 示例渲染管线:")
        pipeline = [
            "场景输入", "几何变换", "光照计算", "阴影处理", 
            "材质着色", "后处理", "色调映射", "屏幕输出"
        ]
        print(" -> ".join(pipeline))
        
        print("\n✨ 特性:")
        print("  • 实时预览渲染结果")
        print("  • 热重载着色器代码")
        print("  • 性能分析工具")
        print("  • 多平台渲染支持 (OpenGL/Vulkan)")
    
    def demo_shadergraph(self):
        print("🔧 着色器图编辑器演示")
        print("\n支持的着色器类型:")
        shaders = ["顶点着色器", "片段着色器", "几何着色器", "计算着色器"]
        for shader in shaders:
            print(f"  • {shader}")
        
        print("\n🧮 数学节点库:")
        math_nodes = [
            "基础运算 (+, -, *, /)", "三角函数 (sin, cos, tan)",
            "向量运算 (dot, cross, normalize)", "矩阵变换",
            "噪声函数 (Perlin, Simplex)", "插值函数 (lerp, smoothstep)"
        ]
        for node in math_nodes:
            print(f"  • {node}")
        
        print("\n📝 示例GLSL代码生成:")
        print("```glsl")
        print("// 自动生成的片段着色器")
        print("in vec2 uv;")
        print("out vec4 fragColor;")
        print("uniform sampler2D mainTexture;")
        print("")
        print("void main() {")
        print("    vec3 color = texture(mainTexture, uv).rgb;")
        print("    fragColor = vec4(color, 1.0);")
        print("}")
        print("```")
    
    def demo_scenegraph(self):
        print("🌍 场景图编辑器演示")
        print("\n场景层次结构:")
        scene_tree = [
            "🌍 根场景",
            "  📷 主相机",
            "  💡 光照组",
            "    ☀️ 方向光 (太阳)",
            "    💡 点光源 x3",
            "  🏠 场景对象",
            "    🧊 几何体组",
            "      📦 立方体",
            "      ⚪ 球体",
            "      📐 平面",
            "    🎨 材质组",
            "      🔴 漫反射材质",
            "      ✨ 金属材质",
            "      🌊 透明材质"
        ]
        
        for item in scene_tree:
            print(item)
        
        print("\n🎮 交互功能:")
        print("  • 拖拽移动对象")
        print("  • 实时变换编辑")
        print("  • 材质参数调节")
        print("  • 光照效果预览")
    
    def demo_physicsgraph(self):
        print("⚡ 物理图编辑器演示")
        print("\n物理系统组件:")
        physics_components = [
            "刚体 (Rigidbody)", "碰撞器 (Collider)", 
            "关节 (Joint)", "力场 (Force Field)",
            "流体 (Fluid)", "软体 (Soft Body)"
        ]
        
        for component in physics_components:
            print(f"  • {component}")
        
        print("\n🔬 物理引擎:")
        print("  • Bullet Physics 3D")
        print("  • Box2D 2D")
        print("  • 自定义物理求解器")
        
        print("\n🎯 应用场景:")
        print("  • 游戏物理仿真")
        print("  • 工程力学分析")
        print("  • 流体动力学")
        print("  • 粒子系统")
    
    def demo_generic(self, mode):
        print(f"🔧 {self.editors[mode]}演示")
        print("\n核心功能:")
        print("  • 可视化节点编程")
        print("  • 拖拽式界面设计")
        print("  • 实时参数调整")
        print("  • 热重载支持")
        print("  • 数据流可视化")
        
        print("\n🔗 VES脚本集成:")
        print("  • 自定义脚本语言")
        print("  • C++无缝绑定")
        print("  • 虚拟机执行")
        print("  • 调试工具支持")
    
    def interactive_mode(self):
        self.show_main_menu()
        
        while True:
            try:
                choice = input(f"\n请选择编辑器模式 (1-{len(self.editors)+1}): ").strip()
                
                if choice in ['exit', 'quit', str(len(self.editors)+1)]:
                    break
                
                try:
                    index = int(choice) - 1
                    if 0 <= index < len(self.editors):
                        mode = list(self.editors.keys())[index]
                        self.run_editor(mode)
                    else:
                        print("❌ 无效选择，请重新输入")
                        continue
                except ValueError:
                    # 尝试直接匹配编辑器名称
                    if choice in self.editors:
                        self.run_editor(choice)
                    else:
                        print("❌ 无效选择，请重新输入")
                        continue
                
                input("\n按回车键继续...")
                
            except KeyboardInterrupt:
                print("\n\n👋 用户中断，退出程序")
                break
            except EOFError:
                print("\n\n👋 输入结束，退出程序")
                break

def main():
    editor = TantienEditor()
    editor.print_header()
    editor.print_system_info()
    
    if len(sys.argv) > 1:
        # 命令行模式
        mode = sys.argv[1].lower()
        if mode in editor.editors:
            editor.run_editor(mode)
        else:
            print(f"❌ 未知的编辑器模式: {mode}")
            print("可用模式:", ", ".join(editor.editors.keys()))
    else:
        # 交互模式
        editor.interactive_mode()
    
    print("\n🎉 感谢使用Tantien引擎编辑器！")
    print("项目地址: https://github.com/TanTien-Engine/tantien")

if __name__ == "__main__":
    main()
