<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members - Functions</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li class="current"><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
      <li><a href="functions_rela.html"><span>Related&nbsp;Functions</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions_func.html#index_a"><span>a</span></a></li>
      <li><a href="functions_func_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_func_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_func_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_func_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_func_0x66.html#index_f"><span>f</span></a></li>
      <li><a href="functions_func_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_func_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_func_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_func_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_func_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_func_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_func_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_func_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_func_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_func_0x72.html#index_r"><span>r</span></a></li>
      <li class="current"><a href="functions_func_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_func_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_func_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_func_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="functions_func_0x7e.html#index_~"><span>~</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_s">- s -</a></h3><ul>
<li>save()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#85494d38c8c720f16df476a9dd9df3be">nv::cloth::ClothFabricCooker</a>
<li>scaleRestvalues()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#8343cbc315361fc0ebb1322009076c86">nv::cloth::Fabric</a>
<li>scaleTetherLengths()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#b884bf893050c00ec8bacb25a5dd76a0">nv::cloth::Fabric</a>
<li>setAcceleationFilterWidth()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#d041f7c80ecb193154e0ccce59e81867">nv::cloth::Cloth</a>
<li>setAngularDrag()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#0878320c241cee9876c2ac3122d80cb8">nv::cloth::Cloth</a>
<li>setAngularInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8">nv::cloth::Cloth</a>
<li>setCapsules()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ee30e619014cf93c518170b4b7a96df5">nv::cloth::Cloth</a>
<li>setCentrifugalInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484">nv::cloth::Cloth</a>
<li>setCollisionMassScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c75a30c4f8f02312b112e9650e886edb">nv::cloth::Cloth</a>
<li>setConvexes()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b05513e796029c7562a1ff6fb740e561">nv::cloth::Cloth</a>
<li>setDamping()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6">nv::cloth::Cloth</a>
<li>setDragCoefficient()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007">nv::cloth::Cloth</a>
<li>setFluidDensity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752">nv::cloth::Cloth</a>
<li>setFriction()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715">nv::cloth::Cloth</a>
<li>setGravity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916">nv::cloth::Cloth</a>
<li>setInterCollisionDistance()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#5043adf6727bf66b966de6393e7d67d9">nv::cloth::Solver</a>
<li>setInterCollisionFilter()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#31b3d4d36f2025f10cb04a32e28fada4">nv::cloth::Solver</a>
<li>setInterCollisionNbIterations()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#3046ea1153c1f9decfc161155cc9810b">nv::cloth::Solver</a>
<li>setInterCollisionStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#4f15accbaeff04edbebd31bf7dd9be3e">nv::cloth::Solver</a>
<li>setLiftCoefficient()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193">nv::cloth::Cloth</a>
<li>setLinearDrag()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e97296e9eec127303d96b4febe90b43e">nv::cloth::Cloth</a>
<li>setLinearInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c">nv::cloth::Cloth</a>
<li>setMotionConstraintScaleBias()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#349196b772aa39e8f3575baaf5dc35d6">nv::cloth::Cloth</a>
<li>setMotionConstraintStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c8d1af1c6df5123d5c81331647b24a67">nv::cloth::Cloth</a>
<li>setPhaseConfig()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ea95e56cb73720970f79903dcffc8360">nv::cloth::Cloth</a>
<li>setPlanes()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#204fe4d80dd980b3fe45ec98270ebcd4">nv::cloth::Cloth</a>
<li>setRestPositions()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#263e8beebed6fb96f06bf2688a15ad1c">nv::cloth::Cloth</a>
<li>setRotation()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7">nv::cloth::Cloth</a>
<li>setSelfCollisionDistance()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8">nv::cloth::Cloth</a>
<li>setSelfCollisionIndices()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e77122c9d483539afe4b944429d5d464">nv::cloth::Cloth</a>
<li>setSelfCollisionStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1">nv::cloth::Cloth</a>
<li>setSleepAfterCount()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7ef6797d557a31d9380835d26a894f15">nv::cloth::Cloth</a>
<li>setSleepTestInterval()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#de65cf56e6b20d5a44a222b9e28ecf2f">nv::cloth::Cloth</a>
<li>setSleepThreshold()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#1a34c62e0891496b949194556dc729f1">nv::cloth::Cloth</a>
<li>setSolverFrequency()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b17d1cc294a3792c5c35e4ab353fac29">nv::cloth::Cloth</a>
<li>setSpheres()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#31daeab54984168c8940f421c908e80f">nv::cloth::Cloth</a>
<li>setStiffnessFrequency()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8a4512e945fa62ffd64d291686cc59a8">nv::cloth::Cloth</a>
<li>setTetherConstraintScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91">nv::cloth::Cloth</a>
<li>setTetherConstraintStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c">nv::cloth::Cloth</a>
<li>setToDefault()
: <a class="el" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98">nv::cloth::ClothFabricDesc</a>
, <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#d269d7bebc10b54088fc73e77c1372dd">nv::cloth::ClothMeshDesc</a>
<li>setTranslation()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0">nv::cloth::Cloth</a>
<li>setTriangles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc7593d195a36d040181fbaa0c21ead6">nv::cloth::Cloth</a>
<li>setUserData()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e7c0b099e90d409a65ee14d6f77e57c5">nv::cloth::Cloth</a>
<li>setVirtualParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#285c31837f64c3cd60fce8ba269fe3f1">nv::cloth::Cloth</a>
<li>setWindVelocity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d">nv::cloth::Cloth</a>
<li>simulateChunk()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#62d209d861c8f5aa0523536d851de093">nv::cloth::Solver</a>
<li>size()
: <a class="el" href="structnv_1_1cloth_1_1_range.html#0ef526ff1b8eef5c117ad0e892ab5d24">nv::cloth::Range&lt; T &gt;</a>
<li>Solver()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#21b6b117db42d8a3206cee521e6af4b0">nv::cloth::Solver</a>
<li>StridedData()
: <a class="el" href="structnv_1_1cloth_1_1_strided_data.html#06829ec148078b342bcf4bcdd11ff035">nv::cloth::StridedData</a>
<li>synchronizeResources()
: <a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#a2ebcef21c55d3a59e01d344e6ee917a">nv::cloth::DxContextManagerCallback</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
