#include "modules/db/EntityMap.h"

namespace tt
{

EntityMap::EntityMap()
{
}

EntityMap::~EntityMap()
{
}

void EntityMap::AddEntity(uint64_t id, const std::shared_ptr<Entity>& entity)
{
    m_entities[id] = entity;
}

void EntityMap::RemoveEntity(uint64_t id)
{
    auto it = m_entities.find(id);
    if (it != m_entities.end()) {
        m_entities.erase(it);
    }
}

std::shared_ptr<EntityMap::Entity> EntityMap::GetEntity(uint64_t id) const
{
    auto it = m_entities.find(id);
    if (it != m_entities.end()) {
        return it->second;
    }
    return nullptr;
}

std::vector<uint64_t> EntityMap::GetAllEntityIds() const
{
    std::vector<uint64_t> ids;
    ids.reserve(m_entities.size());
    
    for (const auto& pair : m_entities) {
        ids.push_back(pair.first);
    }
    
    return ids;
}

size_t EntityMap::GetEntityCount() const
{
    return m_entities.size();
}

void EntityMap::Clear()
{
    m_entities.clear();
}

bool EntityMap::HasEntity(uint64_t id) const
{
    return m_entities.find(id) != m_entities.end();
}

} // namespace tt
