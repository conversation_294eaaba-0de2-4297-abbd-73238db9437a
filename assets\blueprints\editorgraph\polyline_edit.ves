var node0 = ::geograph::nodes::polyline::<PERSON><PERSON><PERSON>()
node0.query_param("vertices").value = [  ]
node0.query_param("closed").value = false

_editor.add_node(node0, -220.81772353069, -139.67912898492)

var node1 = ::geograph::nodes::draw_geometry::DrawGeometry()
node1.query_param("skip").value = false

_editor.add_node(node1, -61.294423209673, -150.76580792514)

var node2 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node2, 285.53701876754, 661.30774000194)

var node3 = ::blueprint::nodes::array::Array()
node3.query_param("serialize").value = false

_editor.add_node(node3, -769.83883407805, 272.55203465032)

var node4 = ::blueprint::nodes::subgraph::Subgraph()
node4.load_from_file(_editor, "points_edit.ves")

_editor.add_node(node4, -4.759121578754, 337.42559511444)

var node5 = ::geograph::nodes::draw_geometry::DrawGeometry()
node5.query_param("skip").value = false

_editor.add_node(node5, -94.03481143239, -280.13725641578)

var node6 = ::geograph::nodes::line::Line()
node6.query_param("x0").value = 0
node6.query_param("y0").value = 0
node6.query_param("x1").value = 100
node6.query_param("y1").value = 100

_editor.add_node(node6, -249.49786747691, -332.78705609004)

var node7 = ::editorgraph::nodes::mouse_move::MouseMove()

_editor.add_node(node7, -258.75169581831, 659.07712753105)

var node8 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node8, -41.010340748259, 571.65630150948)

var node9 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node9, 112.67387140185, 562.40813020786)

var node10 = ::blueprint::nodes::number2::Number2()

node10.value.set(107.23966942149, 24.698347107438)

_editor.add_node(node10, -46.010449842977, 501.22227061879)

var node11 = ::blueprint::nodes::store::Store()

node11.var_name = "curr_pos"

_editor.add_node(node11, 111.26003196023, 485.23908580433)

var node12 = ::blueprint::nodes::load::Load()

node12.var_name = "curr_pos"

_editor.add_node(node12, -381.67190244923, -307.24294981888)

var node13 = ::blueprint::nodes::commentary::Commentary()

node13.set_size(781.23333740234, 288.41561889648)
node13.title = "Set curr pos"

_editor.add_node(node13, 11.550323990753, 736.94108341942)

var node14 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node14, 411.03964792043, 131.14391112646)

var node15 = ::editorgraph::nodes::mouse_right_down::MouseRightDown()

_editor.add_node(node15, -318.93162818166, 114.51030005205)

var node16 = ::blueprint::nodes::list_pop_back::ListPopBack()

_editor.add_node(node16, -80.869912070573, 67.798061555362)

var node17 = ::blueprint::nodes::commentary::Commentary()

node17.set_size(914.35882568359, 230.89582824707)
node17.title = "Pop last pos"

_editor.add_node(node17, 32.95820465447, 200.59838369898)

var node18 = ::geograph::nodes::draw_geometry::DrawGeometry()
node18.query_param("skip").value = false

_editor.add_node(node18, 656.61628224462, -150.60477988676)

var node19 = ::blueprint::nodes::store::Store()

node19.var_name = "polyline"

_editor.add_node(node19, -625.03869242749, 313.76181859782)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "polyline"

_editor.add_node(node20, -228.05641391519, 40.871919757269)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "polyline"

_editor.add_node(node21, -201.93308967982, 369.31506255258)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "polyline"

_editor.add_node(node22, -531.06937854775, -135.81068730401)

var node23 = ::blueprint::nodes::commentary::Commentary()

node23.set_size(1497.5911865234, 315.74530029297)
node23.title = "Draw"

_editor.add_node(node23, 151.12525351658, -78.406942547055)

var node24 = ::blueprint::nodes::array::Array()
node24.query_param("serialize").value = false

_editor.add_node(node24, -770.24017698424, 388.66404362992)

var node25 = ::blueprint::nodes::store::Store()

node25.var_name = "selection"

_editor.add_node(node25, -630.01560162313, 391.47465975996)

var node26 = ::blueprint::nodes::load::Load()

node26.var_name = "selection"

_editor.add_node(node26, -201.21734374585, 324.31693612757)

var node27 = ::geograph::nodes::draw_geometry::DrawGeometry()
node27.query_param("skip").value = false

_editor.add_node(node27, 820.77197290315, -150.26971978561)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "selection"

_editor.add_node(node28, 655.95536039635, -232.53321843098)

var node29 = ::blueprint::nodes::number3::Number3()

node29.value.set(0, 0, 0.91044783592224)

_editor.add_node(node29, 654.54974312594, -308.07930300466)

var node30 = ::blueprint::nodes::load::Load()

node30.var_name = "polyline"

_editor.add_node(node30, 522.63674026854, -139.03268734904)

var node31 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node31, 98.191972550383, 28.832331184863)

var node32 = ::blueprint::nodes::load::Load()

node32.var_name = "selection"

_editor.add_node(node32, -81.337258835467, 3.5326670496922)

var node33 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node33, 392.57695649516, -159.37903668319)

var node34 = ::blueprint::nodes::n_o_t::NOT()

_editor.add_node(node34, -244.07551114701, -200.56602464617)

var node35 = ::blueprint::nodes::a_n_d::AND()

_editor.add_node(node35, 225.86909485319, -261.19722832549)

var node36 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node36, 271.68583411914, 71.065967184264)

var node37 = ::blueprint::nodes::store::Store()

node37.var_name = "select_succ"

_editor.add_node(node37, 199.21430969238, 364.99389648438)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "select_succ"

_editor.add_node(node38, -75.240251020955, -364.91519442472)

var node39 = ::blueprint::nodes::n_o_t::NOT()

_editor.add_node(node39, 53.487021706317, -363.55155806108)

var node40 = ::blueprint::nodes::input::Input()

node40.var_name = "polyline"
node40.var_type = "array"

_editor.add_node(node40, -770.10176014036, 326.35343865965)

var node41 = ::blueprint::nodes::is_empty::IsEmpty()

_editor.add_node(node41, -374.2419175421, -200.54358520049)

var node42 = ::blueprint::nodes::list_back::ListBack()

_editor.add_node(node42, -381.7550782534, -262.15138110498)

var node43 = ::blueprint::nodes::input::Input()

node43.var_name = "cam_mat"
node43.var_type = "mat4"

_editor.add_node(node43, -765.37853759766, 558.7999519043)

var node44 = ::blueprint::nodes::store::Store()

node44.var_name = "cam_mat"

_editor.add_node(node44, -631.17859863281, 560.99994580078)

var node45 = ::blueprint::nodes::load::Load()

node45.var_name = "cam_mat"

_editor.add_node(node45, -201.40480684643, 561.68576772046)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "cam_mat"

_editor.add_node(node46, -378.09945497156, -125.22501738985)

var node47 = ::blueprint::nodes::load::Load()

node47.var_name = "cam_mat"

_editor.add_node(node47, 521.77399744239, -186.1921175999)

var node48 = ::blueprint::nodes::load::Load()

node48.var_name = "cam_mat"

_editor.add_node(node48, -200.52280684643, 276.47922226591)

Blueprint.connect(node43, "var", node44, "var")
Blueprint.connect(node44, "var", node48, "var")
Blueprint.connect(node44, "var", node47, "var")
Blueprint.connect(node44, "var", node46, "var")
Blueprint.connect(node44, "var", node45, "var")
Blueprint.connect(node40, "var", node19, "var")
Blueprint.connect(node19, "var", node30, "var")
Blueprint.connect(node19, "var", node22, "var")
Blueprint.connect(node22, "var", node42, "list")
Blueprint.connect(node22, "var", node41, "items")
Blueprint.connect(node41, "empty", node34, "in")
Blueprint.connect(node22, "var", node0, "vertices")
Blueprint.connect(node0, "geo", node1, "geos")
Blueprint.connect(node46, "var", node1, "cam_mat")
Blueprint.connect(node19, "var", node21, "var")
Blueprint.connect(node19, "var", node20, "var")
Blueprint.connect(node20, "var", node16, "list")
Blueprint.connect(node24, "all", node25, "var")
Blueprint.connect(node25, "var", node32, "var")
Blueprint.connect(node16, "next", node31, "prev")
Blueprint.connect(node32, "var", node31, "list")
Blueprint.connect(node25, "var", node28, "var")
Blueprint.connect(node25, "var", node26, "var")
Blueprint.connect(node21, "var", node4, "points")
Blueprint.connect(node26, "var", node4, "selection")
Blueprint.connect(node48, "var", node4, "cam_mat")
Blueprint.connect(node4, "select_succ", node37, "var")
Blueprint.connect(node37, "var", node38, "var")
Blueprint.connect(node38, "var", node39, "in")
Blueprint.connect(node34, "out", node35, "a")
Blueprint.connect(node39, "out", node35, "b")
Blueprint.connect(node1, "next", node33, "prev")
Blueprint.connect(node35, "out", node33, "cond")
Blueprint.connect(node5, "next", node33, "true")
Blueprint.connect(node33, "next", node18, "prev")
Blueprint.connect(node30, "var", node18, "geos")
Blueprint.connect(node47, "var", node18, "cam_mat")
Blueprint.connect(node18, "next", node27, "prev")
Blueprint.connect(node28, "var", node27, "geos")
Blueprint.connect(node29, "v3", node27, "color")
Blueprint.connect(node47, "var", node27, "cam_mat")
Blueprint.connect(node4, "right_rm_succ", node36, "cond")
Blueprint.connect(node31, "next", node36, "false")
Blueprint.connect(node4, "next", node14, "prev")
Blueprint.connect(node15, "event", node14, "event")
Blueprint.connect(node36, "next", node14, "action")
Blueprint.connect(node10, "v2", node11, "var")
Blueprint.connect(node11, "var", node12, "var")
Blueprint.connect(node42, "back", node6, "p0")
Blueprint.connect(node12, "var", node6, "p1")
Blueprint.connect(node6, "geo", node5, "geos")
Blueprint.connect(node46, "var", node5, "cam_mat")
Blueprint.connect(node7, "pos", node8, "screen")
Blueprint.connect(node45, "var", node8, "cam_mat")
Blueprint.connect(node8, "world", node9, "src")
Blueprint.connect(node10, "v2", node9, "dst")
Blueprint.connect(node7, "event", node2, "event")
Blueprint.connect(node9, "next", node2, "action")
