<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tantien Engine 演示</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .demo-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .demo-card h3 {
            color: #ffd700;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .node-editor {
            background: #2d3748;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }
        
        .node {
            background: #4a5568;
            border-radius: 8px;
            padding: 15px;
            margin: 10px;
            display: inline-block;
            min-width: 120px;
            text-align: center;
            position: relative;
            border: 2px solid #718096;
        }
        
        .node.input {
            background: #2b6cb0;
            border-color: #3182ce;
        }
        
        .node.process {
            background: #38a169;
            border-color: #48bb78;
        }
        
        .node.output {
            background: #d69e2e;
            border-color: #ed8936;
        }
        
        .connection {
            position: absolute;
            height: 2px;
            background: #ffd700;
            top: 50%;
            transform: translateY(-50%);
        }
        
        .features-list {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .features-list li:before {
            content: "✓ ";
            color: #48bb78;
            font-weight: bold;
        }
        
        .architecture-diagram {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .layer {
            background: rgba(255, 255, 255, 0.1);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffd700;
        }
        
        .btn {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #1a202c;
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        
        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
        
        .status {
            background: rgba(255, 0, 0, 0.2);
            border: 1px solid #ff6b6b;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .status h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .loading {
            animation: pulse 2s infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Tantien Engine</h1>
            <p>数据驱动的可视化游戏引擎演示</p>
        </div>
        
        <div class="status">
            <h3>⚠️ 编译状态</h3>
            <p>由于缺少必要的依赖项和CMake生成的项目文件，完整的引擎编译暂时无法完成。</p>
            <p>以下是基于源代码分析的功能演示和架构展示。</p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3>🎨 可视化节点编辑器</h3>
                <div class="node-editor">
                    <div class="node input">输入纹理</div>
                    <div class="connection" style="left: 140px; width: 60px;"></div>
                    <div class="node process">模糊滤镜</div>
                    <div class="connection" style="left: 280px; width: 60px;"></div>
                    <div class="node output">输出</div>
                </div>
                <p>支持14种专业编辑器：渲染图、着色器图、场景图、物理图等，通过拖拽节点创建复杂的游戏逻辑。</p>
            </div>
            
            <div class="demo-card">
                <h3>🔧 VES脚本系统</h3>
                <div style="background: #1a202c; padding: 15px; border-radius: 8px; font-family: monospace;">
                    <div style="color: #68d391;">// VES虚拟机示例</div>
                    <div style="color: #ffd700;">var render = Render.init()</div>
                    <div style="color: #ffd700;">var shader = Shader.load("basic.glsl")</div>
                    <div style="color: #ffd700;">render.draw(mesh, shader)</div>
                </div>
                <p>自定义脚本语言，支持热重载、实时编辑，与C++引擎无缝集成。</p>
            </div>
            
            <div class="demo-card">
                <h3>🎯 核心特性</h3>
                <ul class="features-list">
                    <li>数据驱动架构</li>
                    <li>实时热重载</li>
                    <li>模块化设计</li>
                    <li>跨平台支持</li>
                    <li>OpenGL/Vulkan渲染</li>
                    <li>物理仿真集成</li>
                    <li>蓝图可视化编程</li>
                    <li>SPIR-V着色器编译</li>
                </ul>
            </div>
            
            <div class="demo-card">
                <h3>🏗️ 系统架构</h3>
                <div class="architecture-diagram">
                    <div class="layer">应用层 - 14种专业编辑器</div>
                    <div class="layer">脚本层 - VES虚拟机</div>
                    <div class="layer">引擎层 - 渲染/物理/音频</div>
                    <div class="layer">平台层 - Windows/Linux/macOS</div>
                </div>
                <p>分层架构设计，每层职责清晰，便于扩展和维护。</p>
            </div>
        </div>
        
        <div class="demo-card">
            <h3>📊 技术规格</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                    <strong>渲染引擎:</strong><br>
                    UniRender抽象层<br>
                    OpenGL 4.5+<br>
                    Vulkan支持
                </div>
                <div>
                    <strong>脚本系统:</strong><br>
                    VES虚拟机<br>
                    C++绑定<br>
                    热重载支持
                </div>
                <div>
                    <strong>物理引擎:</strong><br>
                    Bullet Physics<br>
                    Box2D集成<br>
                    实时仿真
                </div>
                <div>
                    <strong>资源管理:</strong><br>
                    Assimp模型加载<br>
                    纹理压缩<br>
                    流式加载
                </div>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 40px;">
            <a href="#" class="btn" onclick="showArchitecture()">查看详细架构</a>
            <a href="#" class="btn" onclick="showSamples()">浏览示例项目</a>
            <a href="https://github.com/TanTien-Engine/tantien" class="btn" target="_blank">GitHub源码</a>
        </div>
        
        <div id="details" style="margin-top: 40px; display: none;">
            <div class="demo-card">
                <h3>🔍 构建问题分析</h3>
                <p><strong>主要问题：</strong></p>
                <ul>
                    <li>缺少CMake生成的项目文件（SPIRV-Cross、SPIRV-Tools等）</li>
                    <li>第三方库依赖路径配置问题</li>
                    <li>子模块未完全初始化</li>
                    <li>缺少预编译的库文件</li>
                </ul>
                <p><strong>解决方案：</strong></p>
                <ol>
                    <li>运行 <code>git submodule update --init --recursive</code></li>
                    <li>使用CMake生成第三方库项目文件</li>
                    <li>配置正确的库文件路径</li>
                    <li>编译所有依赖项后再编译主项目</li>
                </ol>
            </div>
        </div>
    </div>
    
    <script>
        function showArchitecture() {
            alert('Tantien引擎采用分层架构：\n\n1. 应用层：14种专业编辑器\n2. 脚本层：VES虚拟机\n3. 引擎层：渲染、物理、音频系统\n4. 平台层：跨平台抽象\n\n每层通过清晰的接口进行通信，支持模块化开发。');
        }
        
        function showSamples() {
            const samples = [
                'rendergraph - 渲染管线编辑器',
                'shadergraph - 着色器节点编辑器', 
                'scenegraph - 场景图编辑器',
                'physicsgraph - 物理仿真编辑器',
                'pbrgraph - PBR材质编辑器',
                'noisegraph - 程序化噪声生成器',
                'terraingraph - 地形生成器',
                'sdfgraph - 有向距离场编辑器'
            ];
            alert('示例项目：\n\n' + samples.join('\n'));
        }
        
        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.demo-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>
