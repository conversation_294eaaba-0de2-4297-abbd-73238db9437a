var node0 = ::geograph::nodes::circle::Circle()
node0.query_param("x").value = 0
node0.query_param("y").value = 0
node0.query_param("r").value = 100
node0.query_param("fill").value = true
node0.query_param("color").value.set(0.26609805226326, 0.26609805226326, 0)

_editor.add_node(node0, 3.0511739488911, 70.69011687193)

var node1 = ::geograph::nodes::polygon::Polygon()
node1.query_param("vertices").value = [ 0, 10, 25, 5, 25, -5, 0, -10 ]
node1.query_param("color").value.set(0.25941535830498, 0.25776588916779, 0)

_editor.add_node(node1, -336.26849470345, 125.10960733731)

var node2 = ::scenegraph::nodes::sprite::Sprite()

_editor.add_node(node2, 270.15060320872, 141.54338041109)

var node3 = ::scenegraph::nodes::sprite::Sprite()

_editor.add_node(node3, 3.4748309083121, -91.952297276608)

var node4 = ::scenegraph::nodes::transform::Transform()

_editor.add_node(node4, 205.0529484948, -258.1361051881)

var node5 = ::blueprint::nodes::store::Store()

node5.var_name = "radius"

_editor.add_node(node5, -537.7861206063, 218.35341490302)

var node6 = ::blueprint::nodes::sin::Sin()

_editor.add_node(node6, -250.7039637317, -376.41335300821)

var node7 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node7, -123.36225721041, -357.37274876162)

var node8 = ::blueprint::nodes::load::Load()

node8.var_name = "radius"

_editor.add_node(node8, -513.60859572475, -168.07532895044)

var node9 = ::blueprint::nodes::cos::Cos()

_editor.add_node(node9, -251.62294151943, -274.1673249606)

var node10 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node10, -119.74131867731, -251.07522384089)

var node11 = ::blueprint::nodes::combine::Combine()

_editor.add_node(node11, 23.856504798128, -334.16240954838)

var node12 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node12, 360.60091636952, -173.70009582318)

var node13 = ::blueprint::nodes::number::Number()

node13.value = 6.2831852

_editor.add_node(node13, -727.05773922447, -289.63319934537)

var node14 = ::blueprint::nodes::divide::Divide()

_editor.add_node(node14, -583.51838894919, -315.44563780475)

var node15 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node15, -439.16211576991, -317.03774683303)

var node16 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node16, -365.3120619776, -186.75047756226)

var node17 = ::blueprint::nodes::number::Number()

node17.value = 2.3375787734985

_editor.add_node(node17, -512.000117389, -224.31623013766)

var node18 = ::scenegraph::nodes::combine::Combine()

_editor.add_node(node18, 501.11465829896, -109.35511514963)

var node19 = ::blueprint::nodes::input::Input()

node19.var_name = "radius"
node19.var_type = "num"

_editor.add_node(node19, -683.95306537806, 218.67734852805)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "radius"

_editor.add_node(node20, -137.09469640093, 139.16734573024)

var node21 = ::blueprint::nodes::input::Input()

node21.var_name = "count"
node21.var_type = "int"

_editor.add_node(node21, -685.89679331406, 141.25433312678)

var node22 = ::blueprint::nodes::store::Store()

node22.var_name = "count"

_editor.add_node(node22, -539.28379893168, 141.54962344131)

var node23 = ::blueprint::nodes::load::Load()

node23.var_name = "count"

_editor.add_node(node23, -724.01979758523, -349.54797085849)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "count"

_editor.add_node(node24, 205.07111150568, -166.82069813121)

var node25 = ::blueprint::nodes::output::Output()

node25.var_name = "spr"
node25.var_type = "sprite"

_editor.add_node(node25, 638.63170758521, -88.259634831674)

var node26 = ::blueprint::nodes::store::Store()

node26.var_name = "color"

_editor.add_node(node26, -538.3205341959, -0.2204314912498)

var node27 = ::blueprint::nodes::load::Load()

node27.var_name = "color"

_editor.add_node(node27, 136.85195503264, 100.537709759)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "color"

_editor.add_node(node28, -137.97899913716, -112.76056221696)

var node29 = ::blueprint::nodes::combine::Combine()

_editor.add_node(node29, 22.795033432978, -513.47645491203)

var node30 = ::blueprint::nodes::load::Load()

node30.var_name = "radius"

_editor.add_node(node30, -287.50236325141, -458.1910244974)

var node31 = ::blueprint::nodes::divide::Divide()

_editor.add_node(node31, -153.61806863118, -477.19930283184)

var node32 = ::blueprint::nodes::number::Number()

node32.value = 48.859455108643

_editor.add_node(node32, -288.32878910327, -513.56292381058)

var node33 = ::blueprint::nodes::store::Store()

node33.var_name = "teeth_size"

_editor.add_node(node33, -541.17394753563, 71.432140370403)

var node34 = ::blueprint::nodes::load::Load()

node34.var_name = "teeth_size"

_editor.add_node(node34, -286.31090336559, -570.99749521641)

var node35 = ::blueprint::nodes::divide::Divide()

_editor.add_node(node35, -154.93207649647, -595.66468772642)

var node36 = ::blueprint::nodes::number::Number()

node36.value = 25

_editor.add_node(node36, -285.78738800324, -628.50679507892)

var node37 = ::geograph::nodes::polygon::Polygon()
node37.query_param("vertices").value = [ 0, 10, 20, 10, 20, -10, 0, -10 ]
node37.query_param("color").value.set(0.25941535830498, 0.25776588916779, 0.71882021427155)

_editor.add_node(node37, -335.02665205733, 22.036667709489)

var node38 = ::blueprint::nodes::switch_f::SwitchF()

_editor.add_node(node38, -138.79690736023, -29.798656382702)

var node39 = ::blueprint::nodes::input::Input()

node39.var_name = "teeth_type"
node39.var_type = "int"

_editor.add_node(node39, -337.46473678898, 210.09751392558)

var node40 = ::blueprint::nodes::input::Input()

node40.var_name = "teeth_size"
node40.var_type = "num"

_editor.add_node(node40, -684.31197330392, 70.250574170692)

var node41 = ::blueprint::nodes::input::Input()

node41.var_name = "color"
node41.var_type = "num3"

_editor.add_node(node41, -682.14233495205, -0.83288266771081)

var node42 = ::geograph::nodes::polygon::Polygon()
node42.query_param("vertices").value = [ 0, -5, 20, 5, 0, 5 ]
node42.query_param("color").value.set(0.25941535830498, 0.25776588916779, 0.71882021427155)

_editor.add_node(node42, -335.02665205733, -83.147404416732)

Blueprint.connect(node41, "var", node26, "var")
Blueprint.connect(node26, "var", node28, "var")
Blueprint.connect(node26, "var", node27, "var")
Blueprint.connect(node40, "var", node33, "var")
Blueprint.connect(node33, "var", node34, "var")
Blueprint.connect(node34, "var", node35, "a")
Blueprint.connect(node36, "v", node35, "b")
Blueprint.connect(node35, "v", node29, "x")
Blueprint.connect(node35, "v", node29, "y")
Blueprint.connect(node21, "var", node22, "var")
Blueprint.connect(node22, "var", node24, "var")
Blueprint.connect(node22, "var", node23, "var")
Blueprint.connect(node19, "var", node5, "var")
Blueprint.connect(node5, "var", node30, "var")
Blueprint.connect(node30, "var", node31, "a")
Blueprint.connect(node32, "v", node31, "b")
Blueprint.connect(node5, "var", node20, "var")
Blueprint.connect(node20, "var", node0, "raduis")
Blueprint.connect(node0, "geo", node2, "symbol")
Blueprint.connect(node27, "var", node2, "color")
Blueprint.connect(node5, "var", node8, "var")
Blueprint.connect(node8, "var", node16, "a")
Blueprint.connect(node17, "v", node16, "b")
Blueprint.connect(node13, "v", node14, "a")
Blueprint.connect(node23, "var", node14, "b")
Blueprint.connect(node14, "v", node15, "a")
Blueprint.connect(node12, "curr_item", node15, "b")
Blueprint.connect(node15, "v", node9, "v")
Blueprint.connect(node16, "v", node10, "a")
Blueprint.connect(node9, "v", node10, "b")
Blueprint.connect(node15, "v", node6, "v")
Blueprint.connect(node16, "v", node7, "a")
Blueprint.connect(node6, "v", node7, "b")
Blueprint.connect(node10, "v", node11, "x")
Blueprint.connect(node7, "v", node11, "y")
Blueprint.connect(node39, "var", node38, "type")
Blueprint.connect(node1, "geo", node38, "default")
Blueprint.connect(node1, "geo", node38, "case0")
Blueprint.connect(node37, "geo", node38, "case1")
Blueprint.connect(node42, "geo", node38, "case2")
Blueprint.connect(node38, "result", node3, "symbol")
Blueprint.connect(node28, "var", node3, "color")
Blueprint.connect(node3, "spr", node4, "spr")
Blueprint.connect(node11, "xy", node4, "translate")
Blueprint.connect(node15, "v", node4, "rotate")
Blueprint.connect(node29, "xy", node4, "scale")
Blueprint.connect(node24, "var", node12, "count")
Blueprint.connect(node4, "spr", node12, "eval")
Blueprint.connect(node2, "spr", node18, "child0")
Blueprint.connect(node12, "result", node18, "child1")
Blueprint.connect(node18, "parent", node25, "var")
