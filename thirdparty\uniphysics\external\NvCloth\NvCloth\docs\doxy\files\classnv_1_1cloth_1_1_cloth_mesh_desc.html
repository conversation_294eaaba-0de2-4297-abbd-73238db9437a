<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothMeshDesc Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothMeshDesc Class Reference</h1><!-- doxytag: class="nv::cloth::ClothMeshDesc" -->Descriptor class for a cloth mesh.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth_mesh_desc-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e475fee21a2442dd86f30d836a6ad1af">ClothMeshDesc</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">constructor sets to default.  <a href="#e475fee21a2442dd86f30d836a6ad1af"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#3dbb3dc26cddbdf72c5455a485f0a4f4">isValid</a> () const </td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if the descriptor is valid.  <a href="#3dbb3dc26cddbdf72c5455a485f0a4f4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_INLINE void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#d269d7bebc10b54088fc73e77c1372dd">setToDefault</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">(re)sets the structure to the default.  <a href="#d269d7bebc10b54088fc73e77c1372dd"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">unsigned int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a">flags</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Flags bits, combined from values of the enum MeshFlag.  <a href="#e87cb1303f9939d674b448657abd434a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de">invMasses</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Determines whether particle is simulated or static.  <a href="#73d92bee06c06b6748f1726990ca20de"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************">points</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pointer to first vertex point.  <a href="#********************************"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598">pointsStiffness</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pointer to first stiffness value in stiffnes per vertex array.  <a href="#035d7ca18e3feef858f273e0afe16598"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb">quads</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pointer to the first quad.  <a href="#791bdd04c451e10b0155563766b25cdb"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e">triangles</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Pointer to the first triangle.  <a href="#e4517fa952e6cf3ac848b1b7bc67714e"></a><br></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Descriptor class for a cloth mesh. <hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="e475fee21a2442dd86f30d836a6ad1af"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::ClothMeshDesc" ref="e475fee21a2442dd86f30d836a6ad1af" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE nv::cloth::ClothMeshDesc::ClothMeshDesc           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
constructor sets to default. 
<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="3dbb3dc26cddbdf72c5455a485f0a4f4"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::isValid" ref="3dbb3dc26cddbdf72c5455a485f0a4f4" args="() const " -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE bool nv::cloth::ClothMeshDesc::isValid           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if the descriptor is valid. 
<p>
<dl class="return" compact><dt><b>Returns:</b></dt><dd>True if the current settings are valid </dd></dl>

</div>
</div><p>
<a class="anchor" name="d269d7bebc10b54088fc73e77c1372dd"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::setToDefault" ref="d269d7bebc10b54088fc73e77c1372dd" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE void nv::cloth::ClothMeshDesc::setToDefault           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
(re)sets the structure to the default. 
<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="e87cb1303f9939d674b448657abd434a"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::flags" ref="e87cb1303f9939d674b448657abd434a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">unsigned int <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a">nv::cloth::ClothMeshDesc::flags</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Flags bits, combined from values of the enum MeshFlag. 
<p>

</div>
</div><p>
<a class="anchor" name="73d92bee06c06b6748f1726990ca20de"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::invMasses" ref="73d92bee06c06b6748f1726990ca20de" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de">nv::cloth::ClothMeshDesc::invMasses</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Determines whether particle is simulated or static. 
<p>
A positive value denotes that the particle is being simulated, zero denotes a static particle. This data is used to generate tether and zero stretch constraints. If invMasses.data is null, all particles are assumed to be simulated and no tether and zero stretch constraints are being generated. 
</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::points" ref="********************************" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************">nv::cloth::ClothMeshDesc::points</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to first vertex point. 
<p>

</div>
</div><p>
<a class="anchor" name="035d7ca18e3feef858f273e0afe16598"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::pointsStiffness" ref="035d7ca18e3feef858f273e0afe16598" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598">nv::cloth::ClothMeshDesc::pointsStiffness</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to first stiffness value in stiffnes per vertex array. 
<p>
empty if unused. 
</div>
</div><p>
<a class="anchor" name="791bdd04c451e10b0155563766b25cdb"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::quads" ref="791bdd04c451e10b0155563766b25cdb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb">nv::cloth::ClothMeshDesc::quads</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the first quad. 
<p>
These are quadruples of 0 based indices: vert0 vert1 vert2 vert3 vert0 vert1 vert2 vert3 vert0 vert1 vert2 vert3 ...<p>
where vert* is either a 32 or 16 bit unsigned integer. There are a total of 4*count indices. The stride determines the byte offset to the next index quadruple.<p>
This is declared as a void pointer because it is actually either an physx::PxU16 or a physx::PxU32 pointer. 
</div>
</div><p>
<a class="anchor" name="e4517fa952e6cf3ac848b1b7bc67714e"></a><!-- doxytag: member="nv::cloth::ClothMeshDesc::triangles" ref="e4517fa952e6cf3ac848b1b7bc67714e" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">BoundedData</a> <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e">nv::cloth::ClothMeshDesc::triangles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Pointer to the first triangle. 
<p>
These are triplets of 0 based indices: vert0 vert1 vert2 vert0 vert1 vert2 vert0 vert1 vert2 ...<p>
where vert* is either a 32 or 16 bit unsigned integer. There are a total of 3*count indices. The stride determines the byte offset to the next index triple.<p>
This is declared as a void pointer because it is actually either an physx::PxU16 or a physx::PxU32 pointer. 
</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
