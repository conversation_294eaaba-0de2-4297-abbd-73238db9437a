var node0 = ::blueprint::nodes::list_remove::<PERSON><PERSON><PERSON><PERSON>()

_editor.add_node(node0, -275.06806026743, -299.00774559694)

var node1 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node1, 212.17570014969, -116.70039165052)

var node2 = ::geograph::nodes::is_contain::IsContain()

_editor.add_node(node2, -128.02087207274, -232.07518363062)

var node3 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node3, 531.78813158932, -4.1720427607936)

var node4 = ::editorgraph::nodes::mouse_right_down::MouseRightDown()

_editor.add_node(node4, -462.29494268465, -27.293638531966)

var node5 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node5, -273.76647780299, -227.89531715054)

var node6 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node6, 43.332157680334, -243.92054011359)

var node7 = ::blueprint::nodes::input::Input()

node7.var_name = "geos"
node7.var_type = "array"

_editor.add_node(node7, -789.09090909091, -53.136363636364)

var node8 = ::blueprint::nodes::boolean::Boolean()

node8.value = true

_editor.add_node(node8, -786.9003848459, -181.67890580826)

var node9 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node9, 27.207572477809, -84.805352888315)

var node10 = ::blueprint::nodes::store::Store()

node10.var_name = "geos"

_editor.add_node(node10, -650.63551469283, -62.994735717773)

var node11 = ::blueprint::nodes::load::Load()

node11.var_name = "geos"

_editor.add_node(node11, 34.420778430736, -156.78888933395)

var node12 = ::blueprint::nodes::load::Load()

node12.var_name = "geos"

_editor.add_node(node12, -414.16401214109, -299.82965968859)

var node13 = ::blueprint::nodes::boolean::Boolean()

node13.value = false

_editor.add_node(node13, -113.42532748406, -78.043580717304)

var node14 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node14, -123.50793710702, -307.99536389192)

var node15 = ::blueprint::nodes::boolean::Boolean()

node15.value = true

_editor.add_node(node15, -273.0878916276, -383.80986397202)

var node16 = ::blueprint::nodes::output::Output()

node16.var_name = "success"
node16.var_type = "bool"

_editor.add_node(node16, -650.37769364057, -154.11244214271)

var node17 = ::blueprint::nodes::store::Store()

node17.var_name = "success"

_editor.add_node(node17, -650.51567846881, -213.74683992886)

var node18 = ::blueprint::nodes::load::Load()

node18.var_name = "success"

_editor.add_node(node18, -111.62999040033, -135.30209168236)

var node19 = ::blueprint::nodes::load::Load()

node19.var_name = "success"

_editor.add_node(node19, -273.22584566312, -438.18508429013)

var node20 = ::blueprint::nodes::input::Input()

node20.var_name = "selected"
node20.var_type = "array"

_editor.add_node(node20, -779.47004159745, -311.56562625189)

var node21 = ::blueprint::nodes::store::Store()

node21.var_name = "selected"

_editor.add_node(node21, -646.48737076688, -322.83532588738)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "selected"

_editor.add_node(node22, 40.478489604144, -323.03504185392)

var node23 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node23, 383.42946671448, -123.37223015689)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "success"

_editor.add_node(node24, 212.01833185492, -183.48202276919)

var node25 = ::blueprint::nodes::list_clear::ListClear()

_editor.add_node(node25, 215.49329451782, -243.28377904422)

var node26 = ::blueprint::nodes::input::Input()

node26.var_name = "cam_mat"
node26.var_type = "mat4"

_editor.add_node(node26, -409.45810869706, -238.76066198428)

Blueprint.connect(node20, "var", node21, "var")
Blueprint.connect(node21, "var", node22, "var")
Blueprint.connect(node22, "var", node25, "list")
Blueprint.connect(node8, "v", node17, "var")
Blueprint.connect(node17, "var", node24, "var")
Blueprint.connect(node17, "var", node19, "var")
Blueprint.connect(node17, "var", node18, "var")
Blueprint.connect(node13, "v", node9, "src")
Blueprint.connect(node18, "var", node9, "dst")
Blueprint.connect(node9, "next", node1, "prev")
Blueprint.connect(node11, "var", node1, "in")
Blueprint.connect(node6, "next", node1, "do")
Blueprint.connect(node1, "next", node23, "prev")
Blueprint.connect(node24, "var", node23, "cond")
Blueprint.connect(node25, "next", node23, "true")
Blueprint.connect(node8, "v", node16, "var")
Blueprint.connect(node7, "var", node10, "var")
Blueprint.connect(node10, "var", node12, "var")
Blueprint.connect(node12, "var", node0, "list")
Blueprint.connect(node1, "curr_idx", node0, "index")
Blueprint.connect(node0, "next", node14, "prev")
Blueprint.connect(node15, "v", node14, "src")
Blueprint.connect(node19, "var", node14, "dst")
Blueprint.connect(node10, "var", node11, "var")
Blueprint.connect(node4, "pos", node5, "screen")
Blueprint.connect(node26, "var", node5, "cam_mat")
Blueprint.connect(node1, "out", node2, "geo")
Blueprint.connect(node5, "world", node2, "pos")
Blueprint.connect(node2, "out", node6, "cond")
Blueprint.connect(node14, "next", node6, "true")
Blueprint.connect(node4, "event", node3, "event")
Blueprint.connect(node23, "next", node3, "action")
