<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Solver.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Solver.h</h1><a href="_solver_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include "<a class="code" href="_allocator_8h.html" title="This file together with Callbacks.h define most memory management interfaces for...">NvCloth/Allocator.h</a>"</span>
<a name="l00033"></a>00033 <span class="preprocessor">#include "<a class="code" href="_range_8h.html">NvCloth/Range.h</a>"</span>
<a name="l00034"></a>00034 <span class="preprocessor">#include "PsArray.h"</span>
<a name="l00035"></a>00035 
<a name="l00036"></a>00036 <span class="keyword">namespace </span>nv
<a name="l00037"></a>00037 {
<a name="l00038"></a>00038 <span class="keyword">namespace </span>cloth
<a name="l00039"></a>00039 {
<a name="l00040"></a>00040 
<a name="l00041"></a>00041 <span class="keyword">class </span>Cloth;
<a name="l00042"></a>00042 
<a name="l00043"></a>00043 <span class="comment">// called during inter-collision, user0 and user1 are the user data from each cloth</span>
<a name="l00044"></a>00044 <span class="keyword">typedef</span> bool (*InterCollisionFilter)(<span class="keywordtype">void</span>* user0, <span class="keywordtype">void</span>* user1);
<a name="l00045"></a>00045 
<a name="l00047"></a><a class="code" href="classnv_1_1cloth_1_1_solver.html">00047</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a> : <span class="keyword">public</span> UserAllocated
<a name="l00048"></a>00048 {
<a name="l00049"></a>00049   <span class="keyword">protected</span>:
<a name="l00050"></a><a class="code" href="classnv_1_1cloth_1_1_solver.html#8955bbdbea66b33486f0588ab2f80c46">00050</a>     <a class="code" href="classnv_1_1cloth_1_1_solver.html#8955bbdbea66b33486f0588ab2f80c46">Solver</a>() {}
<a name="l00051"></a>00051     <a class="code" href="classnv_1_1cloth_1_1_solver.html#8955bbdbea66b33486f0588ab2f80c46">Solver</a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a>&amp;);
<a name="l00052"></a>00052     <a class="code" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_solver.html#0bc438b310a4989a96c426c83a1e0beb">operator = </a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html" title="base class for solvers">Solver</a>&amp;);
<a name="l00053"></a>00053 
<a name="l00054"></a>00054   <span class="keyword">public</span>:
<a name="l00055"></a><a class="code" href="classnv_1_1cloth_1_1_solver.html#4830e23f5fbaa9dfa7c8c0ce32fa85bd">00055</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#4830e23f5fbaa9dfa7c8c0ce32fa85bd">~Solver</a>() {}
<a name="l00056"></a>00056 
<a name="l00058"></a>00058     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#63e732712c5a43c44e6018cca6c1fb82" title="Adds cloth object.">addCloth</a>(<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* cloth) = 0;
<a name="l00059"></a>00059 
<a name="l00061"></a>00061     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#ea0f778230b2a0f211bdb5d36d3b54f3" title="Adds an array of cloth objects.">addCloths</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;Cloth*&gt;</a> cloths) = 0;
<a name="l00062"></a>00062 
<a name="l00064"></a>00064     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#1ed765a15ab2dabbb5186d14bc5f70b1" title="Removes cloth object.">removeCloth</a>(<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* cloth) = 0;
<a name="l00065"></a>00065 
<a name="l00067"></a>00067     <span class="keyword">virtual</span> <span class="keywordtype">int</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#de377e651711ebbb9e70f928cbb682e2" title="Returns the numer of cloths added to the solver.">getNumCloths</a>() <span class="keyword">const</span> = 0;
<a name="l00068"></a>00068 
<a name="l00070"></a>00070     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> * <span class="keyword">const</span> * <a class="code" href="classnv_1_1cloth_1_1_solver.html#a3e121ffbccc07180e08a2387eb4f6ac" title="Returns the pointer to the first cloth added to the solver.">getClothList</a>() <span class="keyword">const</span> = 0;
<a name="l00071"></a>00071 
<a name="l00072"></a>00072     <span class="comment">// functions executing the simulation work.</span>
<a name="l00078"></a>00078 <span class="comment"></span>    <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#d2efbe447bf07138c615973c349ab839" title="Begins a simulation frame.">beginSimulation</a>(<span class="keywordtype">float</span> dt) = 0;
<a name="l00079"></a>00079 
<a name="l00085"></a>00085     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#62d209d861c8f5aa0523536d851de093" title="Do the computationally heavy part of the simulation.">simulateChunk</a>(<span class="keywordtype">int</span> idx) = 0;
<a name="l00086"></a>00086 
<a name="l00090"></a>00090     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#19d777a3882814910f8a024d92072d48" title="Finishes up the simulation.">endSimulation</a>() = 0;
<a name="l00091"></a>00091 
<a name="l00094"></a>00094     <span class="keyword">virtual</span> <span class="keywordtype">int</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#54e393ba3b9fd5305385e2f57d3ca165" title="Returns the number of chunks that need to be simulated this frame.">getSimulationChunkCount</a>() <span class="keyword">const</span> = 0;
<a name="l00095"></a>00095 
<a name="l00096"></a>00096     <span class="comment">// inter-collision parameters</span>
<a name="l00097"></a>00097     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#5043adf6727bf66b966de6393e7d67d9">setInterCollisionDistance</a>(<span class="keywordtype">float</span> distance) = 0;
<a name="l00098"></a>00098     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#6a72529d713f46dbd17a5b541aaec6df">getInterCollisionDistance</a>() <span class="keyword">const</span> = 0;
<a name="l00099"></a>00099     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#4f15accbaeff04edbebd31bf7dd9be3e">setInterCollisionStiffness</a>(<span class="keywordtype">float</span> stiffness) = 0;
<a name="l00100"></a>00100     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#09e4be9b50229213a837d00a3f2f6a3f">getInterCollisionStiffness</a>() <span class="keyword">const</span> = 0;
<a name="l00101"></a>00101     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#3046ea1153c1f9decfc161155cc9810b">setInterCollisionNbIterations</a>(uint32_t nbIterations) = 0;
<a name="l00102"></a>00102     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_solver.html#124dc836903890185934c6eaedec2079">getInterCollisionNbIterations</a>() <span class="keyword">const</span> = 0;
<a name="l00103"></a>00103     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#31b3d4d36f2025f10cb04a32e28fada4">setInterCollisionFilter</a>(InterCollisionFilter filter) = 0;
<a name="l00104"></a>00104 
<a name="l00106"></a>00106     <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_solver.html#12a60f43b537d78499e30508bd9a6d3c" title="Returns true if an unrecoverable error has occurred.">hasError</a>() <span class="keyword">const</span> = 0;
<a name="l00107"></a>00107 };
<a name="l00108"></a>00108 
<a name="l00109"></a>00109 } <span class="comment">// namespace cloth</span>
<a name="l00110"></a>00110 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
