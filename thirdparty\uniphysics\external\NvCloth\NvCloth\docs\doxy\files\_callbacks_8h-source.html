<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Callbacks.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Callbacks.h</h1><a href="_callbacks_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00036"></a>00036 <span class="preprocessor">#pragma once</span>
<a name="l00037"></a>00037 <span class="preprocessor"></span><span class="preprocessor">#include &lt;foundation/PxPreprocessor.h&gt;</span>
<a name="l00038"></a>00038 <span class="preprocessor">#include &lt;foundation/PxProfiler.h&gt;</span>
<a name="l00039"></a>00039 <span class="preprocessor">#include &lt;foundation/PxAssert.h&gt;</span>
<a name="l00040"></a>00040 <span class="preprocessor">#ifndef NV_CLOTH_IMPORT</span>
<a name="l00041"></a><a class="code" href="_callbacks_8h.html#bd597bda23283ca6fe84282f6e2671dc">00041</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_IMPORT PX_DLL_IMPORT</span>
<a name="l00042"></a>00042 <span class="preprocessor"></span><span class="preprocessor">#endif</span>
<a name="l00043"></a>00043 <span class="preprocessor"></span>
<a name="l00044"></a><a class="code" href="_callbacks_8h.html#d43b3e4b2ee5d1c328c332ee9d1666e2">00044</a> <span class="preprocessor">#define NV_CLOTH_DLL_ID 0x2</span>
<a name="l00045"></a>00045 <span class="preprocessor"></span>
<a name="l00046"></a><a class="code" href="_callbacks_8h.html#71c40d2ed1c52507e10baa313de4d292">00046</a> <span class="preprocessor">#define NV_CLOTH_LINKAGE PX_C_EXPORT NV_CLOTH_IMPORT</span>
<a name="l00047"></a><a class="code" href="_callbacks_8h.html#0a1f306c4d84c8362b056e8fd313629a">00047</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_CALL_CONV PX_CALL_CONV</span>
<a name="l00048"></a><a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">00048</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_API(ret_type) NV_CLOTH_LINKAGE ret_type NV_CLOTH_CALL_CONV</span>
<a name="l00049"></a>00049 <span class="preprocessor"></span>
<a name="l00050"></a>00050 <span class="keyword">namespace </span>physx
<a name="l00051"></a>00051 {
<a name="l00052"></a>00052     <span class="keyword">class </span>PxAllocatorCallback;
<a name="l00053"></a>00053     <span class="keyword">class </span>PxErrorCallback;
<a name="l00054"></a>00054     <span class="keyword">class </span>PxProfilerCallback;
<a name="l00055"></a>00055     <span class="keyword">class </span>PxAssertHandler;
<a name="l00056"></a>00056 }
<a name="l00057"></a>00057 
<a name="l00059"></a>00059 <span class="keyword">namespace </span>nv
<a name="l00060"></a>00060 {
<a name="l00062"></a>00062 <span class="keyword">namespace </span>cloth
<a name="l00063"></a>00063 {
<a name="l00072"></a>00072 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<span class="keywordtype">void</span>)
<a name="l00073"></a>00073     InitializeNvCloth(physx::PxAllocatorCallback* allocatorCallback, physx::PxErrorCallback* errorCallback, 
<a name="l00074"></a>00074                         physx::PxAssertHandler* assertHandler, physx::PxProfilerCallback* profilerCallback,
<a name="l00075"></a>00075                         <span class="keywordtype">int</span> autoDllIDCheck = <a class="code" href="_callbacks_8h.html#d43b3e4b2ee5d1c328c332ee9d1666e2">NV_CLOTH_DLL_ID</a>);
<a name="l00076"></a>00076 }
<a name="l00077"></a>00077 }
<a name="l00078"></a>00078 
<a name="l00079"></a>00079 <span class="comment">//Allocator</span>
<a name="l00080"></a>00080 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(physx::PxAllocatorCallback*) <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>(); <span class="comment">//Only use internally</span>
<a name="l00081"></a>00081 
<a name="l00082"></a>00082 namespace nv
<a name="l00083"></a>00083 {
<a name="l00084"></a>00084 <span class="keyword">namespace </span>cloth
<a name="l00085"></a>00085 {
<a name="l00086"></a>00086 <span class="comment">//Logging</span>
<a name="l00087"></a>00087 <span class="keywordtype">void</span> LogErrorFn  (<span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> lineNumber, <span class="keyword">const</span> <span class="keywordtype">char</span>* msg, ...);
<a name="l00088"></a>00088 <span class="keywordtype">void</span> LogInvalidParameterFn  (<span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> lineNumber, <span class="keyword">const</span> <span class="keywordtype">char</span>* msg, ...);
<a name="l00089"></a>00089 <span class="keywordtype">void</span> LogWarningFn(<span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> lineNumber, <span class="keyword">const</span> <span class="keywordtype">char</span>* msg, ...);
<a name="l00090"></a>00090 <span class="keywordtype">void</span> LogInfoFn   (<span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> lineNumber, <span class="keyword">const</span> <span class="keywordtype">char</span>* msg, ...);
<a name="l00092"></a><a class="code" href="_callbacks_8h.html#0369e7cdaf37f1ecd6dd5e00b2ebf7da">00092</a> <span class="preprocessor">#define NV_CLOTH_LOG_ERROR(...) nv::cloth::LogErrorFn(__FILE__,__LINE__,__VA_ARGS__)</span>
<a name="l00093"></a><a class="code" href="_callbacks_8h.html#e12b7837f3ff9076845affea652c9220">00093</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_LOG_INVALID_PARAMETER(...) nv::cloth::LogInvalidParameterFn(__FILE__,__LINE__,__VA_ARGS__)</span>
<a name="l00094"></a><a class="code" href="_callbacks_8h.html#d7fe263c5c514ce5bc018bec64e7fba5">00094</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_LOG_WARNING(...) nv::cloth::LogWarningFn(__FILE__,__LINE__,__VA_ARGS__)</span>
<a name="l00095"></a><a class="code" href="_callbacks_8h.html#db5608a2350e209f80ac3752e7ec3a42">00095</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_LOG_INFO(...) nv::cloth::LogInfoFn(__FILE__,__LINE__,__VA_ARGS__)</span>
<a name="l00096"></a>00096 <span class="preprocessor"></span>
<a name="l00097"></a>00097 <span class="comment">//ASSERT</span>
<a name="l00098"></a>00098 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(physx::PxAssertHandler*) GetNvClothAssertHandler(); <span class="comment">//This function needs to be exposed to properly inline asserts outside this dll</span>
<a name="l00099"></a>00099 <span class="preprocessor">#if !PX_ENABLE_ASSERTS</span>
<a name="l00100"></a>00100 <span class="preprocessor"></span><span class="preprocessor">#if PX_VC</span>
<a name="l00101"></a>00101 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT(exp) __noop</span>
<a name="l00102"></a>00102 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT_WITH_MESSAGE(message, exp) __noop</span>
<a name="l00103"></a>00103 <span class="preprocessor"></span><span class="preprocessor">#else</span>
<a name="l00104"></a><a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">00104</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT(exp) ((void)0)</span>
<a name="l00105"></a><a class="code" href="_callbacks_8h.html#7580256d644389afb1a6fbf123cd9747">00105</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT_WITH_MESSAGE(message, exp) ((void)0)</span>
<a name="l00106"></a>00106 <span class="preprocessor"></span><span class="preprocessor">#endif</span>
<a name="l00107"></a>00107 <span class="preprocessor"></span><span class="preprocessor">#else</span>
<a name="l00108"></a>00108 <span class="preprocessor"></span><span class="preprocessor">#if PX_VC</span>
<a name="l00109"></a>00109 <span class="preprocessor"></span><span class="preprocessor">#define PX_CODE_ANALYSIS_ASSUME(exp)                                                                                   \</span>
<a name="l00110"></a>00110 <span class="preprocessor">    __analysis_assume(!!(exp)) // This macro will be used to get rid of analysis warning messages if a PX_ASSERT is used</span>
<a name="l00111"></a>00111 <span class="preprocessor"></span>                                                                 <span class="comment">// to "guard" illegal mem access, for example.</span>
<a name="l00112"></a>00112 <span class="preprocessor">#else</span>
<a name="l00113"></a>00113 <span class="preprocessor"></span><span class="preprocessor">#define PX_CODE_ANALYSIS_ASSUME(exp)</span>
<a name="l00114"></a>00114 <span class="preprocessor"></span><span class="preprocessor">#endif</span>
<a name="l00115"></a>00115 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT(exp)                                                                                           \</span>
<a name="l00116"></a>00116 <span class="preprocessor">    {                                                                                                                  \</span>
<a name="l00117"></a>00117 <span class="preprocessor">        static bool _ignore = false;                                                                                   \</span>
<a name="l00118"></a>00118 <span class="preprocessor">        ((void)((!!(exp)) || (!_ignore &amp;&amp; ((*nv::cloth::GetNvClothAssertHandler())(#exp, __FILE__, __LINE__, _ignore), false))));    \</span>
<a name="l00119"></a>00119 <span class="preprocessor">        PX_CODE_ANALYSIS_ASSUME(exp);                                                                                  \</span>
<a name="l00120"></a>00120 <span class="preprocessor">    }</span>
<a name="l00121"></a>00121 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ASSERT_WITH_MESSAGE(message, exp)                                                                             \</span>
<a name="l00122"></a>00122 <span class="preprocessor">    {                                                                                                                    \</span>
<a name="l00123"></a>00123 <span class="preprocessor">        static bool _ignore = false;                                                                                     \</span>
<a name="l00124"></a>00124 <span class="preprocessor">        ((void)((!!(exp)) || (!_ignore &amp;&amp; (physx::PxGetAssertHandler()(message, __FILE__, __LINE__, _ignore), false)))); \</span>
<a name="l00125"></a>00125 <span class="preprocessor">        PX_CODE_ANALYSIS_ASSUME(exp);                                                                                    \</span>
<a name="l00126"></a>00126 <span class="preprocessor">    }</span>
<a name="l00127"></a>00127 <span class="preprocessor"></span><span class="preprocessor">#endif</span>
<a name="l00128"></a>00128 <span class="preprocessor"></span>
<a name="l00129"></a>00129 <span class="comment">//Profiler</span>
<a name="l00130"></a>00130 physx::PxProfilerCallback* GetNvClothProfiler(); <span class="comment">//Only use internally</span>
<a name="l00131"></a>00131 <span class="preprocessor">#if PX_DEBUG || PX_CHECKED || PX_PROFILE || 1</span>
<a name="l00132"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">00132</a> <span class="preprocessor"></span><span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">NvClothProfileScoped</a>
<a name="l00133"></a>00133 {
<a name="l00134"></a>00134   <span class="keyword">public</span>:
<a name="l00135"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#eb4cd36d11aa03f05d05a6f18f8b41cb">00135</a>     PX_FORCE_INLINE <a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">NvClothProfileScoped</a>(<span class="keyword">const</span> <span class="keywordtype">char</span>* eventName, <span class="keywordtype">bool</span> detached, uint64_t contextId,
<a name="l00136"></a>00136                                     <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> lineno, physx::PxProfilerCallback* callback)
<a name="l00137"></a>00137     : mCallback(callback)
<a name="l00138"></a>00138     {
<a name="l00139"></a>00139         PX_UNUSED(fileName);
<a name="l00140"></a>00140         PX_UNUSED(lineno);
<a name="l00141"></a>00141         mProfilerData = NULL; <span class="comment">//nullptr doesn't work here on mac</span>
<a name="l00142"></a>00142         <span class="keywordflow">if</span> (mCallback)
<a name="l00143"></a>00143         {
<a name="l00144"></a>00144             mEventName = eventName;
<a name="l00145"></a>00145             mDetached = detached;
<a name="l00146"></a>00146             mContextId = contextId;
<a name="l00147"></a>00147             mProfilerData = mCallback-&gt;zoneStart(mEventName, mDetached, mContextId);
<a name="l00148"></a>00148         }
<a name="l00149"></a>00149     }
<a name="l00150"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#803ffc5af708346f086454ca7c6948d9">00150</a>     ~<a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">NvClothProfileScoped</a>(<span class="keywordtype">void</span>)
<a name="l00151"></a>00151     {
<a name="l00152"></a>00152         <span class="keywordflow">if</span> (mCallback)
<a name="l00153"></a>00153         {
<a name="l00154"></a>00154             mCallback-&gt;zoneEnd(mProfilerData, mEventName, mDetached, mContextId);
<a name="l00155"></a>00155         }
<a name="l00156"></a>00156     }
<a name="l00157"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">00157</a>     physx::PxProfilerCallback* mCallback;
<a name="l00158"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">00158</a>     <span class="keyword">const</span> <span class="keywordtype">char</span>* mEventName;
<a name="l00159"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">00159</a>     <span class="keywordtype">bool</span> mDetached;
<a name="l00160"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">00160</a>     uint64_t mContextId;
<a name="l00161"></a><a class="code" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">00161</a>     <span class="keywordtype">void</span>* mProfilerData;
<a name="l00162"></a>00162 };
<a name="l00163"></a>00163 
<a name="l00164"></a><a class="code" href="_callbacks_8h.html#07a777d717c8c0eb9d8ae6d1b5db94ee">00164</a> <span class="preprocessor">#define NV_CLOTH_PROFILE_ZONE(x, y)                                                                                          \</span>
<a name="l00165"></a>00165 <span class="preprocessor">    nv::cloth::NvClothProfileScoped PX_CONCAT(_scoped, __LINE__)(x, false, y, __FILE__, __LINE__, nv::cloth::GetNvClothProfiler())</span>
<a name="l00166"></a><a class="code" href="_callbacks_8h.html#d31f06d741b7e7340058e66b64e8d1da">00166</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_PROFILE_START_CROSSTHREAD(x, y)                                                                             \</span>
<a name="l00167"></a>00167 <span class="preprocessor">    (GetNvClothProfiler()!=nullptr?                                                                                        \</span>
<a name="l00168"></a>00168 <span class="preprocessor">    GetNvClothProfiler()-&gt;zoneStart(x, true, y):nullptr)</span>
<a name="l00169"></a><a class="code" href="_callbacks_8h.html#5c4305c0f359cd51e90391ce3847d4de">00169</a> <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_PROFILE_STOP_CROSSTHREAD(profilerData, x, y)                                                                              \</span>
<a name="l00170"></a>00170 <span class="preprocessor">    if (GetNvClothProfiler())                                                                                           \</span>
<a name="l00171"></a>00171 <span class="preprocessor">    GetNvClothProfiler()-&gt;zoneEnd(profilerData, x, true, y)</span>
<a name="l00172"></a>00172 <span class="preprocessor"></span><span class="preprocessor">#else</span>
<a name="l00173"></a>00173 <span class="preprocessor"></span>
<a name="l00174"></a>00174 <span class="preprocessor">#define NV_CLOTH_PROFILE_ZONE(x, y)</span>
<a name="l00175"></a>00175 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_PROFILE_START_CROSSTHREAD(x, y) nullptr</span>
<a name="l00176"></a>00176 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_PROFILE_STOP_CROSSTHREAD(profilerData, x, y)</span>
<a name="l00177"></a>00177 <span class="preprocessor"></span>
<a name="l00178"></a>00178 <span class="preprocessor">#endif</span>
<a name="l00179"></a>00179 <span class="preprocessor"></span>
<a name="l00180"></a>00180 }
<a name="l00181"></a>00181 }
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
