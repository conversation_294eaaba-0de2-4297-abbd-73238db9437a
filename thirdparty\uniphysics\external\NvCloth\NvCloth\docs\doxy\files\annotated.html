<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Class List</h1>Here are the classes, structs, unions and interfaces with brief descriptions:<table>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_bounded_data.html">nv::cloth::BoundedData</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">nv::cloth::Cloth</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a></td><td class="indexvalue">Use NvClothCreateFabricCooker() to create an implemented instance </td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td class="indexvalue">References all the data required to create a fabric </td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">nv::cloth::ClothFabricPhase</a></td><td class="indexvalue">References a set of constraints that can be solved in parallel </td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html">nv::cloth::ClothFabricPhaseType</a></td><td class="indexvalue">Describe type of phase in cloth fabric </td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td class="indexvalue">Descriptor class for a cloth mesh </td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html">nv::cloth::ClothMeshQuadifier</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td class="indexvalue">Callback interface to manage the DirectX context/device used for compute </td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a></td><td class="indexvalue">Abstract factory to create context-specific simulation components such as cloth, solver, collision, etc </td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html">nv::cloth::GpuParticles</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">nv::cloth::MappedRange&lt; T &gt;</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_mesh_flag.html">nv::cloth::MeshFlag</a></td><td class="indexvalue">Enum with flag values to be used in <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a> </td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_phase_config.html">nv::cloth::PhaseConfig</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_range.html">nv::cloth::Range&lt; T &gt;</a></td><td class="indexvalue"></td></tr>
  <tr><td class="indexkey"><a class="el" href="classnv_1_1cloth_1_1_solver.html">nv::cloth::Solver</a></td><td class="indexvalue">Base class for solvers </td></tr>
  <tr><td class="indexkey"><a class="el" href="structnv_1_1cloth_1_1_strided_data.html">nv::cloth::StridedData</a></td><td class="indexvalue"></td></tr>
</table>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
