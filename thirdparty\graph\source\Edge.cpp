#include "graph/Edge.h"

namespace graph
{

bool Edge::HasComponent(const std::string& key) const
{
	return m_components.find(key) != m_components.end();
}

void Edge::SetComponent(const std::string& key, const std::string& value)
{
	m_components[key] = value;
}

std::string Edge::GetComponent(const std::string& key) const
{
	auto it = m_components.find(key);
	if (it != m_components.end()) {
		return it->second;
	}
	return "";
}

}
