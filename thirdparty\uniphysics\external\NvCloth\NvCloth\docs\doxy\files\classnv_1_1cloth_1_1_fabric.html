<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::Fabric Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Fabric Class Reference</h1><!-- doxytag: class="nv::cloth::Fabric" --><code>#include &lt;<a class="el" href="_fabric_8h-source.html">Fabric.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_fabric-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#dbabafe4f0954eb5cea92463de89dfa0">decRefCount</a> ()</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if the object is destroyed.  <a href="#dbabafe4f0954eb5cea92463de89dfa0"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#e6ab4bb76335c9af1a67435eb2520d62">getFactory</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the <a class="el" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a> used to create this <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>.  <a href="#e6ab4bb76335c9af1a67435eb2520d62"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#057b35a8f4b7cce31a0be2eb0704e52d">getNumIndices</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of indices stored.  <a href="#057b35a8f4b7cce31a0be2eb0704e52d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8dd6c3990522e16832311a2b04b17619">getNumParticles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of particles.  <a href="#8dd6c3990522e16832311a2b04b17619"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#4d9348de98c1c00498709dc591fa27ba">getNumPhases</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of constraint solve phases stored.  <a href="#4d9348de98c1c00498709dc591fa27ba"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#52c968ff1b808ab00d994db25bc01d83">getNumRestvalues</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of rest lengths stored.  <a href="#52c968ff1b808ab00d994db25bc01d83"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#d28fcf11b0c9ebb20325cafb5dbcde4d">getNumSets</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of sets stored.  <a href="#d28fcf11b0c9ebb20325cafb5dbcde4d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#24d3ef1c25d42d981a12f5b7a96114e4">getNumStiffnessValues</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of constraint stiffness values stored.  <a href="#24d3ef1c25d42d981a12f5b7a96114e4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#aa6b9b09786b98e3be8cc9f362c1f09d">getNumTethers</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of Tethers stored.  <a href="#aa6b9b09786b98e3be8cc9f362c1f09d"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8d15c9c15000eeaad9b855cb3ca1d8c8">getNumTriangles</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of triangles that make up the cloth mesh.  <a href="#8d15c9c15000eeaad9b855cb3ca1d8c8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#7566de18b6640949fcce3839238fb9ce">incRefCount</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8343cbc315361fc0ebb1322009076c86">scaleRestvalues</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Scales all constraint rest lengths.  <a href="#8343cbc315361fc0ebb1322009076c86"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#b884bf893050c00ec8bacb25a5dd76a0">scaleTetherLengths</a> (float)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Scales all tether lengths.  <a href="#b884bf893050c00ec8bacb25a5dd76a0"></a><br></td></tr>
<tr><td colspan="2"><br><h2>Protected Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#bb5cffce0412bc67bafbb1c47b56886e">Fabric</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#2f603146b3133b7867cd9d008ab68ff3">Fabric</a> (const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#3d850841d4168a5827731f3fa4cc07c6">operator=</a> (const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8d3748f793f73d1cc5547ee99d052038">~Fabric</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Protected Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">int32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a></td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="2f603146b3133b7867cd9d008ab68ff3"></a><!-- doxytag: member="nv::cloth::Fabric::Fabric" ref="2f603146b3133b7867cd9d008ab68ff3" args="(const Fabric &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Fabric::Fabric           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="bb5cffce0412bc67bafbb1c47b56886e"></a><!-- doxytag: member="nv::cloth::Fabric::Fabric" ref="bb5cffce0412bc67bafbb1c47b56886e" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Fabric::Fabric           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="8d3748f793f73d1cc5547ee99d052038"></a><!-- doxytag: member="nv::cloth::Fabric::~Fabric" ref="8d3748f793f73d1cc5547ee99d052038" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::Fabric::~Fabric           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="dbabafe4f0954eb5cea92463de89dfa0"></a><!-- doxytag: member="nv::cloth::Fabric::decRefCount" ref="dbabafe4f0954eb5cea92463de89dfa0" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool nv::cloth::Fabric::decRefCount           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if the object is destroyed. 
<p>

</div>
</div><p>
<a class="anchor" name="e6ab4bb76335c9af1a67435eb2520d62"></a><!-- doxytag: member="nv::cloth::Fabric::getFactory" ref="e6ab4bb76335c9af1a67435eb2520d62" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a>&amp; nv::cloth::Fabric::getFactory           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the <a class="el" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a> used to create this <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>. 
<p>

</div>
</div><p>
<a class="anchor" name="057b35a8f4b7cce31a0be2eb0704e52d"></a><!-- doxytag: member="nv::cloth::Fabric::getNumIndices" ref="057b35a8f4b7cce31a0be2eb0704e52d" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumIndices           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of indices stored. 
<p>
Each constraint has a pair of indices that indicate which particles it connects. 
</div>
</div><p>
<a class="anchor" name="8dd6c3990522e16832311a2b04b17619"></a><!-- doxytag: member="nv::cloth::Fabric::getNumParticles" ref="8dd6c3990522e16832311a2b04b17619" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumParticles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of particles. 
<p>

</div>
</div><p>
<a class="anchor" name="4d9348de98c1c00498709dc591fa27ba"></a><!-- doxytag: member="nv::cloth::Fabric::getNumPhases" ref="4d9348de98c1c00498709dc591fa27ba" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumPhases           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of constraint solve phases stored. 
<p>
Phases are groups of constraints that make up the general structure of the fabric. <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> instances can have different configuration settings per phase (see <a class="el" href="classnv_1_1cloth_1_1_cloth.html#ea95e56cb73720970f79903dcffc8360">Cloth::setPhaseConfig()</a>). Phases are usually split by type (horizontal, vertical, bending, shearing), depending on the cooker used. 
</div>
</div><p>
<a class="anchor" name="52c968ff1b808ab00d994db25bc01d83"></a><!-- doxytag: member="nv::cloth::Fabric::getNumRestvalues" ref="52c968ff1b808ab00d994db25bc01d83" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumRestvalues           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of rest lengths stored. 
<p>
Each constraint uses the rest value to determine if the two connected particles need to be pulled together or pushed apart. 
</div>
</div><p>
<a class="anchor" name="d28fcf11b0c9ebb20325cafb5dbcde4d"></a><!-- doxytag: member="nv::cloth::Fabric::getNumSets" ref="d28fcf11b0c9ebb20325cafb5dbcde4d" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumSets           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of sets stored. 
<p>
Sets connect a phase to a range of indices. 
</div>
</div><p>
<a class="anchor" name="24d3ef1c25d42d981a12f5b7a96114e4"></a><!-- doxytag: member="nv::cloth::Fabric::getNumStiffnessValues" ref="24d3ef1c25d42d981a12f5b7a96114e4" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumStiffnessValues           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of constraint stiffness values stored. 
<p>
It is optional for a <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> to have per constraint stiffness values provided. This function will return 0 if no values are stored. Stiffness per constraint values stored here can be used if more fine grain control is required (as opposed to the values stored in the cloth's phase configuration). The <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> 's phase configuration stiffness values will be ignored if stiffness per constraint values are used. 
</div>
</div><p>
<a class="anchor" name="aa6b9b09786b98e3be8cc9f362c1f09d"></a><!-- doxytag: member="nv::cloth::Fabric::getNumTethers" ref="aa6b9b09786b98e3be8cc9f362c1f09d" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumTethers           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of Tethers stored. 
<p>

</div>
</div><p>
<a class="anchor" name="8d15c9c15000eeaad9b855cb3ca1d8c8"></a><!-- doxytag: member="nv::cloth::Fabric::getNumTriangles" ref="8d15c9c15000eeaad9b855cb3ca1d8c8" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Fabric::getNumTriangles           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of triangles that make up the cloth mesh. 
<p>

</div>
</div><p>
<a class="anchor" name="7566de18b6640949fcce3839238fb9ce"></a><!-- doxytag: member="nv::cloth::Fabric::incRefCount" ref="7566de18b6640949fcce3839238fb9ce" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void nv::cloth::Fabric::incRefCount           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="3d850841d4168a5827731f3fa4cc07c6"></a><!-- doxytag: member="nv::cloth::Fabric::operator=" ref="3d850841d4168a5827731f3fa4cc07c6" args="(const Fabric &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; nv::cloth::Fabric::operator=           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="8343cbc315361fc0ebb1322009076c86"></a><!-- doxytag: member="nv::cloth::Fabric::scaleRestvalues" ref="8343cbc315361fc0ebb1322009076c86" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Fabric::scaleRestvalues           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Scales all constraint rest lengths. 
<p>

</div>
</div><p>
<a class="anchor" name="b884bf893050c00ec8bacb25a5dd76a0"></a><!-- doxytag: member="nv::cloth::Fabric::scaleTetherLengths" ref="b884bf893050c00ec8bacb25a5dd76a0" args="(float)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Fabric::scaleTetherLengths           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Scales all tether lengths. 
<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="21c01654b6a8e398f5b39e24678f706d"></a><!-- doxytag: member="nv::cloth::Fabric::mRefCount" ref="21c01654b6a8e398f5b39e24678f706d" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">int32_t <a class="el" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">nv::cloth::Fabric::mRefCount</a><code> [protected]</code>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_fabric_8h-source.html">Fabric.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
