<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Range.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Range.h</h1><a href="_range_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include "<a class="code" href="_callbacks_8h.html" title="All functions to initialize and use user provided callbacks are declared in this...">NvCloth/Callbacks.h</a>"</span>
<a name="l00033"></a>00033 
<a name="l00034"></a>00034 <span class="keyword">namespace </span>nv
<a name="l00035"></a>00035 {
<a name="l00036"></a>00036 <span class="keyword">namespace </span>cloth
<a name="l00037"></a>00037 {
<a name="l00038"></a>00038 
<a name="l00039"></a>00039 <span class="keyword">template</span> &lt;<span class="keyword">class</span> T&gt;
<a name="l00040"></a><a class="code" href="structnv_1_1cloth_1_1_range.html">00040</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>
<a name="l00041"></a>00041 {
<a name="l00044"></a>00044     <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range</a>();
<a name="l00045"></a>00045 
<a name="l00052"></a>00052     <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range</a>(T* <a class="code" href="structnv_1_1cloth_1_1_range.html#45a26e7bbcaffef1a5c22262a86ad145">begin</a>, T* <a class="code" href="structnv_1_1cloth_1_1_range.html#639b15c01cb026a8c6f9689f20ed84c1">end</a>);
<a name="l00053"></a>00053 
<a name="l00054"></a>00054     <span class="keyword">template</span> &lt;<span class="keyword">typename</span> S&gt;
<a name="l00055"></a>00055     <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range</a>(<span class="keyword">const</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;S&gt;</a>&amp; other);
<a name="l00056"></a>00056 
<a name="l00057"></a>00057     uint32_t <a class="code" href="structnv_1_1cloth_1_1_range.html#0ef526ff1b8eef5c117ad0e892ab5d24">size</a>() <span class="keyword">const</span>;
<a name="l00058"></a>00058     <span class="keywordtype">bool</span> <a class="code" href="structnv_1_1cloth_1_1_range.html#f44707a5b73331a43e4f03ec08cb7601">empty</a>() <span class="keyword">const</span>;
<a name="l00059"></a>00059 
<a name="l00060"></a>00060     <span class="keywordtype">void</span> <a class="code" href="structnv_1_1cloth_1_1_range.html#09a3da916a813cc0760cfcf93bb5c907">popFront</a>();
<a name="l00061"></a>00061     <span class="keywordtype">void</span> <a class="code" href="structnv_1_1cloth_1_1_range.html#a5b319fd912310273acea0f178560c65">popBack</a>();
<a name="l00062"></a>00062 
<a name="l00063"></a>00063     T* <a class="code" href="structnv_1_1cloth_1_1_range.html#45a26e7bbcaffef1a5c22262a86ad145">begin</a>() <span class="keyword">const</span>;
<a name="l00064"></a>00064     T* <a class="code" href="structnv_1_1cloth_1_1_range.html#639b15c01cb026a8c6f9689f20ed84c1">end</a>() <span class="keyword">const</span>;
<a name="l00065"></a>00065 
<a name="l00066"></a>00066     T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html#5d7d8a09e16cb3e3a0137563571588dc">front</a>() <span class="keyword">const</span>;
<a name="l00067"></a>00067     T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html#6eea0965791c328ef945c3c9ec16637b">back</a>() <span class="keyword">const</span>;
<a name="l00068"></a>00068 
<a name="l00069"></a>00069     T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html#194cc89be14aa7944b95fd8bf0a948fd">operator[]</a>(uint32_t i) <span class="keyword">const</span>;
<a name="l00070"></a>00070 
<a name="l00071"></a>00071   <span class="keyword">private</span>:
<a name="l00072"></a>00072     T* mBegin;
<a name="l00073"></a>00073     T* mEnd; <span class="comment">// past last element (like std::vector::end())</span>
<a name="l00074"></a>00074 };
<a name="l00075"></a>00075 
<a name="l00076"></a>00076 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00077"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7">00077</a> <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range&lt;T&gt;::Range</a>()
<a name="l00078"></a>00078 : mBegin(0), mEnd(0)
<a name="l00079"></a>00079 {
<a name="l00080"></a>00080 }
<a name="l00081"></a>00081 
<a name="l00082"></a>00082 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00083"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#cc2a8d8c4dac26809deefca1ef8f68e8">00083</a> <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range&lt;T&gt;::Range</a>(T* begin, T* end)
<a name="l00084"></a>00084 : mBegin(begin), mEnd(end)
<a name="l00085"></a>00085 {
<a name="l00086"></a>00086 }
<a name="l00087"></a>00087 
<a name="l00088"></a>00088 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00089"></a>00089 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> S&gt;
<a name="l00090"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#4c5a521f9b7114617506396667d75a4e">00090</a> <a class="code" href="structnv_1_1cloth_1_1_range.html#9a25cc8511d0d9d8b4147ca7592eebc7" title="Construct an empty range.">Range&lt;T&gt;::Range</a>(<span class="keyword">const</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;S&gt;</a>&amp; other)
<a name="l00091"></a>00091 : mBegin(other.begin()), mEnd(other.end())
<a name="l00092"></a>00092 {
<a name="l00093"></a>00093 }
<a name="l00094"></a>00094 
<a name="l00095"></a>00095 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00096"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#0ef526ff1b8eef5c117ad0e892ab5d24">00096</a> uint32_t <a class="code" href="structnv_1_1cloth_1_1_range.html#0ef526ff1b8eef5c117ad0e892ab5d24">Range&lt;T&gt;::size</a>()<span class="keyword"> const</span>
<a name="l00097"></a>00097 <span class="keyword"></span>{
<a name="l00098"></a>00098     <span class="keywordflow">return</span> uint32_t(mEnd - mBegin);
<a name="l00099"></a>00099 }
<a name="l00100"></a>00100 
<a name="l00101"></a>00101 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00102"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#f44707a5b73331a43e4f03ec08cb7601">00102</a> <span class="keywordtype">bool</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;T&gt;::empty</a>()<span class="keyword"> const</span>
<a name="l00103"></a>00103 <span class="keyword"></span>{
<a name="l00104"></a>00104     <span class="keywordflow">return</span> mBegin &gt;= mEnd;
<a name="l00105"></a>00105 }
<a name="l00106"></a>00106 
<a name="l00107"></a>00107 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00108"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#09a3da916a813cc0760cfcf93bb5c907">00108</a> <span class="keywordtype">void</span> <a class="code" href="structnv_1_1cloth_1_1_range.html#09a3da916a813cc0760cfcf93bb5c907">Range&lt;T&gt;::popFront</a>()
<a name="l00109"></a>00109 {
<a name="l00110"></a>00110     <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(mBegin &lt; mEnd);
<a name="l00111"></a>00111     ++mBegin;
<a name="l00112"></a>00112 }
<a name="l00113"></a>00113 
<a name="l00114"></a>00114 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00115"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#a5b319fd912310273acea0f178560c65">00115</a> <span class="keywordtype">void</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;T&gt;::popBack</a>()
<a name="l00116"></a>00116 {
<a name="l00117"></a>00117     <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(mBegin &lt; mEnd);
<a name="l00118"></a>00118     --mEnd;
<a name="l00119"></a>00119 }
<a name="l00120"></a>00120 
<a name="l00121"></a>00121 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00122"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#45a26e7bbcaffef1a5c22262a86ad145">00122</a> T* <a class="code" href="structnv_1_1cloth_1_1_range.html#45a26e7bbcaffef1a5c22262a86ad145">Range&lt;T&gt;::begin</a>()<span class="keyword"> const</span>
<a name="l00123"></a>00123 <span class="keyword"></span>{
<a name="l00124"></a>00124     <span class="keywordflow">return</span> mBegin;
<a name="l00125"></a>00125 }
<a name="l00126"></a>00126 
<a name="l00127"></a>00127 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00128"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#639b15c01cb026a8c6f9689f20ed84c1">00128</a> T* <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;T&gt;::end</a>()<span class="keyword"> const</span>
<a name="l00129"></a>00129 <span class="keyword"></span>{
<a name="l00130"></a>00130     <span class="keywordflow">return</span> mEnd;
<a name="l00131"></a>00131 }
<a name="l00132"></a>00132 
<a name="l00133"></a>00133 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00134"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#5d7d8a09e16cb3e3a0137563571588dc">00134</a> T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html#5d7d8a09e16cb3e3a0137563571588dc">Range&lt;T&gt;::front</a>()<span class="keyword"> const</span>
<a name="l00135"></a>00135 <span class="keyword"></span>{
<a name="l00136"></a>00136     <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(mBegin &lt; mEnd);
<a name="l00137"></a>00137     <span class="keywordflow">return</span> *mBegin;
<a name="l00138"></a>00138 }
<a name="l00139"></a>00139 
<a name="l00140"></a>00140 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00141"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#6eea0965791c328ef945c3c9ec16637b">00141</a> T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;T&gt;::back</a>()<span class="keyword"> const</span>
<a name="l00142"></a>00142 <span class="keyword"></span>{
<a name="l00143"></a>00143     <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(mBegin &lt; mEnd);
<a name="l00144"></a>00144     <span class="keywordflow">return</span> mEnd[-1];
<a name="l00145"></a>00145 }
<a name="l00146"></a>00146 
<a name="l00147"></a>00147 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00148"></a><a class="code" href="structnv_1_1cloth_1_1_range.html#194cc89be14aa7944b95fd8bf0a948fd">00148</a> T&amp; <a class="code" href="structnv_1_1cloth_1_1_range.html#194cc89be14aa7944b95fd8bf0a948fd">Range&lt;T&gt;::operator[]</a>(uint32_t i)<span class="keyword"> const</span>
<a name="l00149"></a>00149 <span class="keyword"></span>{
<a name="l00150"></a>00150     <a class="code" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(mBegin + i &lt; mEnd);
<a name="l00151"></a>00151     <span class="keywordflow">return</span> mBegin[i];
<a name="l00152"></a>00152 }
<a name="l00153"></a>00153 
<a name="l00154"></a>00154 } <span class="comment">// namespace cloth</span>
<a name="l00155"></a>00155 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
