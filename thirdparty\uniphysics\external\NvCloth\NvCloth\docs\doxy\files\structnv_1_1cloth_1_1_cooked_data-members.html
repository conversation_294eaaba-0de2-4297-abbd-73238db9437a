<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::CookedData Member List</h1>This is the complete list of members for <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">mAnchors</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">mIndices</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">mNumParticles</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">mPhaseIndices</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">mPhaseTypes</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">mRestvalues</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">mSets</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">mStiffnessValues</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">mTetherLengths</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">mTriangles</a></td><td><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td><td></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
