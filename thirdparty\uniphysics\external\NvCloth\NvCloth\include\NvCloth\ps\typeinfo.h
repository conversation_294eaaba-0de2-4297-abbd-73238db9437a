#pragma once

// Minimal typeinfo.h for NvCloth compatibility
// This provides basic type information functionality

#include <typeinfo>

namespace physx {
namespace shdfnd {

// Basic type info wrapper
template<typename T>
struct TypeInfo {
    static const char* name() {
        return typeid(T).name();
    }
    
    static size_t hash() {
        return typeid(T).hash_code();
    }
};

} // namespace shdfnd
} // namespace physx
