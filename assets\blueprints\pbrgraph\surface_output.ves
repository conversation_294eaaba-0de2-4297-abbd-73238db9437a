var node0 = ::pbrgraph::nodes::srgb_to_linear::SrgbToLinear()

_editor.add_node(node0, 290.21326089297, -280.6304181197)

var node1 = ::blueprint::nodes::split::Split()

_editor.add_node(node1, -62.181609215933, -357.06927842162)

var node2 = ::pbrgraph::nodes::srgb_to_linear::SrgbToLinear()

_editor.add_node(node2, 15.56384293636, -475.97647639852)

var node3 = ::shadergraph::nodes::tex_coord::TexCoord()

_editor.add_node(node3, -172.36371127661, 423.6980202048)

var node4 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node4, 129.14787357894, -291.66831822291)

var node5 = ::blueprint::nodes::fetch::Fetch()
node5.index = "base_color"

_editor.add_node(node5, -478.73232634747, -289.36369311923)

var node6 = ::blueprint::nodes::fetch::Fetch()
node6.index = "normal"

_editor.add_node(node6, -567.7244027412, 439.13912166854)

var node7 = ::blueprint::nodes::fetch::Fetch()
node7.index = "tex_coord"

_editor.add_node(node7, -346.55109765219, 402.83632470383)

var node8 = ::blueprint::nodes::fetch::Fetch()
node8.index = "metallic_roughness"

_editor.add_node(node8, -615.77067687286, 207.55011253647)

var node9 = ::blueprint::nodes::fetch::Fetch()
node9.index = "occlusion"

_editor.add_node(node9, -397.3729735403, -97.631711977545)

var node10 = ::blueprint::nodes::fetch::Fetch()
node10.index = "factor"

_editor.add_node(node10, -245.39948266961, -344.43050091846)

var node11 = ::blueprint::nodes::fetch::Fetch()
node11.index = "emissive"

_editor.add_node(node11, -431.41751137739, -504.00490537593)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "model"
node12.var_type = [ "table", "array" ]

_editor.add_node(node12, -1119.4060240878, -206.44170627175)

var node13 = ::blueprint::nodes::output::Output()

node13.var_name = "normal"
node13.var_type = "num3"

_editor.add_node(node13, -145.77223551835, 520.5758389283)

var node14 = ::blueprint::nodes::output::Output()

node14.var_name = "normal_uv"
node14.var_type = "num2"

_editor.add_node(node14, -25.045456285595, 424.80743693546)

var node15 = ::blueprint::nodes::output::Output()

node15.var_name = "roughness"
node15.var_type = "num"

_editor.add_node(node15, 221.28788709904, 293.02696407597)

var node16 = ::blueprint::nodes::output::Output()

node16.var_name = "metallic"
node16.var_type = "num"

_editor.add_node(node16, 228.85436596403, 137.89705261351)

var node17 = ::blueprint::nodes::output::Output()

node17.var_name = "occlusion"
node17.var_type = "num"

_editor.add_node(node17, 215.59854619789, -52.66926313783)

var node18 = ::blueprint::nodes::output::Output()

node18.var_name = "albedo"
node18.var_type = "num3"

_editor.add_node(node18, 434.77999038492, -280.05938452684)

var node19 = ::blueprint::nodes::output::Output()

node19.var_name = "emission"
node19.var_type = "num3"

_editor.add_node(node19, 169.65750253615, -475.6953363765)

var node20 = ::blueprint::nodes::fetch::Fetch()
node20.index = "sheen"

_editor.add_node(node20, -523.38661228314, -785.44251205441)

var node21 = ::blueprint::nodes::fetch::Fetch()
node21.index = "color_factor"

_editor.add_node(node21, -313.00121271957, -770.22706363759)

var node22 = ::blueprint::nodes::output::Output()

node22.var_name = "sheen_color"
node22.var_type = "num3"

_editor.add_node(node22, 414.63521167336, -716.87157478393)

var node23 = ::blueprint::nodes::fetch::Fetch()
node23.index = "roughness_factor"

_editor.add_node(node23, -306.26356444214, -984.2545629354)

var node24 = ::blueprint::nodes::output::Output()

node24.var_name = "sheen_roughness"
node24.var_type = "num"

_editor.add_node(node24, 545.65142465505, -930.96017510449)

var node25 = ::blueprint::nodes::fetch::Fetch()
node25.index = "roughness_factor"

_editor.add_node(node25, -370.23993982233, 289.31416494535)

var node26 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node26, 56.16114368051, 282.36015346192)

var node27 = ::blueprint::nodes::fetch::Fetch()
node27.index = "metallic_factor"

_editor.add_node(node27, -360.63418705624, 80.252118702817)

var node28 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node28, 65.5730240309, 130.15306598572)

var node29 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node29, 125.84020092107, -730.44531663524)

var node30 = ::blueprint::nodes::fetch::Fetch()
node30.index = "roughness_texture"

_editor.add_node(node30, -310.31714813506, -875.17152511852)

var node31 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node31, 396.85787821183, -939.37556725404)

var node32 = ::pbrgraph::nodes::srgb_to_linear::SrgbToLinear()

_editor.add_node(node32, 274.61644043824, -718.04851537028)

var node33 = ::blueprint::nodes::subgraph::Subgraph()
node33.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node33, -226.43506912451, -235.15436770684)

var node34 = ::blueprint::nodes::subgraph::Subgraph()
node34.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node34, -184.74686745747, -484.18662990772)

var node35 = ::blueprint::nodes::subgraph::Subgraph()
node35.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node35, -82.591729846178, -680.14504244802)

var node36 = ::blueprint::nodes::fetch::Fetch()
node36.index = "color_texture"

_editor.add_node(node36, -315.86822451376, -665.81368384241)

var node37 = ::blueprint::nodes::subgraph::Subgraph()
node37.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node37, -42.722936166479, -860.04288395615)

var node38 = ::blueprint::nodes::subgraph::Subgraph()
node38.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node38, -168.55239245575, -75.929707451227)

var node39 = ::blueprint::nodes::split::Split()

_editor.add_node(node39, 39.184417694797, -93.084577923016)

var node40 = ::blueprint::nodes::subgraph::Subgraph()
node40.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node40, -333.70053829344, 509.47624365917)

var node41 = ::blueprint::nodes::subgraph::Subgraph()
node41.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node41, -353.40083852542, 184.41012444728)

var node42 = ::blueprint::nodes::split::Split()

_editor.add_node(node42, -138.44255154192, 180.12907270952)

var node43 = ::blueprint::nodes::split::Split()

_editor.add_node(node43, 242.8217515648, -887.21401714317)

var node44 = ::blueprint::nodes::fetch::Fetch()
node44.index = "clearcoat"

_editor.add_node(node44, -492.65676469254, -1203.614008964)

var node45 = ::blueprint::nodes::fetch::Fetch()
node45.index = "texture"

_editor.add_node(node45, -287.42265241032, -1206.6332510497)

var node46 = ::blueprint::nodes::subgraph::Subgraph()
node46.load_from_file(_editor, "../shadergraph/gltf_tex_sample.ves")

_editor.add_node(node46, -65.77464028505, -1206.4399879791)

var node47 = ::blueprint::nodes::fetch::Fetch()
node47.index = "factor"

_editor.add_node(node47, -279.13567030725, -1325.4178582814)

var node48 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node48, 303.7196031613, -1294.4276267704)

var node49 = ::blueprint::nodes::output::Output()

node49.var_name = "clearcoat"
node49.var_type = "num"

_editor.add_node(node49, 445.36408808674, -1284.0252534053)

var node50 = ::blueprint::nodes::split::Split()

_editor.add_node(node50, 134.88050131143, -1217.773594463)

var node51 = ::blueprint::nodes::output::Output()

node51.var_name = "clearcoat_roughness"
node51.var_type = "num"

_editor.add_node(node51, 433.11183204776, -1231.0192236444)

var node52 = ::blueprint::nodes::output::Output()

node52.var_name = "src_alpha"
node52.var_type = "num"

_editor.add_node(node52, -42.14746944191, -205.67015292678)

Blueprint.connect(node12, "var", node44, "items")
Blueprint.connect(node44, "item", node47, "items")
Blueprint.connect(node44, "item", node45, "items")
Blueprint.connect(node45, "item", node46, "items")
Blueprint.connect(node44, "key", node46, "prefix")
Blueprint.connect(node46, "rgb", node50, "xyz")
Blueprint.connect(node50, "y", node51, "var")
Blueprint.connect(node50, "x", node48, "a")
Blueprint.connect(node47, "item", node48, "b")
Blueprint.connect(node48, "v", node49, "var")
Blueprint.connect(node12, "var", node20, "items")
Blueprint.connect(node20, "item", node36, "items")
Blueprint.connect(node36, "item", node35, "items")
Blueprint.connect(node20, "key", node35, "prefix")
Blueprint.connect(node20, "item", node30, "items")
Blueprint.connect(node30, "item", node37, "items")
Blueprint.connect(node20, "key", node37, "prefix")
Blueprint.connect(node37, "rgb", node43, "xyz")
Blueprint.connect(node20, "item", node23, "items")
Blueprint.connect(node43, "x", node31, "a")
Blueprint.connect(node23, "item", node31, "b")
Blueprint.connect(node31, "v", node24, "var")
Blueprint.connect(node20, "item", node21, "items")
Blueprint.connect(node35, "rgb", node29, "a")
Blueprint.connect(node21, "item", node29, "b")
Blueprint.connect(node29, "v", node32, "srgb")
Blueprint.connect(node32, "ret", node22, "var")
Blueprint.connect(node12, "var", node11, "items")
Blueprint.connect(node11, "item", node34, "items")
Blueprint.connect(node11, "key", node34, "prefix")
Blueprint.connect(node34, "rgb", node2, "srgb")
Blueprint.connect(node2, "ret", node19, "var")
Blueprint.connect(node12, "var", node9, "items")
Blueprint.connect(node9, "item", node38, "items")
Blueprint.connect(node9, "key", node38, "prefix")
Blueprint.connect(node38, "rgb", node39, "xyz")
Blueprint.connect(node39, "x", node17, "var")
Blueprint.connect(node12, "var", node8, "items")
Blueprint.connect(node8, "item", node41, "items")
Blueprint.connect(node8, "key", node41, "prefix")
Blueprint.connect(node41, "rgb", node42, "xyz")
Blueprint.connect(node8, "item", node27, "items")
Blueprint.connect(node42, "z", node28, "a")
Blueprint.connect(node27, "item", node28, "b")
Blueprint.connect(node28, "v", node16, "var")
Blueprint.connect(node8, "item", node25, "items")
Blueprint.connect(node25, "item", node26, "a")
Blueprint.connect(node42, "y", node26, "b")
Blueprint.connect(node26, "v", node15, "var")
Blueprint.connect(node12, "var", node6, "items")
Blueprint.connect(node6, "item", node40, "items")
Blueprint.connect(node6, "key", node40, "prefix")
Blueprint.connect(node40, "rgb", node13, "var")
Blueprint.connect(node6, "item", node7, "items")
Blueprint.connect(node7, "item", node3, "set_id")
Blueprint.connect(node3, "uv", node14, "var")
Blueprint.connect(node12, "var", node5, "items")
Blueprint.connect(node5, "item", node33, "items")
Blueprint.connect(node5, "key", node33, "prefix")
Blueprint.connect(node33, "alpha", node52, "var")
Blueprint.connect(node5, "item", node10, "items")
Blueprint.connect(node10, "item", node1, "xyzw")
Blueprint.connect(node33, "rgb", node4, "a")
Blueprint.connect(node1, "xyz", node4, "b")
Blueprint.connect(node4, "v", node0, "srgb")
Blueprint.connect(node0, "ret", node18, "var")
