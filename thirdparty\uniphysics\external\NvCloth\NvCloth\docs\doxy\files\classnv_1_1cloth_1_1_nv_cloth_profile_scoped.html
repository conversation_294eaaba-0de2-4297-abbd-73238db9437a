<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::NvClothProfileScoped Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">NvClothProfileScoped</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::NvClothProfileScoped Class Reference</h1><!-- doxytag: class="nv::cloth::NvClothProfileScoped" --><code>#include &lt;<a class="el" href="_callbacks_8h-source.html">Callbacks.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_FORCE_INLINE&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#eb4cd36d11aa03f05d05a6f18f8b41cb">NvClothProfileScoped</a> (const char *eventName, bool detached, uint64_t contextId, const char *fileName, int lineno, physx::PxProfilerCallback *callback)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#803ffc5af708346f086454ca7c6948d9">~NvClothProfileScoped</a> (void)</td></tr>

<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxProfilerCallback *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">mCallback</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint64_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">mContextId</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">mDetached</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">const char *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">mEventName</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">mProfilerData</a></td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="eb4cd36d11aa03f05d05a6f18f8b41cb"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::NvClothProfileScoped" ref="eb4cd36d11aa03f05d05a6f18f8b41cb" args="(const char *eventName, bool detached, uint64_t contextId, const char *fileName, int lineno, physx::PxProfilerCallback *callback)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_FORCE_INLINE nv::cloth::NvClothProfileScoped::NvClothProfileScoped           </td>
          <td>(</td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>eventName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">bool&nbsp;</td>
          <td class="paramname"> <em>detached</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">uint64_t&nbsp;</td>
          <td class="paramname"> <em>contextId</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const char *&nbsp;</td>
          <td class="paramname"> <em>fileName</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>lineno</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">physx::PxProfilerCallback *&nbsp;</td>
          <td class="paramname"> <em>callback</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="803ffc5af708346f086454ca7c6948d9"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::~NvClothProfileScoped" ref="803ffc5af708346f086454ca7c6948d9" args="(void)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::NvClothProfileScoped::~NvClothProfileScoped           </td>
          <td>(</td>
          <td class="paramtype">void&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="561c535d463cb4ef349db1b13b52761b"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::mCallback" ref="561c535d463cb4ef349db1b13b52761b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxProfilerCallback* <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">nv::cloth::NvClothProfileScoped::mCallback</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="dbbaf92e01e8d8cd7c2a80242a60c5a3"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::mContextId" ref="dbbaf92e01e8d8cd7c2a80242a60c5a3" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint64_t <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">nv::cloth::NvClothProfileScoped::mContextId</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="3a008841539b432550e139510d84d987"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::mDetached" ref="3a008841539b432550e139510d84d987" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">bool <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">nv::cloth::NvClothProfileScoped::mDetached</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="e4bae4fa99fd522f226143f9b7f8a0fb"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::mEventName" ref="e4bae4fa99fd522f226143f9b7f8a0fb" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const char* <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">nv::cloth::NvClothProfileScoped::mEventName</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="45731a72d188fd61afab53f65793ae6b"></a><!-- doxytag: member="nv::cloth::NvClothProfileScoped::mProfilerData" ref="45731a72d188fd61afab53f65793ae6b" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">void* <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">nv::cloth::NvClothProfileScoped::mProfilerData</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_callbacks_8h-source.html">Callbacks.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
