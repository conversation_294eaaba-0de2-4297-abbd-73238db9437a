@echo off
echo 编译简化版Tantien编辑器...

REM 尝试找到Visual Studio编译器
set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC"
if not exist "%VSPATH%" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Tools\MSVC"
)
if not exist "%VSPATH%" (
    set "VSPATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC"
)

REM 查找最新版本的MSVC
for /f "delims=" %%i in ('dir "%VSPATH%" /b /ad /o-n 2^>nul ^| findstr /r "^[0-9]"') do (
    set "MSVCVER=%%i"
    goto :found_msvc
)

:found_msvc
if not defined MSVCVER (
    echo 未找到Visual Studio编译器
    echo 尝试使用系统默认编译器...
    cl.exe simple_editor.cpp /Fe:editor.exe
    goto :check_result
)

echo 找到MSVC版本: %MSVCVER%
set "CLPATH=%VSPATH%\%MSVCVER%\bin\Hostx64\x64\cl.exe"

if exist "%CLPATH%" (
    echo 使用编译器: %CLPATH%
    "%CLPATH%" simple_editor.cpp /Fe:editor.exe /EHsc
) else (
    echo 编译器路径不存在，尝试系统路径...
    cl.exe simple_editor.cpp /Fe:editor.exe /EHsc
)

:check_result
if exist "editor.exe" (
    echo 编译成功！
    echo 生成的可执行文件: editor.exe
    echo.
    echo 运行演示...
    editor.exe rendergraph
) else (
    echo 编译失败，尝试使用g++...
    g++ simple_editor.cpp -o editor.exe
    if exist "editor.exe" (
        echo 使用g++编译成功！
        editor.exe rendergraph
    ) else (
        echo 所有编译尝试都失败了
        echo 请确保安装了Visual Studio或MinGW
    )
)

pause
