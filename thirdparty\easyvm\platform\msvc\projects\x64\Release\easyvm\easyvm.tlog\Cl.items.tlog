C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\source\OpCodes.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\platform\msvc\projects\x64\Release\easyvm\OpCodes.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\source\Value.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\platform\msvc\projects\x64\Release\easyvm\Value.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\source\VM.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\platform\msvc\projects\x64\Release\easyvm\VM.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\source\VMHelper.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easyvm\platform\msvc\projects\x64\Release\easyvm\VMHelper.obj
