var node0 = ::geograph::nodes::brush_to_gltf::BrushToGltf()
node0.query_param("adjacencies").value = false

_editor.add_node(node0, -233.2981122369, -246.77417200059)

var node1 = ::blueprint::nodes::fetch::Fetch()
node1.index = "node0"

_editor.add_node(node1, 392.27798280781, -403.81316996516)

var node2 = ::blueprint::nodes::fetch::Fetch()
node2.index = "va"

_editor.add_node(node2, 606.40270246338, -401.55926091791)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "geos"
node3.var_type = "array"

_editor.add_node(node3, 1000.0138585934, 109.41430090309)

var node4 = ::rendergraph::nodes::draw::Draw()

node4.set_prim_type("triangles")
node4.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node4.skip = false

_editor.add_node(node4, 784.30380359571, -191.81447758435)

var node5 = ::rendergraph::nodes::shader::Shader()
node5.query_param("inc_dir").value = ""

node5.vs = "
#version 330 core
layout (location = 0) in vec3  aPos;
layout (location = 1) in vec3  aCol;
layout (location = 2) in float aMatID;
layout (location = 3) in float aOffset;
layout (location = 4) in mat4 aInstanceMatrix;

out VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;

	float offset;

	bool use_inst_mat;	
};

void main()
{
	float dy = aOffset * 1.0;
	dy = max(0.0, dy - offset);

	vec3 pos = aPos;
	pos.y += dy;

	mat4 model_mat = use_inst_mat ? aInstanceMatrix : model;

	vs_out.frag_pos = vec3(model_mat * vec4(pos, 1.0));

	vs_out.frag_col = aCol;

    gl_Position = projection * view * model_mat * vec4(pos, 1.0);
}
"
node5.tcs = ""
node5.tes = ""
node5.gs = ""
node5.fs = "
#version 330 core
layout (location = 0) out vec3 gMain;
layout (location = 1) out vec3 gDepth;
layout (location = 2) out vec3 gNormal;

in VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} fs_in;

uniform UBO
{
	vec3 light_pos;
	vec3 cam_pos;

	float geo_idx;	
};

float near = 0.01;
float far = 100.0; 
float LinearizeDepth(float depth) 
{
    float z = depth * 2.0 - 1.0; // back to NDC 
    return (2.0 * near * far) / (far + near - z * (far - near));	
}

void main()
{
 	vec3 dFdxPos = dFdx( fs_in.frag_pos );
    vec3 dFdyPos = dFdy( fs_in.frag_pos );
    vec3 N = normalize( cross(dFdxPos,dFdyPos ));

	// ambient
	vec3 ambient = vec3(0.25);

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * fs_in.frag_col;

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(N, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    float depth = LinearizeDepth(gl_FragCoord.z) / far; // divide by far to get depth in range [0,1] for visualization purposes
//    depth = depth + 0.001 * mod(geo_idx, 10);
    depth = depth + 0.001 * geo_idx;
    gDepth = vec3(depth);

    gMain = ambient + diffuse + specular; 
    gNormal = N;
}
"
node5.cs = ""
node5.render_gen()

_editor.add_node(node5, 605.45756861689, -133.49176664733)

var node6 = ::rendergraph::nodes::clear::Clear()

node6.masks = [ "color", "depth" ]
node6.values = { "color" : [ 0, 0, 0, 0 ] }

_editor.add_node(node6, 769.17729517107, 294.19647127855)

var node7 = ::blueprint::nodes::perspective::Perspective()

node7.fovy = 45
node7.aspect = 0
node7.znear = 0.01
node7.zfar = 100

_editor.add_node(node7, -35.39599251269, 251.7399246789)

var node8 = ::blueprint::nodes::proxy::Proxy()

node8.real_name = "view_cam"
node8.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node8, 402.06615286487, 48.85559416076)

var node9 = ::blueprint::nodes::number3::Number3()

node9.value.set(5.9766573905945, 5.6433238983154, 5.3099908828735)

_editor.add_node(node9, 99.017136713578, -122.99093408604)

var node10 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node10, 1164.9441409146, 107.00451606488)

var node11 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node11, 400.11502375197, 165.469198382)

var node12 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node12, 1011.9389355872, 20.226619751981)

var node13 = ::geograph::nodes::draw_geometry::DrawGeometry()
node13.query_param("skip").value = false

_editor.add_node(node13, 783.1307712437, 32.615556424757)

var node14 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node14, 583.27974385043, 110.02639030966)

var node15 = ::rendergraph::nodes::render_target::RenderTarget()

_editor.add_node(node15, 1006.2254582471, -601.81666748678)

var node16 = ::rendergraph::nodes::texture::Texture()
node16.query_param("unif_name").value = "u_tex"
node16.gamma_correction = false
node16.mipmap = false
node16.init_texture(0, 0, "rgb")

_editor.add_node(node16, 474.40405076971, -784.61954261786)

var node17 = ::rendergraph::nodes::texture::Texture()
node17.query_param("unif_name").value = "u_tex"
node17.gamma_correction = false
node17.mipmap = false
node17.init_texture(0, 0, "r16f")

_editor.add_node(node17, 720.05358296636, -779.70827458377)

var node18 = ::rendergraph::nodes::texture::Texture()
node18.query_param("unif_name").value = "u_tex"
node18.gamma_correction = false
node18.mipmap = false
node18.init_texture(0, 0, "rgb16f")

_editor.add_node(node18, 942.95412422541, -781.75098051994)

var node19 = ::rendergraph::nodes::render_buffer::RenderBuffer()

node19.init_rbo(0, 0, "depth_component")

_editor.add_node(node19, 761.28070305769, -623.97703707472)

var node20 = ::blueprint::nodes::commentary::Commentary()

node20.set_size(934.75103759766, 556.74530029297)
node20.title = "GBuffer"

_editor.add_node(node20, 797.3810625679, -480.95532357993)

var node21 = ::rendergraph::nodes::pass::Pass()

node21.once = false

_editor.add_node(node21, 1436.8208839844, -147.05825367542)

var node22 = ::rendergraph::nodes::draw::Draw()

node22.set_prim_type("tri_strip")
node22.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node22.skip = false

_editor.add_node(node22, 1850.7890513854, -817.59943915683)

var node23 = ::rendergraph::nodes::shader::Shader()
node23.query_param("inc_dir").value = ""

node23.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;

out vec2 TexCoord;

void main()
{
	gl_Position = vec4(aPos, 1.0);
	TexCoord = vec2(aTexCoord.x, aTexCoord.y);
}
"
node23.tcs = ""
node23.tes = ""
node23.gs = ""
node23.fs = "
#version 330 core
out vec4 FragColor;

in vec2 TexCoord;

uniform UBO
{
	float scale;
	float depthThreshold;
	float normalThreshold;
};

uniform sampler2D main_tex;
uniform sampler2D cam_depth_tex;
uniform sampler2D cam_normal_tex;

vec4 alphaBlend(vec4 top, vec4 bottom)
{
	vec3 color = (top.rgb * top.a) + (bottom.rgb * (1 - top.a));
	float alpha = top.a + bottom.a * (1 - top.a);

	return vec4(color, alpha);
}

void main()
{
	float depth = texture(cam_depth_tex, TexCoord).r;

	float halfScaleFloor = floor(scale * 0.5);
	float halfScaleCeil = ceil(scale * 0.5);

	vec2 _MainTex_TexelSize = vec2(1.0/1024.0, 1.0/768.0);

	vec2 bottomLeftUV = TexCoord - vec2(_MainTex_TexelSize.x, _MainTex_TexelSize.y) * halfScaleFloor;
	vec2 topRightUV = TexCoord + vec2(_MainTex_TexelSize.x, _MainTex_TexelSize.y) * halfScaleCeil;  
	vec2 bottomRightUV = TexCoord + vec2(_MainTex_TexelSize.x * halfScaleCeil, -_MainTex_TexelSize.y * halfScaleFloor);
	vec2 topLeftUV = TexCoord + vec2(-_MainTex_TexelSize.x * halfScaleFloor, _MainTex_TexelSize.y * halfScaleCeil);

	vec3 normal0 = texture(cam_normal_tex, bottomLeftUV).rgb;
	vec3 normal1 = texture(cam_normal_tex, topRightUV).rgb;
	vec3 normal2 = texture(cam_normal_tex, bottomRightUV).rgb;
	vec3 normal3 = texture(cam_normal_tex, topLeftUV).rgb;

	vec3 normalFiniteDifference0 = normal1 - normal0;
	vec3 normalFiniteDifference1 = normal3 - normal2;
	float edgeNormal = sqrt(dot(normalFiniteDifference0, normalFiniteDifference0) + dot(normalFiniteDifference1, normalFiniteDifference1));
	edgeNormal = edgeNormal > normalThreshold ? 1 : 0;

	float depth0 = texture(cam_depth_tex, bottomLeftUV).r;
	float depth1 = texture(cam_depth_tex, topRightUV).r;
	float depth2 = texture(cam_depth_tex, bottomRightUV).r;
	float depth3 = texture(cam_depth_tex, topLeftUV).r;

	float depthFiniteDifference0 = depth1 - depth0;
	float depthFiniteDifference1 = depth3 - depth2;
	float edgeDepth = sqrt(pow(depthFiniteDifference0, 2) + pow(depthFiniteDifference1, 2)) * 100;	
	edgeDepth = (edgeDepth) > depthThreshold ? 1 : 0;

	float edge = max(edgeDepth, edgeNormal);

	vec4 _Color = vec4(0.0, 0.0, 0.0, 1.0);
	//vec4 _Color = vec4(1.0);
	vec4 edgeColor = vec4(_Color.rgb, _Color.a * edge);

	vec4 color = texture(main_tex, TexCoord);
	FragColor = alphaBlend(edgeColor, color);
}
"
node23.cs = ""
node23.render_gen()
node23.set_uniform("scale", [ 1 ])
node23.set_uniform("normalThreshold", [ 0.35301756858826 ])

_editor.add_node(node23, 1456.0473424022, -843.15563683398)

var node24 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node24.type = "quad"
node24.layout = [ "position", "texture" ]
node24.shape_params = {  }

_editor.add_node(node24, 1639.493374151, -884.79448653931)

var node25 = ::rendergraph::nodes::clear::Clear()

node25.masks = [ "color" ]
node25.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node25, 1620.5813407813, -636.93494254883)

var node26 = ::blueprint::nodes::commentary::Commentary()

node26.set_size(635.08331298828, 464.41665649414)
node26.title = "Outline"

_editor.add_node(node26, 1629.0608817969, -515.58837882813)

var node27 = ::rendergraph::nodes::pass::Pass()

node27.once = false

_editor.add_node(node27, 2594.1057567941, -694.26369554953)

var node28 = ::rendergraph::nodes::render_target::RenderTarget()

_editor.add_node(node28, 2532.6442579609, -820.59767915388)

var node29 = ::rendergraph::nodes::texture::Texture()
node29.query_param("unif_name").value = "u_tex"
node29.gamma_correction = false
node29.mipmap = false
node29.init_texture(0, 0, "rgb")

_editor.add_node(node29, 2169.5975193976, -1011.5945192769)

var node30 = ::rendergraph::nodes::draw::Draw()

node30.set_prim_type("tri_strip")
node30.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node30.skip = false

_editor.add_node(node30, 2659.8492689063, -983.98777384766)

var node31 = ::rendergraph::nodes::shader::Shader()
node31.query_param("inc_dir").value = ""

node31.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;

out vec2 TexCoord;

void main()
{
	gl_Position = vec4(aPos, 1.0);
	TexCoord = vec2(aTexCoord.x, aTexCoord.y);
}
"
node31.tcs = ""
node31.tes = ""
node31.gs = ""
node31.fs = "
#version 330
out vec4 FragColor;

uniform sampler2D tex;

in vec2 TexCoord;

void main() 
{
	const float FXAA_REDUCE_MIN = 1.0 / 128.0;
	const float FXAA_REDUCE_MUL = 1.0 / 8.0;
	const float FXAA_SPAN_MAX = 8.0;

	vec2 screenSizeInv = 1.0 / textureSize(tex, 0);
	
	vec2 tcrgbNW = (TexCoord + vec2(-1.0, -1.0) * screenSizeInv);
	vec2 tcrgbNE = (TexCoord + vec2(1.0, -1.0) * screenSizeInv);
	vec2 tcrgbSW = (TexCoord + vec2(-1.0, 1.0) * screenSizeInv);
	vec2 tcrgbSE = (TexCoord + vec2(1.0, 1.0) * screenSizeInv);
	vec2 tcrgbM = vec2(TexCoord);
	
	vec3 rgbNW = textureLod(tex, tcrgbNW, 0.0).rgb;
	vec3 rgbNE = textureLod(tex, tcrgbNE, 0.0).rgb;
	vec3 rgbSW = textureLod(tex, tcrgbSW, 0.0).rgb;
	vec3 rgbSE = textureLod(tex, tcrgbSE, 0.0).rgb;
	vec4 texColor = textureLod(tex, tcrgbM, 0.0);
	vec3 rgbM  = texColor.rgb;
	vec3 luma = vec3(0.299, 0.587, 0.114);
	float lumaNW = dot(rgbNW, luma);
	float lumaNE = dot(rgbNE, luma);
	float lumaSW = dot(rgbSW, luma);
	float lumaSE = dot(rgbSE, luma);
	float lumaM  = dot(rgbM,  luma);
	float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));
	float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));
	
	vec2 dir;
	dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));
	dir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));
	
	float dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) *
						  (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);
	
	float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);
	dir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX),
			  max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),
			  dir * rcpDirMin)) * screenSizeInv;
			  
	vec3 rgbA = 0.5 * (
		textureLod(tex, TexCoord + dir * (1.0 / 3.0 - 0.5), 0.0).rgb +
		textureLod(tex, TexCoord + dir * (2.0 / 3.0 - 0.5), 0.0).rgb);
	FragColor.rgb = rgbA * 0.5 + 0.25 * ( // vec3 rgbB
		textureLod(tex, TexCoord + dir * -0.5, 0.0).rgb +
		textureLod(tex, TexCoord + dir * 0.5, 0.0).rgb);
		
	// float lumaB = dot(rgbB, luma);
	float lumaB = dot(FragColor.rgb, luma);
	if ((lumaB < lumaMin) || (lumaB > lumaMax)) FragColor.rgb = rgbA;
	// else FragColor.rgb = rgbB;
}
"
node31.cs = ""
node31.render_gen()

_editor.add_node(node31, 2439.6102256368, -918.82419357788)

var node32 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node32.type = "quad"
node32.layout = [ "position", "texture" ]
node32.shape_params = {  }

_editor.add_node(node32, 2504.1860373729, -1082.7474231804)

var node33 = ::blueprint::nodes::commentary::Commentary()

node33.set_size(682.41668701172, 682.41668701172)
node33.title = "FXAA"

_editor.add_node(node33, 2394.868311875, -602.64295939453)

var node34 = ::blueprint::nodes::input::Input()

node34.var_name = "light_pos"
node34.var_type = "num3"

_editor.add_node(node34, 4.9501538176719, -24.821615711059)

var node35 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node35, 257.79715545555, -38.816832960415)

var node36 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node36, 131.84016370285, -15.95797169282)

var node37 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node37, 439.55755615234, -635.83148803711)

var node38 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node38, 2133.8637990142, -891.18750354357)

var node39 = ::blueprint::nodes::input::Input()

node39.var_name = "offset"
node39.var_type = "num"

_editor.add_node(node39, 413.20924919696, -205.46166739976)

var node40 = ::blueprint::nodes::length::Length()

_editor.add_node(node40, -570.47489716987, -459.53048874703)

var node41 = ::blueprint::nodes::compare::Compare()

node41.cmp = "greater"

_editor.add_node(node41, -439.69221047291, -498.14228436647)

var node42 = ::blueprint::nodes::integer::Integer()

node42.value = 0

_editor.add_node(node42, -574.92884716709, -515.42251863096)

var node43 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node43, -621.57041197351, -397.18619920922)

var node44 = ::blueprint::nodes::n_o_t::NOT()

_editor.add_node(node44, -495.36216881714, -400.78040521356)

var node45 = ::blueprint::nodes::a_n_d::AND()

_editor.add_node(node45, -253.66468770139, -422.33304670692)

var node46 = ::blueprint::nodes::orthographic::Orthographic()

node46.left = -1.5
node46.right = 1
node46.bottom = 0
node46.top = 1
node46.near = 1
node46.far = -1

_editor.add_node(node46, -129.16467389827, 398.62770768744)

var node47 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node47, -546.92415431579, 409.2175287042)

var node48 = ::blueprint::nodes::split::Split()

_editor.add_node(node48, -415.96621321593, 408.65306990171)

var node49 = ::blueprint::nodes::divide::Divide()

_editor.add_node(node49, -278.23458011229, 438.57017137206)

var node50 = ::blueprint::nodes::number::Number()

node50.value = 0.32784762978554

_editor.add_node(node50, -530.50628134814, 300.43876159183)

var node51 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node51, 128.24325551026, 414.6561433197)

var node52 = ::blueprint::nodes::input::Input()

node52.var_name = "ortho"
node52.var_type = "bool"

_editor.add_node(node52, -187.82443756782, 598.41099214835)

var node53 = ::blueprint::nodes::store::Store()

node53.var_name = "ortho"

_editor.add_node(node53, -31.490615907779, 596.88507075492)

var node54 = ::blueprint::nodes::load::Load()

node54.var_name = "ortho"

_editor.add_node(node54, -91.746058337229, 523.25617959175)

var node55 = ::blueprint::nodes::load::Load()

node55.var_name = "ortho"

_editor.add_node(node55, 997.57368297679, -1026.9346942078)

var node56 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node56, 1136.9570067209, -1047.0249629162)

var node57 = ::blueprint::nodes::number::Number()

node57.value = 0.0001

_editor.add_node(node57, 996.93920714177, -1083.2246731741)

var node58 = ::blueprint::nodes::number::Number()

node58.value = 0.15

_editor.add_node(node58, 993.5241920331, -1150.159968538)

var node59 = ::blueprint::nodes::number2::Number2()

node59.value.set(0.18203972280025, 0.87092864513397)

_editor.add_node(node59, -402.3835868223, 198.18406105164)

var node60 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node60, -404.73064421295, 273.88628710736)

var node61 = ::blueprint::nodes::integer::Integer()

node61.value = 10

_editor.add_node(node61, -535.74505929333, 238.49376363056)

var node62 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node62, -266.88611742774, 174.53887819146)

var node63 = ::blueprint::nodes::integer::Integer()

node63.value = 3

_editor.add_node(node63, -399.76329006827, 124.24425306656)

var node64 = ::blueprint::nodes::input::Input()

node64.var_name = "model"
node64.var_type = "mat4"

_editor.add_node(node64, 280.96244330514, 603.28062731543)

var node65 = ::blueprint::nodes::subgraph::Subgraph()
node65.load_from_file(_editor, "../tools/opt_value.ves")

_editor.add_node(node65, 468.02582763672, 567.49682617188)

var node66 = ::blueprint::nodes::matrix::Matrix()

_editor.add_node(node66, 285.03167267401, 519.36319908558)

var node67 = ::blueprint::nodes::number3::Number3()

node67.value.set(0.25385114550591, 0.35385113954544, 0.36496224999428)

_editor.add_node(node67, 59.653396606445, 748.71981201172)

var node68 = ::blueprint::nodes::property::Property()

node68.var_name = "no_bg"
node68.var_type = "bool"

_editor.add_node(node68, 767.35557890986, 421.60414954163)

var node69 = ::rendergraph::nodes::clear::Clear()

node69.masks = [ "color", "depth" ]
node69.values = { "color" : [ 0.5, 0.5, 0.5, 1 ] }

_editor.add_node(node69, 633.46477948575, 301.90323384557)

var node70 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node70, 916.99616491947, 394.74632729775)

Blueprint.connect(node68, "var", node70, "cond")
Blueprint.connect(node6, "next", node70, "true")
Blueprint.connect(node69, "next", node70, "false")
Blueprint.connect(node70, "next", node10, "prev")
Blueprint.connect(node3, "var", node10, "in")
Blueprint.connect(node12, "next", node10, "do")
Blueprint.connect(node67, "v3", node66, "scaling")
Blueprint.connect(node64, "var", node65, "val")
Blueprint.connect(node66, "mat", node65, "default")
Blueprint.connect(node59, "v2", node62, "a")
Blueprint.connect(node63, "v", node62, "b")
Blueprint.connect(node52, "var", node53, "var")
Blueprint.connect(node53, "var", node55, "var")
Blueprint.connect(node55, "var", node56, "cond")
Blueprint.connect(node57, "v", node56, "true")
Blueprint.connect(node58, "v", node56, "false")
Blueprint.connect(node53, "var", node54, "var")
Blueprint.connect(node50, "v", node60, "a")
Blueprint.connect(node61, "v", node60, "b")
Blueprint.connect(node47, "size", node48, "xy")
Blueprint.connect(node48, "x", node49, "a")
Blueprint.connect(node48, "y", node49, "b")
Blueprint.connect(node49, "v", node46, "aspect")
Blueprint.connect(node60, "v", node46, "scale")
Blueprint.connect(node62, "v", node46, "offset")
Blueprint.connect(node38, "size", node29, "size")
Blueprint.connect(node29, "tex", node31, "tex")
Blueprint.connect(node29, "tex", node28, "col0")
Blueprint.connect(node37, "size", node19, "size")
Blueprint.connect(node37, "size", node18, "size")
Blueprint.connect(node37, "size", node17, "size")
Blueprint.connect(node37, "size", node16, "size")
Blueprint.connect(node56, "result", node23, "depthThreshold")
Blueprint.connect(node17, "tex", node23, "cam_depth_tex")
Blueprint.connect(node18, "tex", node23, "cam_normal_tex")
Blueprint.connect(node16, "tex", node23, "main_tex")
Blueprint.connect(node16, "tex", node15, "col0")
Blueprint.connect(node17, "tex", node15, "col1")
Blueprint.connect(node18, "tex", node15, "col2")
Blueprint.connect(node19, "rbo", node15, "depth")
Blueprint.connect(node34, "var", node36, "in")
Blueprint.connect(node31, "out", node30, "shader")
Blueprint.connect(node32, "out", node30, "va")
Blueprint.connect(node25, "next", node22, "prev")
Blueprint.connect(node23, "out", node22, "shader")
Blueprint.connect(node24, "out", node22, "va")
Blueprint.connect(node10, "next", node21, "do")
Blueprint.connect(node15, "fbo", node21, "fbo")
Blueprint.connect(node21, "next", node27, "prev")
Blueprint.connect(node22, "next", node27, "do")
Blueprint.connect(node28, "fbo", node27, "fbo")
Blueprint.connect(node36, "out", node35, "cond")
Blueprint.connect(node9, "v3", node35, "true")
Blueprint.connect(node34, "var", node35, "false")
Blueprint.connect(node8, "zoom", node7, "fovy")
Blueprint.connect(node54, "var", node51, "cond")
Blueprint.connect(node46, "mat", node51, "true")
Blueprint.connect(node7, "mat", node51, "false")
Blueprint.connect(node51, "result", node14, "a")
Blueprint.connect(node8, "mat", node14, "b")
Blueprint.connect(node10, "out", node13, "geos")
Blueprint.connect(node14, "v", node13, "mat")
Blueprint.connect(node10, "out", node0, "brush")
Blueprint.connect(node0, "inst_mats", node43, "in")
Blueprint.connect(node43, "out", node44, "in")
Blueprint.connect(node0, "inst_mats", node40, "v")
Blueprint.connect(node40, "v", node41, "a")
Blueprint.connect(node42, "v", node41, "b")
Blueprint.connect(node44, "out", node45, "a")
Blueprint.connect(node41, "out", node45, "b")
Blueprint.connect(node64, "var", node5, "model")
Blueprint.connect(node8, "mat", node5, "view")
Blueprint.connect(node51, "result", node5, "projection")
Blueprint.connect(node39, "var", node5, "offset")
Blueprint.connect(node45, "out", node5, "use_inst_mat")
Blueprint.connect(node35, "result", node5, "light_pos")
Blueprint.connect(node8, "pos", node5, "cam_pos")
Blueprint.connect(node10, "curr_idx", node5, "geo_idx")
Blueprint.connect(node0, "gltf", node11, "in")
Blueprint.connect(node11, "out", node12, "cond")
Blueprint.connect(node13, "next", node12, "true")
Blueprint.connect(node4, "next", node12, "false")
Blueprint.connect(node0, "gltf", node1, "items")
Blueprint.connect(node1, "item", node2, "items")
Blueprint.connect(node5, "out", node4, "shader")
Blueprint.connect(node2, "item", node4, "va")
Blueprint.connect(node0, "inst_mats", node4, "inst_mats")
