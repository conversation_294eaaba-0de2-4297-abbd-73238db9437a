#include "logger/logger.h"

#include <iostream>
#include <sstream>
#include <iomanip>
#include <chrono>

namespace logger {

std::string GetTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

void PrintLog(LogLevel level, const std::string& file, int line, const std::string& message) {
    std::string timestamp = GetTimestamp();
    std::string location = file + ":" + std::to_string(line);
    
    const char* level_str = "";
    switch (level) {
        case LogLevel::Debug: level_str = "DEBUG"; break;
        case LogLevel::Info: level_str = "INFO"; break;
        case LogLevel::Warning: level_str = "WARNING"; break;
        case LogLevel::Error: level_str = "ERROR"; break;
        case LogLevel::Fatal: level_str = "FATAL"; break;
    }
    
    std::string full_message = "[" + timestamp + "] [" + level_str + "] [" + location + "] " + message;
    Log(level, full_message);
}

} // namespace logger
