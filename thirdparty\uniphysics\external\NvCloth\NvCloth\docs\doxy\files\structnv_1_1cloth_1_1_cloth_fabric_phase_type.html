<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothFabricPhaseType Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html">ClothFabricPhaseType</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothFabricPhaseType Struct Reference<br>
<small>
[<a class="el" href="group__extensions.html">Extensions</a>]</small>
</h1><!-- doxytag: class="nv::cloth::ClothFabricPhaseType" -->Describe type of phase in cloth fabric.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_cloth_fabric_phase_type-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Types</h2></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Describe type of phase in cloth fabric. 
<p>
<dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> for an explanation of concepts on phase and set. </dd></dl>
<hr><h2>Member Enumeration Documentation</h2>
<a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c8"></a><!-- doxytag: member="nv::cloth::ClothFabricPhaseType::Enum" ref="7ac4204396c4dc558681a39e1f4ad8c8" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">nv::cloth::ClothFabricPhaseType::Enum</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45"></a><!-- doxytag: member="eINVALID" ref="7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45" args="" -->eINVALID</em>&nbsp;</td><td>
invalid type </td></tr>
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c895d408850eff3c958150f13eea8728df"></a><!-- doxytag: member="eVERTICAL" ref="7ac4204396c4dc558681a39e1f4ad8c895d408850eff3c958150f13eea8728df" args="" -->eVERTICAL</em>&nbsp;</td><td>
resists stretching or compression, usually along the gravity </td></tr>
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c86b9950273c603473058bf8374ae22412"></a><!-- doxytag: member="eHORIZONTAL" ref="7ac4204396c4dc558681a39e1f4ad8c86b9950273c603473058bf8374ae22412" args="" -->eHORIZONTAL</em>&nbsp;</td><td>
resists stretching or compression, perpendicular to the gravity </td></tr>
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c85a210009d7fffa6fb7fbf246e40b1eb9"></a><!-- doxytag: member="eBENDING" ref="7ac4204396c4dc558681a39e1f4ad8c85a210009d7fffa6fb7fbf246e40b1eb9" args="" -->eBENDING</em>&nbsp;</td><td>
resists out-of-plane bending in angle-based formulation </td></tr>
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c89e3f928ec6acb0a8ab211149afc9e24c"></a><!-- doxytag: member="eSHEARING" ref="7ac4204396c4dc558681a39e1f4ad8c89e3f928ec6acb0a8ab211149afc9e24c" args="" -->eSHEARING</em>&nbsp;</td><td>
resists in-plane shearing along (typically) diagonal edges, </td></tr>
<tr><td valign="top"><em><a class="anchor" name="7ac4204396c4dc558681a39e1f4ad8c83033500239eabb666a723f55c257ad2f"></a><!-- doxytag: member="eCOUNT" ref="7ac4204396c4dc558681a39e1f4ad8c83033500239eabb666a723f55c257ad2f" args="" -->eCOUNT</em>&nbsp;</td><td>
</td></tr>
</table>
</dl>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
