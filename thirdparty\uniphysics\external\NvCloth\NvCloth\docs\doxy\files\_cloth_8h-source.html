<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Cloth.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Cloth.h</h1><a href="_cloth_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include "<a class="code" href="_range_8h.html">NvCloth/Range.h</a>"</span>
<a name="l00033"></a>00033 <span class="preprocessor">#include "<a class="code" href="_phase_config_8h.html">NvCloth/PhaseConfig.h</a>"</span>
<a name="l00034"></a>00034 <span class="preprocessor">#include &lt;foundation/PxVec3.h&gt;</span>
<a name="l00035"></a>00035 <span class="preprocessor">#include "<a class="code" href="_allocator_8h.html" title="This file together with Callbacks.h define most memory management interfaces for...">NvCloth/Allocator.h</a>"</span>
<a name="l00036"></a>00036 
<a name="l00037"></a>00037 <span class="keyword">struct </span>ID3D11Buffer;
<a name="l00038"></a>00038 
<a name="l00039"></a>00039 <span class="keyword">namespace </span>nv
<a name="l00040"></a>00040 {
<a name="l00041"></a>00041 <span class="keyword">namespace </span>cloth
<a name="l00042"></a>00042 {
<a name="l00043"></a>00043 
<a name="l00044"></a>00044 <span class="keyword">class </span>Factory;
<a name="l00045"></a>00045 <span class="keyword">class </span>Fabric;
<a name="l00046"></a>00046 <span class="keyword">class </span>Cloth;
<a name="l00047"></a>00047 
<a name="l00048"></a>00048 <span class="preprocessor">#ifdef _MSC_VER </span>
<a name="l00049"></a>00049 <span class="preprocessor"></span><span class="preprocessor">#pragma warning(disable : 4371) // layout of class may have changed from a previous version of the compiler due to</span>
<a name="l00050"></a>00050 <span class="preprocessor"></span>                                <span class="comment">// better packing of member</span>
<a name="l00051"></a>00051 <span class="preprocessor">#endif</span>
<a name="l00052"></a>00052 <span class="preprocessor"></span>
<a name="l00053"></a>00053 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00054"></a><a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">00054</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a> : <span class="keyword">public</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;T&gt;
<a name="l00055"></a>00055 {
<a name="l00056"></a><a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#52e834449347f418cab023175a53f7dc">00056</a>     <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#52e834449347f418cab023175a53f7dc">MappedRange</a>(T* first, T* last, <span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; cloth, <span class="keywordtype">void</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>::*lock)() <span class="keyword">const</span>, <span class="keywordtype">void</span> (<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>::*unlock)() <span class="keyword">const</span>)
<a name="l00057"></a>00057     : <a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;T&gt;(first, last), mCloth(cloth), mLock(lock), mUnlock(unlock)
<a name="l00058"></a>00058     {
<a name="l00059"></a>00059     }
<a name="l00060"></a>00060 
<a name="l00061"></a><a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#88d1b22544c74ada526357be9cd99328">00061</a>     <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#52e834449347f418cab023175a53f7dc">MappedRange</a>(<span class="keyword">const</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&amp; other)
<a name="l00062"></a>00062     : <a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;T&gt;(other), mCloth(other.mCloth), mLock(other.mLock), mUnlock(other.mUnlock)
<a name="l00063"></a>00063     {
<a name="l00064"></a>00064         (mCloth.*mLock)();
<a name="l00065"></a>00065     }
<a name="l00066"></a>00066 
<a name="l00067"></a><a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#62b2655a79f97194636a1fc82f898bf4">00067</a>     <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html#62b2655a79f97194636a1fc82f898bf4">~MappedRange</a>()
<a name="l00068"></a>00068     {
<a name="l00069"></a>00069         (mCloth.*mUnlock)();
<a name="l00070"></a>00070     }
<a name="l00071"></a>00071 
<a name="l00072"></a>00072   <span class="keyword">private</span>:
<a name="l00073"></a>00073     <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&amp; operator = (<span class="keyword">const</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&amp;);
<a name="l00074"></a>00074 
<a name="l00075"></a>00075     <span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; mCloth;
<a name="l00076"></a>00076     void (<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>::*mLock)() <span class="keyword">const</span>;
<a name="l00077"></a>00077     void (<a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>::*mUnlock)() <span class="keyword">const</span>;
<a name="l00078"></a>00078 };
<a name="l00079"></a>00079 
<a name="l00080"></a><a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html">00080</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html">GpuParticles</a>
<a name="l00081"></a>00081 {
<a name="l00082"></a><a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">00082</a>     physx::PxVec4* <a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">mCurrent</a>;
<a name="l00083"></a><a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">00083</a>     physx::PxVec4* <a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">mPrevious</a>;
<a name="l00084"></a><a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">00084</a>     ID3D11Buffer* <a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">mBuffer</a>;
<a name="l00085"></a>00085 };
<a name="l00086"></a>00086 
<a name="l00087"></a>00087 <span class="comment">// abstract cloth instance</span>
<a name="l00088"></a><a class="code" href="classnv_1_1cloth_1_1_cloth.html">00088</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> : <span class="keyword">public</span> UserAllocated
<a name="l00089"></a>00089 {
<a name="l00090"></a>00090   <span class="keyword">protected</span>:
<a name="l00091"></a><a class="code" href="classnv_1_1cloth_1_1_cloth.html#6991f178368b6de52fe4dce86f10910f">00091</a>     <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6991f178368b6de52fe4dce86f10910f">Cloth</a>() {}
<a name="l00092"></a>00092     <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6991f178368b6de52fe4dce86f10910f">Cloth</a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp;);
<a name="l00093"></a>00093     <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#0844b06815e4395bbc6f9c00d2d4bb24">operator = </a>(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>&amp;);
<a name="l00094"></a>00094 
<a name="l00095"></a>00095   <span class="keyword">public</span>:
<a name="l00096"></a><a class="code" href="classnv_1_1cloth_1_1_cloth.html#6de3f7e8b8d71624daa22cebf41b5679">00096</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6de3f7e8b8d71624daa22cebf41b5679">~Cloth</a>() {}
<a name="l00097"></a>00097 
<a name="l00104"></a>00104     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* <a class="code" href="classnv_1_1cloth_1_1_cloth.html#ac8169cc296ebabd715f51ece660a2e5" title="Creates a duplicate of this Cloth instance.">clone</a>(<a class="code" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a>&amp; factory) <span class="keyword">const</span> = 0;
<a name="l00105"></a>00105 
<a name="l00107"></a>00107     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#14174ed06c234119fd42bbddbaabc5f1" title="Returns the fabric used to create this Cloth.">getFabric</a>() <span class="keyword">const</span> = 0;
<a name="l00109"></a>00109     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_factory.html" title="abstract factory to create context-specific simulation components such as cloth,...">Factory</a>&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#dfb665fce893853e21ddbd3241685d7f" title="Returns the Factory used to create this Cloth.">getFactory</a>() <span class="keyword">const</span> = 0;
<a name="l00110"></a>00110 
<a name="l00111"></a>00111     <span class="comment">/* particle properties */</span>
<a name="l00113"></a>00113     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#cf0e6ac1e540ae6d2f7a9450a42fcb18" title="Returns the number of particles simulated by this fabric.">getNumParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00115"></a>00115     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#00d6c87135168af45d1b9694433f9036" title="Used internally to synchronize CPU and GPU particle memory.">lockParticles</a>() <span class="keyword">const</span> = 0; <span class="comment">//Might be better if it was called map/unmapParticles</span>
<a name="l00117"></a>00117 <span class="comment"></span>    <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#ffc4d89e66969c5fcf3b4ac2af01fe9d" title="Used internally to synchronize CPU and GPU particle memory.">unlockParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00118"></a>00118 
<a name="l00124"></a>00124     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange&lt;physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d" title="Returns the simulation particles of the current frame.">getCurrentParticles</a>() = 0;
<a name="l00125"></a>00125 
<a name="l00130"></a>00130     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange&lt;const physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d" title="Returns the simulation particles of the current frame.">getCurrentParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00131"></a>00131 
<a name="l00135"></a>00135     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange&lt;physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#9b22cae129eb4d9677fdea24fa5ec486" title="Returns the simulation particles of the previous frame.">getPreviousParticles</a>() = 0;
<a name="l00136"></a>00136 
<a name="l00140"></a>00140     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange&lt;const physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#9b22cae129eb4d9677fdea24fa5ec486" title="Returns the simulation particles of the previous frame.">getPreviousParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00141"></a>00141 
<a name="l00143"></a>00143     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_gpu_particles.html">GpuParticles</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#8d31c57a119fb853d4ceb1e197b2351a" title="Returns platform dependent pointers to the current GPU particle memory.">getGpuParticles</a>() = 0;
<a name="l00144"></a>00144 
<a name="l00145"></a>00145 
<a name="l00152"></a>00152     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#080cb97581d6e37079b6f62a7abfced0" title="Set the translation of the local space simulation after next call to simulate().">setTranslation</a>(<span class="keyword">const</span> physx::PxVec3&amp; trans) = 0;
<a name="l00153"></a>00153 
<a name="l00158"></a>00158     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#f86beb891c025a1e4cfd1135e9ad8ae7" title="Set the rotation of the local space simulation after next call to simulate().">setRotation</a>(<span class="keyword">const</span> physx::PxQuat&amp; rot) = 0;
<a name="l00159"></a>00159 
<a name="l00161"></a>00161     <span class="keyword">virtual</span> <span class="keyword">const</span> physx::PxVec3&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#519a015726fbc04a7bcf60afcfe3b0ca" title="Returns the current translation value that was set using setTranslation().">getTranslation</a>() <span class="keyword">const</span> = 0;
<a name="l00163"></a>00163     <span class="keyword">virtual</span> <span class="keyword">const</span> physx::PxQuat&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#78235f2aa83c32ccf35b6da0e221fe8e" title="Returns the current rotation value that was set using setRotation().">getRotation</a>() <span class="keyword">const</span> = 0;
<a name="l00164"></a>00164 
<a name="l00166"></a>00166     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#8b2a9bc21d7c04bd0e656b911282000b" title="Set inertia derived from setTranslation() and setRotation() to zero (once).">clearInertia</a>() = 0;
<a name="l00167"></a>00167 
<a name="l00169"></a>00169     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#86e8ce29e3d64732d8940857115f397e" title="Adjust the position of the cloth without affecting the dynamics (to call after a...">teleport</a>(<span class="keyword">const</span> physx::PxVec3&amp; delta) = 0;
<a name="l00170"></a>00170 
<a name="l00171"></a>00171     <span class="comment">/* solver parameters */</span>
<a name="l00172"></a>00172 
<a name="l00174"></a>00174     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#b8dee849c57c802ed40234edeaa998be" title="Returns the delta time used for previous iteration.">getPreviousIterationDt</a>() <span class="keyword">const</span> = 0;
<a name="l00175"></a>00175 
<a name="l00177"></a>00177     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#0223c7c82b616a6df01d7a4ffb57d916" title="Sets gravity in global coordinates.">setGravity</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00179"></a>00179     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#92a76707f82caf33088f23983d5ede03" title="Returns gravity set with setGravity().">getGravity</a>() <span class="keyword">const</span> = 0;
<a name="l00180"></a>00180 
<a name="l00184"></a>00184     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#f1e7d559fd4942d82432aeb6ab477cf6" title="Sets damping of local particle velocity (1/stiffnessFrequency).">setDamping</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00186"></a>00186     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e12abf9173dbcbb09690f229b8c8b7dd" title="Returns value set with setDamping().">getDamping</a>() <span class="keyword">const</span> = 0;
<a name="l00187"></a>00187 
<a name="l00188"></a>00188     <span class="comment">// portion of local frame velocity applied to particles</span>
<a name="l00189"></a>00189     <span class="comment">// 0 (default): particles are unaffected</span>
<a name="l00190"></a>00190     <span class="comment">// same as damping: damp global particle velocity</span>
<a name="l00191"></a>00191     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e97296e9eec127303d96b4febe90b43e">setLinearDrag</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00192"></a>00192     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#fa87c1d6ab87c5d7edbd48b5eb755659">getLinearDrag</a>() <span class="keyword">const</span> = 0;
<a name="l00193"></a>00193     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#0878320c241cee9876c2ac3122d80cb8">setAngularDrag</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00194"></a>00194     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#294f5e23749618c8e90f35bd851270f3">getAngularDrag</a>() <span class="keyword">const</span> = 0;
<a name="l00195"></a>00195 
<a name="l00199"></a>00199     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#eb427bb61aac45279fd32f3c0dc5b66c" title="Set the portion of local frame linear acceleration applied to particles.">setLinearInertia</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00201"></a>00201     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6" title="Returns value set with getLinearInertia().">getLinearInertia</a>() <span class="keyword">const</span> = 0;
<a name="l00203"></a>00203     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e14358081c4d1f30c14f08f3c71e38b8" title="Similar to setLinearInertia(), but for angular inertia.">setAngularInertia</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00205"></a>00205     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#079c1d3a32dd4657631820ac01a1f3bb" title="Returns value set with setAngularInertia().">getAngularInertia</a>() <span class="keyword">const</span> = 0;
<a name="l00207"></a>00207     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#ba9e3001d7c11d70526ef281febe8484" title="Similar to setLinearInertia(), but for centrifugal inertia.">setCentrifugalInertia</a>(<span class="keyword">const</span> physx::PxVec3&amp;) = 0;
<a name="l00209"></a>00209     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#f87e077bafe91061772683416c849484" title="Returns value set with setCentrifugalInertia().">getCentrifugalInertia</a>() <span class="keyword">const</span> = 0;
<a name="l00210"></a>00210 
<a name="l00214"></a>00214     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#b17d1cc294a3792c5c35e4ab353fac29" title="Set target solver iterations per second.">setSolverFrequency</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00216"></a>00216     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01" title="Returns gravity set with getSolverFrequency().*/.">getSolverFrequency</a>() <span class="keyword">const</span> = 0;
<a name="l00217"></a>00217 
<a name="l00218"></a>00218     <span class="comment">// damp, drag, stiffness exponent per second</span>
<a name="l00219"></a>00219     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#8a4512e945fa62ffd64d291686cc59a8">setStiffnessFrequency</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00220"></a>00220     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6476ef704ed1733398ba87093bc8bb22">getStiffnessFrequency</a>() <span class="keyword">const</span> = 0;
<a name="l00221"></a>00221 
<a name="l00222"></a>00222     <span class="comment">// filter width for averaging dt^2 factor of gravity and</span>
<a name="l00223"></a>00223     <span class="comment">// external acceleration, in numbers of iterations (default=30).</span>
<a name="l00224"></a>00224     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#d041f7c80ecb193154e0ccce59e81867">setAcceleationFilterWidth</a>(uint32_t) = 0;
<a name="l00225"></a>00225     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#56dd08034230d00dd332e01e65075ad6">getAccelerationFilterWidth</a>() <span class="keyword">const</span> = 0;
<a name="l00226"></a>00226 
<a name="l00227"></a>00227     <span class="comment">// setup edge constraint solver iteration</span>
<a name="l00228"></a>00228     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#ea95e56cb73720970f79903dcffc8360">setPhaseConfig</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const PhaseConfig&gt;</a> configs) = 0;
<a name="l00229"></a>00229 
<a name="l00230"></a>00230     <span class="comment">/* collision parameters */</span>
<a name="l00231"></a>00231 
<a name="l00239"></a>00239     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#31daeab54984168c8940f421c908e80f" title="Set spheres for collision detection.">setSpheres</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec4&gt;</a> spheres, uint32_t first, uint32_t last) = 0;
<a name="l00241"></a>00241     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#4b6b1d7fd2adfbc9d7ac66bbb9c418dc" title="Returns the number of spheres currently set.">getNumSpheres</a>() <span class="keyword">const</span> = 0;
<a name="l00242"></a>00242 
<a name="l00243"></a>00243 
<a name="l00257"></a>00257     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#ee30e619014cf93c518170b4b7a96df5" title="Set indices for capsule collision detection.">setCapsules</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> capsules, uint32_t first, uint32_t last) = 0;
<a name="l00259"></a>00259     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c9253d7ea3e5bb8b7389c6718d1d14e7" title="Returns the number of capsules (which is half the number of capsule indices).">getNumCapsules</a>() <span class="keyword">const</span> = 0;
<a name="l00260"></a>00260 
<a name="l00267"></a>00267     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#204fe4d80dd980b3fe45ec98270ebcd4" title="Sets plane values to be used with convex collision detection.">setPlanes</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec4&gt;</a> planes, uint32_t first, uint32_t last) = 0;
<a name="l00269"></a>00269     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#63c8731061e061c5d69c43c83a1f7213" title="Returns the number of planes currently set.">getNumPlanes</a>() <span class="keyword">const</span> = 0;
<a name="l00270"></a>00270 
<a name="l00276"></a>00276     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#b05513e796029c7562a1ff6fb740e561" title="Enable planes for collision.">setConvexes</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> convexMasks, uint32_t first, uint32_t last) = 0;
<a name="l00278"></a>00278     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#72d40e0b094a67c5a75c3a442aff4d88" title="Returns the number of convexMasks currently set.">getNumConvexes</a>() <span class="keyword">const</span> = 0;
<a name="l00279"></a>00279 
<a name="l00284"></a>00284     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e5d69e455fee1bdd9f92ef888e8d2514" title="Set triangles for collision.">setTriangles</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec3&gt;</a> triangles, uint32_t first, uint32_t last) = 0;
<a name="l00285"></a>00285     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e5d69e455fee1bdd9f92ef888e8d2514" title="Set triangles for collision.">setTriangles</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec3&gt;</a> triangles, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec3&gt;</a>, uint32_t first) = 0;
<a name="l00287"></a>00287     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#9dc99e94a2d2698b7c165160dc850337" title="Returns the number of triangles currently set.">getNumTriangles</a>() <span class="keyword">const</span> = 0;
<a name="l00288"></a>00288 
<a name="l00290"></a>00290     <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#5afecc558e319c61e047a2bb8b113b40" title="Returns true if we use ccd.">isContinuousCollisionEnabled</a>() <span class="keyword">const</span> = 0;
<a name="l00292"></a>00292     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#f901e20a52f80678a0e4aad59bdd8286" title="Set if we use ccd or not (disabled by default).">enableContinuousCollision</a>(<span class="keywordtype">bool</span>) = 0;
<a name="l00293"></a>00293 
<a name="l00294"></a>00294     <span class="comment">// controls how quickly mass is increased during collisions</span>
<a name="l00295"></a>00295     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c5c1bf8f32e9add7d6978cd80344a829">getCollisionMassScale</a>() <span class="keyword">const</span> = 0;
<a name="l00296"></a>00296     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c75a30c4f8f02312b112e9650e886edb">setCollisionMassScale</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00297"></a>00297 
<a name="l00299"></a>00299     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c10abfe96f96b65c9b3a5f37fee68715" title="Set the cloth collision shape friction coefficient.">setFriction</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00301"></a>00301     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#a2daf756406fd64e1b7b2174eb040367" title="Returns value set with setFriction().">getFriction</a>() <span class="keyword">const</span> = 0;
<a name="l00302"></a>00302 
<a name="l00303"></a>00303     <span class="comment">// set virtual particles for collision handling.</span>
<a name="l00304"></a>00304     <span class="comment">// each indices element consists of 3 particle</span>
<a name="l00305"></a>00305     <span class="comment">// indices and an index into the lerp weights array.</span>
<a name="l00306"></a>00306     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#285c31837f64c3cd60fce8ba269fe3f1">setVirtualParticles</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;<span class="keyword">const</span> uint32_t[4]&gt; indices, <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec3&gt;</a> weights) = 0;
<a name="l00307"></a>00307     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#7b67c8d82763c26d18d52e864137f46f">getNumVirtualParticles</a>() <span class="keyword">const</span> = 0;
<a name="l00308"></a>00308     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#a08c88cf1855e76452a783c336d1102c">getNumVirtualParticleWeights</a>() <span class="keyword">const</span> = 0;
<a name="l00309"></a>00309 
<a name="l00310"></a>00310     <span class="comment">/* tether constraint parameters */</span>
<a name="l00311"></a>00311 
<a name="l00316"></a>00316     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2e2b3e5e4542417c61bbe65064b6ba91" title="Set Tether constraint scale.">setTetherConstraintScale</a>(<span class="keywordtype">float</span> scale) = 0;
<a name="l00318"></a>00318     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#********************************" title="Returns value set with setTetherConstraintScale().">getTetherConstraintScale</a>() <span class="keyword">const</span> = 0;
<a name="l00323"></a>00323     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#7e8eadb5e98ea146ad2e079cfddeeb0c" title="Set Tether constraint stiffness.">setTetherConstraintStiffness</a>(<span class="keywordtype">float</span> stiffness) = 0;
<a name="l00325"></a>00325     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#d423f35a4952860552430fea2796ce15" title="Returns value set with setTetherConstraintStiffness().">getTetherConstraintStiffness</a>() <span class="keyword">const</span> = 0;
<a name="l00326"></a>00326 
<a name="l00327"></a>00327     <span class="comment">/* motion constraint parameters */</span>
<a name="l00328"></a>00328 
<a name="l00332"></a>00332     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#8f26feaee081f503440e077477d51d24" title="Returns reference to motion constraints (position, radius) The entire range must...">getMotionConstraints</a>() = 0;
<a name="l00335"></a>00335     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#82ab50da85a99a76060c7b9463fdf386" title="Removes all motion constraints.">clearMotionConstraints</a>() = 0;
<a name="l00336"></a>00336     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#639565810f9f83088f870643c957bee3">getNumMotionConstraints</a>() <span class="keyword">const</span> = 0;
<a name="l00337"></a>00337     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#349196b772aa39e8f3575baaf5dc35d6">setMotionConstraintScaleBias</a>(<span class="keywordtype">float</span> scale, <span class="keywordtype">float</span> bias) = 0;
<a name="l00338"></a>00338     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c0a1cd8a04b0e44580f53185bd3a7438">getMotionConstraintScale</a>() <span class="keyword">const</span> = 0;
<a name="l00339"></a>00339     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#12449a7e62ac5d66149510fe01c51126">getMotionConstraintBias</a>() <span class="keyword">const</span> = 0;
<a name="l00340"></a>00340     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#c8d1af1c6df5123d5c81331647b24a67">setMotionConstraintStiffness</a>(<span class="keywordtype">float</span> stiffness) = 0;
<a name="l00341"></a>00341     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#56bb155eacd1b0b2d1dc4803ff7c02a7">getMotionConstraintStiffness</a>() <span class="keyword">const</span> = 0;
<a name="l00342"></a>00342 
<a name="l00343"></a>00343     <span class="comment">/* separation constraint parameters */</span>
<a name="l00344"></a>00344 
<a name="l00345"></a>00345     <span class="comment">// return reference to separation constraints (position, radius)</span>
<a name="l00346"></a>00346     <span class="comment">// The entire range must be written after calling this function.</span>
<a name="l00347"></a>00347     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2c02b9284fb998282226b0a57209a7d3">getSeparationConstraints</a>() = 0;
<a name="l00348"></a>00348     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2a1776072b165064eddb3719633b291f">clearSeparationConstraints</a>() = 0;
<a name="l00349"></a>00349     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#7951527b51d5e4c523c179c5c7f34d3c">getNumSeparationConstraints</a>() <span class="keyword">const</span> = 0;
<a name="l00350"></a>00350 
<a name="l00351"></a>00351     <span class="comment">/* clear interpolation */</span>
<a name="l00352"></a>00352 
<a name="l00353"></a>00353     <span class="comment">// assign current to previous positions for</span>
<a name="l00354"></a>00354     <span class="comment">// collision spheres, motion, and separation constraints</span>
<a name="l00355"></a>00355     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2f5b55bbff3741ffd107d67bb63b2adf">clearInterpolation</a>() = 0;
<a name="l00356"></a>00356 
<a name="l00357"></a>00357     <span class="comment">/* particle acceleration parameters */</span>
<a name="l00358"></a>00358 
<a name="l00359"></a>00359     <span class="comment">// return reference to particle accelerations (in local coordinates)</span>
<a name="l00360"></a>00360     <span class="comment">// The entire range must be written after calling this function.</span>
<a name="l00361"></a>00361     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;physx::PxVec4&gt;</a> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#5b6086a1da8379361db57d0d3f6c8655">getParticleAccelerations</a>() = 0;
<a name="l00362"></a>00362     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6bd3a4e6c557ff981303f111db9d8aaa">clearParticleAccelerations</a>() = 0;
<a name="l00363"></a>00363     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#22f4390c87ae7e40704b7e346b6c3dc4">getNumParticleAccelerations</a>() <span class="keyword">const</span> = 0;
<a name="l00364"></a>00364 
<a name="l00365"></a>00365     <span class="comment">/* wind */</span>
<a name="l00366"></a>00366 
<a name="l00368"></a>00368     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#dd843ef612805153bdf04f2229697e0d" title="/brief Set wind in global coordinates.">setWindVelocity</a>(physx::PxVec3) = 0;
<a name="l00370"></a>00370     <span class="keyword">virtual</span> physx::PxVec3 <a class="code" href="classnv_1_1cloth_1_1_cloth.html#dc98811170dedd7f79c97a5ad289aeb2" title="Returns value set with setWindVelocity().">getWindVelocity</a>() <span class="keyword">const</span> = 0;
<a name="l00372"></a>00372     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#fce3065c14afac4e5cf6e93b5d60a007" title="/brief Sets the air drag coefficient.">setDragCoefficient</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00374"></a>00374     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#16b04df700089098bb956fcdc30e77b4" title="Returns value set with setDragCoefficient().">getDragCoefficient</a>() <span class="keyword">const</span> = 0;
<a name="l00376"></a>00376     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#17b5a40330eb57bdc495a2eb0d713193" title="/brief Sets the air lift coefficient.">setLiftCoefficient</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00378"></a>00378     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2dbaaaa013d7c69902c9d5eaa98f6af9" title="Returns value set with setLiftCoefficient().">getLiftCoefficient</a>() <span class="keyword">const</span> = 0;
<a name="l00380"></a>00380     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#23bb80bd7b7acd3caa9c2b792c41a752" title="/brief Sets the fluid density used for air drag/lift calculations.">setFluidDensity</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00382"></a>00382     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#31aeac4d22831073a79d2b6da53c17ae" title="Returns value set with setFluidDensity().">getFluidDensity</a>() <span class="keyword">const</span> = 0;
<a name="l00383"></a>00383 
<a name="l00384"></a>00384     <span class="comment">/* self collision */</span>
<a name="l00385"></a>00385 
<a name="l00387"></a>00387     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#8677510130ff4438306d20a413abd5d8" title="/brief Set the distance particles need to be separated from each other withing the...">setSelfCollisionDistance</a>(<span class="keywordtype">float</span> distance) = 0;
<a name="l00389"></a>00389     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#064c442c7588405581006d72aa3d88f9" title="Returns value set with setSelfCollisionDistance().">getSelfCollisionDistance</a>() <span class="keyword">const</span> = 0;
<a name="l00391"></a>00391     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#b9988307f35da068e3d2ff08b56d95a1" title="/brief Set the constraint stiffness for the self collision constraints.">setSelfCollisionStiffness</a>(<span class="keywordtype">float</span> stiffness) = 0;
<a name="l00393"></a>00393     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#209dae86682049c944b9c2ba41aeb0bf" title="Returns value set with setSelfCollisionStiffness().">getSelfCollisionStiffness</a>() <span class="keyword">const</span> = 0;
<a name="l00394"></a>00394 
<a name="l00399"></a>00399     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e77122c9d483539afe4b944429d5d464" title="Set self collision indices.">setSelfCollisionIndices</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a>) = 0;
<a name="l00401"></a>00401     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#44b430eff88a119b5242e5ed87722ee0" title="Returns the number of self collision indices set.">getNumSelfCollisionIndices</a>() <span class="keyword">const</span> = 0;
<a name="l00402"></a>00402 
<a name="l00403"></a>00403     <span class="comment">/* rest positions */</span>
<a name="l00404"></a>00404 
<a name="l00405"></a>00405     <span class="comment">// set rest particle positions used during self-collision</span>
<a name="l00406"></a>00406     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#263e8beebed6fb96f06bf2688a15ad1c">setRestPositions</a>(<a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const physx::PxVec4&gt;</a>) = 0;
<a name="l00407"></a>00407     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6549b36b76a8269864c695d3b77aae63">getNumRestPositions</a>() <span class="keyword">const</span> = 0;
<a name="l00408"></a>00408 
<a name="l00409"></a>00409     <span class="comment">/* bounding box */</span>
<a name="l00410"></a>00410 
<a name="l00412"></a>00412     <span class="keyword">virtual</span> <span class="keyword">const</span> physx::PxVec3&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6b0c89213073d0a58f2309b4c0526c7d" title="Returns current particle position bounds center in local space.">getBoundingBoxCenter</a>() <span class="keyword">const</span> = 0;
<a name="l00414"></a>00414     <span class="keyword">virtual</span> <span class="keyword">const</span> physx::PxVec3&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth.html#4b16c7cedaecc35b5d722040b28f7bdf" title="Returns current particle position bounds size in local space.">getBoundingBoxScale</a>() <span class="keyword">const</span> = 0;
<a name="l00415"></a>00415 
<a name="l00416"></a>00416     <span class="comment">/* sleeping (disabled by default) */</span>
<a name="l00417"></a>00417 
<a name="l00418"></a>00418     <span class="comment">// max particle velocity (per axis) to pass sleep test</span>
<a name="l00419"></a>00419     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#1a34c62e0891496b949194556dc729f1">setSleepThreshold</a>(<span class="keywordtype">float</span>) = 0;
<a name="l00420"></a>00420     <span class="keyword">virtual</span> <span class="keywordtype">float</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#2bd353debfdb900979395fe8870df1bf">getSleepThreshold</a>() <span class="keyword">const</span> = 0;
<a name="l00421"></a>00421     <span class="comment">// test sleep condition every nth millisecond</span>
<a name="l00422"></a>00422     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#de65cf56e6b20d5a44a222b9e28ecf2f">setSleepTestInterval</a>(uint32_t) = 0;
<a name="l00423"></a>00423     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#6313c4680c883d3bb6e20632ebde1ab8">getSleepTestInterval</a>() <span class="keyword">const</span> = 0;
<a name="l00424"></a>00424     <span class="comment">// put cloth to sleep when n consecutive sleep tests pass</span>
<a name="l00425"></a>00425     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#7ef6797d557a31d9380835d26a894f15">setSleepAfterCount</a>(uint32_t) = 0;
<a name="l00426"></a>00426     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#a3be62e917066f2f29f64320c8286893">getSleepAfterCount</a>() <span class="keyword">const</span> = 0;
<a name="l00427"></a>00427     <span class="keyword">virtual</span> uint32_t <a class="code" href="classnv_1_1cloth_1_1_cloth.html#7a07e21a9b99dd3eab429569c77eac1c">getSleepPassCount</a>() <span class="keyword">const</span> = 0;
<a name="l00428"></a>00428     <span class="keyword">virtual</span> <span class="keywordtype">bool</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#21e5c91983c11511600cfefd68be49f9">isAsleep</a>() <span class="keyword">const</span> = 0;
<a name="l00429"></a>00429     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#21da17df59669e7a2b670823400e740b">putToSleep</a>() = 0;
<a name="l00430"></a>00430     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#4294ed2b0a515600e9448264cc0377e3">wakeUp</a>() = 0;
<a name="l00431"></a>00431 
<a name="l00433"></a>00433     <span class="keyword">virtual</span> <span class="keywordtype">void</span> <a class="code" href="classnv_1_1cloth_1_1_cloth.html#e7c0b099e90d409a65ee14d6f77e57c5" title="Set user data.">setUserData</a>(<span class="keywordtype">void</span>*) = 0;
<a name="l00434"></a>00434     <span class="comment">// Returns value set by setUserData().</span>
<a name="l00435"></a>00435     <span class="keyword">virtual</span> <span class="keywordtype">void</span>* <a class="code" href="classnv_1_1cloth_1_1_cloth.html#91e02303afccc55bba87886c1187002b">getUserData</a>() <span class="keyword">const</span> = 0;
<a name="l00436"></a>00436 };
<a name="l00437"></a>00437 
<a name="l00438"></a>00438 <span class="comment">// wrappers to prevent non-const overload from marking particles dirty</span>
<a name="l00439"></a>00439 <span class="keyword">inline</span> MappedRange&lt;const physx::PxVec4&gt; readCurrentParticles(<span class="keyword">const</span> Cloth&amp; cloth)
<a name="l00440"></a>00440 {
<a name="l00441"></a>00441     <span class="keywordflow">return</span> cloth.getCurrentParticles();
<a name="l00442"></a>00442 }
<a name="l00443"></a>00443 <span class="keyword">inline</span> MappedRange&lt;const physx::PxVec4&gt; readPreviousParticles(<span class="keyword">const</span> Cloth&amp; cloth)
<a name="l00444"></a>00444 {
<a name="l00445"></a>00445     <span class="keywordflow">return</span> cloth.getPreviousParticles();
<a name="l00446"></a>00446 }
<a name="l00447"></a>00447 
<a name="l00448"></a>00448 } <span class="comment">// namespace cloth</span>
<a name="l00449"></a>00449 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
