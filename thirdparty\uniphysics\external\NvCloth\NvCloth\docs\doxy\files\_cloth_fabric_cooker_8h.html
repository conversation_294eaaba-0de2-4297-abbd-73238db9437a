<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: ClothFabricCooker.h File Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>ClothFabricCooker.h File Reference</h1><code>#include &quot;<a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a>&quot;</code><br>
<code>#include &quot;<a class="el" href="_fabric_8h-source.html">NvCloth/Fabric.h</a>&quot;</code><br>
<code>#include &quot;<a class="el" href="_factory_8h-source.html">NvCloth/Factory.h</a>&quot;</code><br>

<p>
<a href="_cloth_fabric_cooker_8h-source.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Classes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Use NvClothCreateFabricCooker() to create an implemented instance.  <a href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">References all the data required to create a fabric.  <a href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">nv::cloth::ClothFabricPhase</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">References a set of constraints that can be solved in parallel.  <a href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html">nv::cloth::ClothFabricPhaseType</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Describe type of phase in cloth fabric.  <a href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#_details">More...</a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">struct &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">nv::cloth::CookedData</a></td></tr>

<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="group__extensions.html#g927e2eff017f040fb3ed01823e46fc4a">NV_CLOTH_API</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">nv::cloth::ClothFabricCooker</a> *) NvClothCreateFabricCooker()</td></tr>

</table>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
