var node0 = ::blueprint::nodes::combine::Combine()

_editor.add_node(node0, 2078.4351541394, -187.95051532683)

var node1 = ::pbrgraph::nodes::gamma_correction::GammaCorrection()

_editor.add_node(node1, 1921.0457172345, -248.06803946099)

var node2 = ::blueprint::nodes::add::Add()

_editor.add_node(node2, 1608.8842720618, -257.51215881889)

var node3 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node3, 1467.0680126638, -223.35938090841)

var node4 = ::shadergraph::nodes::normal::Normal()

_editor.add_node(node4, -841.848966822, 300.2259895813)

var node5 = ::shadergraph::nodes::world_pos::WorldPos()

_editor.add_node(node5, -841.47619626178, 340.85758194888)

var node6 = ::pbrgraph::nodes::d__g_g_x::D_GGX()
node6.query_param("roughness").value = 1

_editor.add_node(node6, 298.41349334891, 335.89996592836)

var node7 = ::pbrgraph::nodes::v__smith::V_Smith()
node7.query_param("roughness").value = 1

_editor.add_node(node7, 297.60697815347, 238.82448117688)

var node8 = ::pbrgraph::nodes::f__schlick::F_Schlick()
node8.query_param("f0").value.set(0.04, 0.04, 0.04)
node8.query_param("f90").value = 1

_editor.add_node(node8, 295.14059315574, 115.93320080168)

var node9 = ::blueprint::nodes::commentary::Commentary()

node9.set_size(772.76666259766, 313.67291259766)
node9.title = "Dirs"

_editor.add_node(node9, -534.05133760624, 402.4286628871)

var node10 = ::blueprint::nodes::commentary::Commentary()

node10.set_size(1305.6239013672, 418.15728759766)
node10.title = "BRDF"

_editor.add_node(node10, 559.01484329245, 401.43955703439)

var node11 = ::shadergraph::nodes::uniform::Uniform()
node11.query_param("unif_name").value = "u_light_pos"

_editor.add_node(node11, -690.61066274567, 117.59253793371)

var node12 = ::shadergraph::nodes::mix::Mix()

_editor.add_node(node12, 162.35130508557, 115.22085632334)

var node13 = ::blueprint::nodes::number3::Number3()

node13.value.set(0, 0, 0)

_editor.add_node(node13, -12.17324652527, 145.74244765056)

var node14 = ::pbrgraph::nodes::fr__cook_torrance::Fr_CookTorrance()

_editor.add_node(node14, 808.58425056977, 279.36150616054)

var node15 = ::pbrgraph::nodes::fd__lambert::Fd_Lambert()

_editor.add_node(node15, 542.56241001751, 84.78258277928)

var node16 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node16, 681.85694111992, 113.21929390795)

var node17 = ::blueprint::nodes::add::Add()

_editor.add_node(node17, 993.52229781726, 184.3084537376)

var node18 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node18, 1126.6134796354, 161.73772646487)

var node19 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node19, 836.83136834371, 121.04591578306)

var node20 = ::pbrgraph::nodes::energy_conservation::EnergyConservation()
node20.query_param("metallic").value = 1

_editor.add_node(node20, 682.83985718507, 24.296191993459)

var node21 = ::shadergraph::nodes::uniform::Uniform()
node21.query_param("unif_name").value = "u_cam_pos"

_editor.add_node(node21, -692.72986010688, 166.10777370081)

var node22 = ::blueprint::nodes::commentary::Commentary()

node22.set_size(1110.9291992188, 238.18333435059)
node22.title = "Layers"

_editor.add_node(node22, 1767.0220246306, -86.610538452945)

var node23 = ::blueprint::nodes::input::Input()

node23.var_name = "model"
node23.var_type = [ "table", "array" ]

_editor.add_node(node23, -801.49414342334, -110.76888120669)

var node24 = ::blueprint::nodes::output::Output()

node24.var_name = "frag_out"
node24.var_type = "num4"

_editor.add_node(node24, 2245.7427376035, -148.09800294144)

var node25 = ::blueprint::nodes::input::Input()

node25.var_name = "cam_pos"
node25.var_type = "num3"

_editor.add_node(node25, -829.66771893993, 164.37491987428)

var node26 = ::blueprint::nodes::input::Input()

node26.var_name = "light_pos"
node26.var_type = "vec3"

_editor.add_node(node26, -827.50546940854, 116.34927425016)

var node27 = ::blueprint::nodes::subgraph::Subgraph()
node27.load_from_file(_editor, "lighting_dirs.ves")

_editor.add_node(node27, -418.80456583384, 258.31681043291)

var node28 = ::blueprint::nodes::subgraph::Subgraph()
node28.load_from_file(_editor, "../shadergraph/calc_normal.ves")

_editor.add_node(node28, -652.2500385659, 277.01316501149)

var node29 = ::blueprint::nodes::subgraph::Subgraph()
node29.load_from_file(_editor, "surface_output.ves")

_editor.add_node(node29, -612.28136214004, -219.0989631358)

var node30 = ::blueprint::nodes::store::Store()

node30.var_name = "NoV"

_editor.add_node(node30, -224.61428069973, 360.0555495095)

var node31 = ::blueprint::nodes::load::Load()

node31.var_name = "NoV"

_editor.add_node(node31, 153.89961870474, 271.77344874987)

var node32 = ::blueprint::nodes::store::Store()

node32.var_name = "NoH"

_editor.add_node(node32, -222.37718035193, 273.55720784956)

var node33 = ::blueprint::nodes::load::Load()

node33.var_name = "NoH"

_editor.add_node(node33, 157.44999057768, 357.40591423599)

var node34 = ::blueprint::nodes::store::Store()

node34.var_name = "NoL"

_editor.add_node(node34, -223.40191462921, 315.91844742837)

var node35 = ::blueprint::nodes::load::Load()

node35.var_name = "NoL"

_editor.add_node(node35, 154.14574828283, 228.82873693359)

var node36 = ::blueprint::nodes::store::Store()

node36.var_name = "VoH"

_editor.add_node(node36, -221.06643685362, 230.45816589943)

var node37 = ::blueprint::nodes::load::Load()

node37.var_name = "VoH"

_editor.add_node(node37, 164.29725609688, 48.02317475009)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "NoV"

_editor.add_node(node38, 665.68454082504, 262.84677388456)

var node39 = ::blueprint::nodes::load::Load()

node39.var_name = "NoL"

_editor.add_node(node39, 666.67483079069, 221.40576228094)

var node40 = ::blueprint::nodes::load::Load()

node40.var_name = "NoL"

_editor.add_node(node40, 994.33624401312, 115.84579288444)

var node41 = ::blueprint::nodes::store::Store()

node41.var_name = "roughness"

_editor.add_node(node41, -419.41260573509, -156.83300807813)

var node42 = ::blueprint::nodes::load::Load()

node42.var_name = "roughness"

_editor.add_node(node42, 157.37293972692, 315.59310871311)

var node43 = ::blueprint::nodes::store::Store()

node43.var_name = "metallic"

_editor.add_node(node43, -418.67646517435, -199.28240063969)

var node44 = ::blueprint::nodes::load::Load()

node44.var_name = "metallic"

_editor.add_node(node44, -10.466510250968, 21.442581779935)

var node45 = ::blueprint::nodes::store::Store()

node45.var_name = "occlusion"

_editor.add_node(node45, -418.10422158996, -240.85075542571)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "occlusion"

_editor.add_node(node46, 1326.5259607887, -243.16269152576)

var node47 = ::blueprint::nodes::store::Store()

node47.var_name = "emission"

_editor.add_node(node47, -417.97044566075, -326.5883007526)

var node48 = ::blueprint::nodes::load::Load()

node48.var_name = "emission"

_editor.add_node(node48, 1467.9415209858, -292.77966951532)

var node49 = ::blueprint::nodes::store::Store()

node49.var_name = "albedo"

_editor.add_node(node49, -420.27024103115, -284.77859159102)

var node50 = ::blueprint::nodes::load::Load()

node50.var_name = "albedo"

_editor.add_node(node50, -10.03528664559, 67.853914122389)

var node51 = ::blueprint::nodes::store::Store()

node51.var_name = "normal"

_editor.add_node(node51, -419.16103235765, -73.556644686929)

var node52 = ::blueprint::nodes::load::Load()

node52.var_name = "normal"

_editor.add_node(node52, -841.07587057012, 214.07811000354)

var node53 = ::blueprint::nodes::store::Store()

node53.var_name = "normal_uv"

_editor.add_node(node53, -419.35786846287, -115.47713870055)

var node54 = ::blueprint::nodes::load::Load()

node54.var_name = "normal_uv"

_editor.add_node(node54, -841.07272924886, 257.38643239852)

var node55 = ::blueprint::nodes::commentary::Commentary()

node55.set_size(573.14581298828, 432.41665649414)
node55.title = "Surface"

_editor.add_node(node55, -593.66848291233, -15.272842794165)

var node56 = ::blueprint::nodes::load::Load()

node56.var_name = "metallic"

_editor.add_node(node56, 547.87953552313, -5.766650626433)

var node57 = ::blueprint::nodes::load::Load()

node57.var_name = "albedo"

_editor.add_node(node57, 541.3250673946, 128.19626402314)

var node58 = ::blueprint::nodes::load::Load()

node58.var_name = "roughness"

_editor.add_node(node58, 157.2035975479, 186.66726308883)

var node59 = ::blueprint::nodes::subgraph::Subgraph()
node59.load_from_file(_editor, "ibl_brdf.ves")

_editor.add_node(node59, 414.02127693238, -324.20755904266)

var node60 = ::blueprint::nodes::load::Load()

node60.var_name = "albedo"

_editor.add_node(node60, 195.40794460855, -197.36074926847)

var node61 = ::blueprint::nodes::load::Load()

node61.var_name = "metallic"

_editor.add_node(node61, 194.40069151816, -238.29890722859)

var node62 = ::blueprint::nodes::load::Load()

node62.var_name = "roughness"

_editor.add_node(node62, 196.46030621039, -281.88197916948)

var node63 = ::blueprint::nodes::load::Load()

node63.var_name = "occlusion"

_editor.add_node(node63, 196.44637297541, -322.52721161281)

var node64 = ::blueprint::nodes::load::Load()

node64.var_name = "N"

_editor.add_node(node64, 196.92598128001, -363.16715806635)

var node65 = ::blueprint::nodes::load::Load()

node65.var_name = "V"

_editor.add_node(node65, 196.30507066172, -405.38981262775)

var node66 = ::blueprint::nodes::load::Load()

node66.var_name = "F"

_editor.add_node(node66, 197.82232904125, -449.74318784491)

var node67 = ::rendergraph::nodes::pass::Pass()

node67.once = true

_editor.add_node(node67, 629.18509049951, -226.70735528245)

var node68 = ::blueprint::nodes::add::Add()

_editor.add_node(node68, 1759.5174744922, -303.98650174418)

var node69 = ::blueprint::nodes::store::Store()

node69.var_name = "N"

_editor.add_node(node69, -221.65201057875, 187.07301948484)

var node70 = ::blueprint::nodes::store::Store()

node70.var_name = "V"

_editor.add_node(node70, -222.03509798917, 141.17749162154)

var node71 = ::blueprint::nodes::store::Store()

node71.var_name = "F"

_editor.add_node(node71, 450.99060058594, 174.42572021484)

var node72 = ::blueprint::nodes::load::Load()

node72.var_name = "F"

_editor.add_node(node72, 544.99060058594, 37.425720214844)

var node73 = ::blueprint::nodes::commentary::Commentary()

node73.set_size(624.99163818359, 357.49688720703)
node73.title = "IBL"

_editor.add_node(node73, 404.52647084277, -144.17956124609)

var node74 = ::blueprint::nodes::store::Store()

node74.var_name = "src_alpha"

_editor.add_node(node74, -419.56964923577, -367.63473267569)

var node75 = ::blueprint::nodes::load::Load()

node75.var_name = "src_alpha"

_editor.add_node(node75, 1918.9889437089, -192.19496504534)

Blueprint.connect(node59, "next", node67, "do")
Blueprint.connect(node26, "var", node11, "v")
Blueprint.connect(node25, "var", node21, "v")
Blueprint.connect(node23, "var", node29, "model")
Blueprint.connect(node29, "src_alpha", node74, "var")
Blueprint.connect(node74, "var", node75, "var")
Blueprint.connect(node29, "normal_uv", node53, "var")
Blueprint.connect(node53, "var", node54, "var")
Blueprint.connect(node29, "normal", node51, "var")
Blueprint.connect(node51, "var", node52, "var")
Blueprint.connect(node29, "albedo", node49, "var")
Blueprint.connect(node49, "var", node60, "var")
Blueprint.connect(node49, "var", node57, "var")
Blueprint.connect(node49, "var", node50, "var")
Blueprint.connect(node29, "emission", node47, "var")
Blueprint.connect(node47, "var", node48, "var")
Blueprint.connect(node29, "occlusion", node45, "var")
Blueprint.connect(node45, "var", node63, "var")
Blueprint.connect(node45, "var", node46, "var")
Blueprint.connect(node29, "metallic", node43, "var")
Blueprint.connect(node43, "var", node61, "var")
Blueprint.connect(node43, "var", node56, "var")
Blueprint.connect(node43, "var", node44, "var")
Blueprint.connect(node29, "roughness", node41, "var")
Blueprint.connect(node41, "var", node62, "var")
Blueprint.connect(node41, "var", node58, "var")
Blueprint.connect(node41, "var", node42, "var")
Blueprint.connect(node57, "var", node16, "a")
Blueprint.connect(node15, "ret", node16, "b")
Blueprint.connect(node13, "v3", node12, "x")
Blueprint.connect(node50, "var", node12, "y")
Blueprint.connect(node44, "var", node12, "a")
Blueprint.connect(node5, "pos", node28, "world_pos")
Blueprint.connect(node4, "normal", node28, "normal")
Blueprint.connect(node54, "var", node28, "tex_coords")
Blueprint.connect(node52, "var", node28, "normal_map")
Blueprint.connect(node28, "normal", node27, "normal")
Blueprint.connect(node5, "pos", node27, "world_pos")
Blueprint.connect(node21, "v", node27, "cam_pos")
Blueprint.connect(node11, "v", node27, "light_pos")
Blueprint.connect(node27, "V", node70, "var")
Blueprint.connect(node70, "var", node65, "var")
Blueprint.connect(node27, "N", node69, "var")
Blueprint.connect(node69, "var", node64, "var")
Blueprint.connect(node27, "VoH", node36, "var")
Blueprint.connect(node36, "var", node37, "var")
Blueprint.connect(node12, "mix", node8, "f0")
Blueprint.connect(node37, "var", node8, "VoH")
Blueprint.connect(node8, "ret", node71, "var")
Blueprint.connect(node71, "var", node72, "var")
Blueprint.connect(node72, "var", node20, "F")
Blueprint.connect(node56, "var", node20, "metallic")
Blueprint.connect(node16, "v", node19, "a")
Blueprint.connect(node20, "ret", node19, "b")
Blueprint.connect(node71, "var", node66, "var")
Blueprint.connect(node60, "var", node59, "albedo")
Blueprint.connect(node61, "var", node59, "metallic")
Blueprint.connect(node62, "var", node59, "roughness")
Blueprint.connect(node63, "var", node59, "ao")
Blueprint.connect(node64, "var", node59, "N")
Blueprint.connect(node65, "var", node59, "V")
Blueprint.connect(node66, "var", node59, "F")
Blueprint.connect(node27, "NoL", node34, "var")
Blueprint.connect(node34, "var", node40, "var")
Blueprint.connect(node34, "var", node39, "var")
Blueprint.connect(node34, "var", node35, "var")
Blueprint.connect(node27, "NoH", node32, "var")
Blueprint.connect(node32, "var", node33, "var")
Blueprint.connect(node33, "var", node6, "NoH")
Blueprint.connect(node42, "var", node6, "roughness")
Blueprint.connect(node27, "NoV", node30, "var")
Blueprint.connect(node30, "var", node38, "var")
Blueprint.connect(node30, "var", node31, "var")
Blueprint.connect(node31, "var", node7, "NoV")
Blueprint.connect(node35, "var", node7, "NoL")
Blueprint.connect(node58, "var", node7, "roughness")
Blueprint.connect(node6, "ret", node14, "D")
Blueprint.connect(node7, "ret", node14, "G")
Blueprint.connect(node8, "ret", node14, "F")
Blueprint.connect(node38, "var", node14, "NoV")
Blueprint.connect(node39, "var", node14, "NoL")
Blueprint.connect(node14, "ret", node17, "a")
Blueprint.connect(node19, "v", node17, "b")
Blueprint.connect(node17, "v", node18, "a")
Blueprint.connect(node40, "var", node18, "b")
Blueprint.connect(node18, "v", node3, "a")
Blueprint.connect(node46, "var", node3, "b")
Blueprint.connect(node3, "v", node2, "a")
Blueprint.connect(node48, "var", node2, "b")
Blueprint.connect(node2, "v", node68, "a")
Blueprint.connect(node59, "col", node68, "b")
Blueprint.connect(node68, "v", node1, "linear")
Blueprint.connect(node75, "var", node0, "w")
Blueprint.connect(node1, "non-linear", node0, "xyz")
Blueprint.connect(node0, "xyzw", node24, "var")
