<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::MappedRange&lt; T &gt; Struct Template Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::MappedRange&lt; T &gt; Struct Template Reference</h1><!-- doxytag: class="nv::cloth::MappedRange" --><!-- doxytag: inherits="Range&lt; T &gt;" --><code>#include &lt;<a class="el" href="_cloth_8h-source.html">Cloth.h</a>&gt;</code>
<p>
<div class="dynheader">
Inheritance diagram for nv::cloth::MappedRange&lt; T &gt;:</div>
<div class="dynsection">

<p><center><img src="structnv_1_1cloth_1_1_mapped_range.png" usemap="#nv::cloth::MappedRange< T >_map" border="0" alt=""></center>
<map name="nv::cloth::MappedRange< T >_map">
<area href="structnv_1_1cloth_1_1_range.html" alt="nv::cloth::Range< T >" shape="rect" coords="0,0,178,24">
</map>
</div>

<p>
<a href="structnv_1_1cloth_1_1_mapped_range-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html#88d1b22544c74ada526357be9cd99328">MappedRange</a> (const <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a> &amp;other)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html#52e834449347f418cab023175a53f7dc">MappedRange</a> (T *first, T *last, const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, void(Cloth::*lock)() const, void(Cloth::*unlock)() const)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html#62b2655a79f97194636a1fc82f898bf4">~MappedRange</a> ()</td></tr>

</table>
<h3>template&lt;typename T&gt;<br>
 struct nv::cloth::MappedRange&lt; T &gt;</h3>

<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="52e834449347f418cab023175a53f7dc"></a><!-- doxytag: member="nv::cloth::MappedRange::MappedRange" ref="52e834449347f418cab023175a53f7dc" args="(T *first, T *last, const Cloth &amp;cloth, void(Cloth::*lock)() const, void(Cloth::*unlock)() const)" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">nv::cloth::MappedRange</a>&lt; T &gt;::<a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>           </td>
          <td>(</td>
          <td class="paramtype">T *&nbsp;</td>
          <td class="paramname"> <em>first</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T *&nbsp;</td>
          <td class="paramname"> <em>last</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void(Cloth::*)() const &nbsp;</td>
          <td class="paramname"> <em>lock</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">void(Cloth::*)() const &nbsp;</td>
          <td class="paramname"> <em>unlock</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="88d1b22544c74ada526357be9cd99328"></a><!-- doxytag: member="nv::cloth::MappedRange::MappedRange" ref="88d1b22544c74ada526357be9cd99328" args="(const MappedRange &amp;other)" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">nv::cloth::MappedRange</a>&lt; T &gt;::<a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>&lt; T &gt; &amp;&nbsp;</td>
          <td class="paramname"> <em>other</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="62b2655a79f97194636a1fc82f898bf4"></a><!-- doxytag: member="nv::cloth::MappedRange::~MappedRange" ref="62b2655a79f97194636a1fc82f898bf4" args="()" -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename T &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">nv::cloth::MappedRange</a>&lt; T &gt;::~<a class="el" href="structnv_1_1cloth_1_1_mapped_range.html">MappedRange</a>           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_8h-source.html">Cloth.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
