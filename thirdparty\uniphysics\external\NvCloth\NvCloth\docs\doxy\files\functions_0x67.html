<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li class="current"><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
      <li><a href="functions_rela.html"><span>Related&nbsp;Functions</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html#index_a"><span>a</span></a></li>
      <li><a href="functions_0x62.html#index_b"><span>b</span></a></li>
      <li><a href="functions_0x63.html#index_c"><span>c</span></a></li>
      <li><a href="functions_0x64.html#index_d"><span>d</span></a></li>
      <li><a href="functions_0x65.html#index_e"><span>e</span></a></li>
      <li><a href="functions_0x66.html#index_f"><span>f</span></a></li>
      <li class="current"><a href="functions_0x67.html#index_g"><span>g</span></a></li>
      <li><a href="functions_0x68.html#index_h"><span>h</span></a></li>
      <li><a href="functions_0x69.html#index_i"><span>i</span></a></li>
      <li><a href="functions_0x6c.html#index_l"><span>l</span></a></li>
      <li><a href="functions_0x6d.html#index_m"><span>m</span></a></li>
      <li><a href="functions_0x6e.html#index_n"><span>n</span></a></li>
      <li><a href="functions_0x6f.html#index_o"><span>o</span></a></li>
      <li><a href="functions_0x70.html#index_p"><span>p</span></a></li>
      <li><a href="functions_0x71.html#index_q"><span>q</span></a></li>
      <li><a href="functions_0x72.html#index_r"><span>r</span></a></li>
      <li><a href="functions_0x73.html#index_s"><span>s</span></a></li>
      <li><a href="functions_0x74.html#index_t"><span>t</span></a></li>
      <li><a href="functions_0x75.html#index_u"><span>u</span></a></li>
      <li><a href="functions_0x77.html#index_w"><span>w</span></a></li>
      <li><a href="functions_0x7e.html#index_~"><span>~</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
Here is a list of all class members with links to the classes they belong to:
<p>
<h3><a class="anchor" name="index_g">- g -</a></h3><ul>
<li>getAccelerationFilterWidth()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#56dd08034230d00dd332e01e65075ad6">nv::cloth::Cloth</a>
<li>getAngularDrag()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#294f5e23749618c8e90f35bd851270f3">nv::cloth::Cloth</a>
<li>getAngularInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#079c1d3a32dd4657631820ac01a1f3bb">nv::cloth::Cloth</a>
<li>getBoundingBoxCenter()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#6b0c89213073d0a58f2309b4c0526c7d">nv::cloth::Cloth</a>
<li>getBoundingBoxScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#4b16c7cedaecc35b5d722040b28f7bdf">nv::cloth::Cloth</a>
<li>getCentrifugalInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#f87e077bafe91061772683416c849484">nv::cloth::Cloth</a>
<li>getClothList()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#a3e121ffbccc07180e08a2387eb4f6ac">nv::cloth::Solver</a>
<li>getCollisionMassScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c5c1bf8f32e9add7d6978cd80344a829">nv::cloth::Cloth</a>
<li>getContext()
: <a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#39f71451d6802462f724554a6d06004c">nv::cloth::DxContextManagerCallback</a>
<li>getCookedData()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#63c669356e99a97a7ed90788aec9b8f4">nv::cloth::ClothFabricCooker</a>
<li>getCookerStatus()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#e1670477f35e78f3ca1038a6093c1ac1">nv::cloth::ClothTetherCooker</a>
<li>getCurrentParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c659f1fa3f6dcf0eef323dc6bef81b9d">nv::cloth::Cloth</a>
<li>getDamping()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#e12abf9173dbcbb09690f229b8c8b7dd">nv::cloth::Cloth</a>
<li>getDescriptor()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html#35d0eeb9e4dc7156bf4993cd5c13a9d8">nv::cloth::ClothMeshQuadifier</a>
, <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#24e4bd5204366e6f8ddcfb7e27d4d19f">nv::cloth::ClothFabricCooker</a>
<li>getDevice()
: <a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#b3ca0c862df8de0e4022fcbfee5351a3">nv::cloth::DxContextManagerCallback</a>
<li>getDragCoefficient()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#16b04df700089098bb956fcdc30e77b4">nv::cloth::Cloth</a>
<li>getFabric()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#14174ed06c234119fd42bbddbaabc5f1">nv::cloth::Cloth</a>
<li>getFactory()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dfb665fce893853e21ddbd3241685d7f">nv::cloth::Cloth</a>
, <a class="el" href="classnv_1_1cloth_1_1_fabric.html#e6ab4bb76335c9af1a67435eb2520d62">nv::cloth::Fabric</a>
<li>getFluidDensity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#31aeac4d22831073a79d2b6da53c17ae">nv::cloth::Cloth</a>
<li>getFriction()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a2daf756406fd64e1b7b2174eb040367">nv::cloth::Cloth</a>
<li>getGpuParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8d31c57a119fb853d4ceb1e197b2351a">nv::cloth::Cloth</a>
<li>getGravity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#92a76707f82caf33088f23983d5ede03">nv::cloth::Cloth</a>
<li>getInterCollisionDistance()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#6a72529d713f46dbd17a5b541aaec6df">nv::cloth::Solver</a>
<li>getInterCollisionNbIterations()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#124dc836903890185934c6eaedec2079">nv::cloth::Solver</a>
<li>getInterCollisionStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#09e4be9b50229213a837d00a3f2f6a3f">nv::cloth::Solver</a>
<li>getLiftCoefficient()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2dbaaaa013d7c69902c9d5eaa98f6af9">nv::cloth::Cloth</a>
<li>getLinearDrag()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#fa87c1d6ab87c5d7edbd48b5eb755659">nv::cloth::Cloth</a>
<li>getLinearInertia()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#203a19cd80d2c9897df7c02006a05cb6">nv::cloth::Cloth</a>
<li>getMotionConstraintBias()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#12449a7e62ac5d66149510fe01c51126">nv::cloth::Cloth</a>
<li>getMotionConstraints()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#8f26feaee081f503440e077477d51d24">nv::cloth::Cloth</a>
<li>getMotionConstraintScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c0a1cd8a04b0e44580f53185bd3a7438">nv::cloth::Cloth</a>
<li>getMotionConstraintStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#56bb155eacd1b0b2d1dc4803ff7c02a7">nv::cloth::Cloth</a>
<li>getNbTethersPerParticle()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#683c0c2fbe6ba9127e55b20864b04035">nv::cloth::ClothTetherCooker</a>
<li>getNumCapsules()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#c9253d7ea3e5bb8b7389c6718d1d14e7">nv::cloth::Cloth</a>
<li>getNumCloths()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#de377e651711ebbb9e70f928cbb682e2">nv::cloth::Solver</a>
<li>getNumConvexes()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#72d40e0b094a67c5a75c3a442aff4d88">nv::cloth::Cloth</a>
<li>getNumIndices()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#057b35a8f4b7cce31a0be2eb0704e52d">nv::cloth::Fabric</a>
<li>getNumMotionConstraints()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#639565810f9f83088f870643c957bee3">nv::cloth::Cloth</a>
<li>getNumParticleAccelerations()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#22f4390c87ae7e40704b7e346b6c3dc4">nv::cloth::Cloth</a>
<li>getNumParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#cf0e6ac1e540ae6d2f7a9450a42fcb18">nv::cloth::Cloth</a>
, <a class="el" href="classnv_1_1cloth_1_1_fabric.html#8dd6c3990522e16832311a2b04b17619">nv::cloth::Fabric</a>
<li>getNumPhases()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#4d9348de98c1c00498709dc591fa27ba">nv::cloth::Fabric</a>
<li>getNumPlanes()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#63c8731061e061c5d69c43c83a1f7213">nv::cloth::Cloth</a>
<li>getNumRestPositions()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#6549b36b76a8269864c695d3b77aae63">nv::cloth::Cloth</a>
<li>getNumRestvalues()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#52c968ff1b808ab00d994db25bc01d83">nv::cloth::Fabric</a>
<li>getNumSelfCollisionIndices()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#44b430eff88a119b5242e5ed87722ee0">nv::cloth::Cloth</a>
<li>getNumSeparationConstraints()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7951527b51d5e4c523c179c5c7f34d3c">nv::cloth::Cloth</a>
<li>getNumSets()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#d28fcf11b0c9ebb20325cafb5dbcde4d">nv::cloth::Fabric</a>
<li>getNumSpheres()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#4b6b1d7fd2adfbc9d7ac66bbb9c418dc">nv::cloth::Cloth</a>
<li>getNumStiffnessValues()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#24d3ef1c25d42d981a12f5b7a96114e4">nv::cloth::Fabric</a>
<li>getNumTethers()
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#aa6b9b09786b98e3be8cc9f362c1f09d">nv::cloth::Fabric</a>
<li>getNumTriangles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#9dc99e94a2d2698b7c165160dc850337">nv::cloth::Cloth</a>
, <a class="el" href="classnv_1_1cloth_1_1_fabric.html#8d15c9c15000eeaad9b855cb3ca1d8c8">nv::cloth::Fabric</a>
<li>getNumVirtualParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7b67c8d82763c26d18d52e864137f46f">nv::cloth::Cloth</a>
<li>getNumVirtualParticleWeights()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a08c88cf1855e76452a783c336d1102c">nv::cloth::Cloth</a>
<li>getParticleAccelerations()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#5b6086a1da8379361db57d0d3f6c8655">nv::cloth::Cloth</a>
<li>getPlatform()
: <a class="el" href="classnv_1_1cloth_1_1_factory.html#de78d96c7cd81520176d4bfd4e488b04">nv::cloth::Factory</a>
<li>getPreviousIterationDt()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#b8dee849c57c802ed40234edeaa998be">nv::cloth::Cloth</a>
<li>getPreviousParticles()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#9b22cae129eb4d9677fdea24fa5ec486">nv::cloth::Cloth</a>
<li>getRotation()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#78235f2aa83c32ccf35b6da0e221fe8e">nv::cloth::Cloth</a>
<li>getSelfCollisionDistance()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#064c442c7588405581006d72aa3d88f9">nv::cloth::Cloth</a>
<li>getSelfCollisionStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#209dae86682049c944b9c2ba41aeb0bf">nv::cloth::Cloth</a>
<li>getSeparationConstraints()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2c02b9284fb998282226b0a57209a7d3">nv::cloth::Cloth</a>
<li>getSimulationChunkCount()
: <a class="el" href="classnv_1_1cloth_1_1_solver.html#54e393ba3b9fd5305385e2f57d3ca165">nv::cloth::Solver</a>
<li>getSleepAfterCount()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a3be62e917066f2f29f64320c8286893">nv::cloth::Cloth</a>
<li>getSleepPassCount()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#7a07e21a9b99dd3eab429569c77eac1c">nv::cloth::Cloth</a>
<li>getSleepTestInterval()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#6313c4680c883d3bb6e20632ebde1ab8">nv::cloth::Cloth</a>
<li>getSleepThreshold()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#2bd353debfdb900979395fe8870df1bf">nv::cloth::Cloth</a>
<li>getSolverFrequency()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#a7737f7ba0dfca885cfc1f1a7f651b01">nv::cloth::Cloth</a>
<li>getStiffnessFrequency()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#6476ef704ed1733398ba87093bc8bb22">nv::cloth::Cloth</a>
<li>getTetherConstraintScale()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#********************************">nv::cloth::Cloth</a>
<li>getTetherConstraintStiffness()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#d423f35a4952860552430fea2796ce15">nv::cloth::Cloth</a>
<li>getTetherData()
: <a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#214d9ee202260d53da8ed8058994dc55">nv::cloth::ClothTetherCooker</a>
<li>getTranslation()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#519a015726fbc04a7bcf60afcfe3b0ca">nv::cloth::Cloth</a>
<li>getUserData()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#91e02303afccc55bba87886c1187002b">nv::cloth::Cloth</a>
<li>getWindVelocity()
: <a class="el" href="classnv_1_1cloth_1_1_cloth.html#dc98811170dedd7f79c97a5ad289aeb2">nv::cloth::Cloth</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
