var node0 = ::geograph::nodes::draw_geometry::DrawGeometry()
node0.query_param("skip").value = false

_editor.add_node(node0, -1005.8781512487, -253.72942685234)

var node1 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node1, 170.42428288906, 207.31228816969)

var node2 = ::editorgraph::nodes::mouse_left_down::MouseLeftDown()

_editor.add_node(node2, -480.90368497682, 220.2425714512)

var node3 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node3, -181.51224753801, 140.03218943532)

var node4 = ::blueprint::nodes::number2::Number2()

node4.value.set(-808.99725934326, -564.56584458764)

_editor.add_node(node4, -1159.6426935563, 361.29180270597)

var node5 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node5, -334.49041756175, 163.69321989585)

var node6 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node6, 81.282065413544, -155.41122819078)

var node7 = ::editorgraph::nodes::mouse_left_drag::MouseLeftDrag()

_editor.add_node(node7, -515.83611640464, -156.12857135565)

var node8 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node8, -374.37174255581, -199.28767624986)

var node9 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node9, -216.52454005319, -273.27568489718)

var node10 = ::blueprint::nodes::number2::Number2()

node10.value.set(-808.99725934326, -564.56584458764)

_editor.add_node(node10, -854.95648382704, 359.37544927729)

var node11 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node11, -1020.5353042568, 261.45600377636)

var node12 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node12, 224.39106190058, -467.3419110927)

var node13 = ::editorgraph::nodes::mouse_left_up::MouseLeftUp()

_editor.add_node(node13, 81.814341052888, -458.17698800582)

var node14 = ::blueprint::nodes::list_add::ListAdd()
node14.query_param("unique").value = false

_editor.add_node(node14, -196.61764179788, -555.70659726002)

var node15 = ::geograph::nodes::draw_geometry::DrawGeometry()
node15.query_param("skip").value = false

_editor.add_node(node15, -976.23657026824, -443.59153157259)

var node16 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node16, 3.8856716047919, 58.930217106398)

var node17 = ::blueprint::nodes::store::Store()

node17.var_name = "center"

_editor.add_node(node17, -1010.325228079, 372.21954275194)

var node18 = ::blueprint::nodes::load::Load()

node18.var_name = "center"

_editor.add_node(node18, -330.90865572396, 101.00620272934)

var node19 = ::blueprint::nodes::load::Load()

node19.var_name = "center"

_editor.add_node(node19, -179.97544760976, 65.632874404765)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "center"

_editor.add_node(node20, -1155.7950262309, 233.63861847613)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "center"

_editor.add_node(node21, -1261.6037813499, -402.1916084214)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "center"

_editor.add_node(node22, -494.35843950616, -555.64344122307)

var node23 = ::blueprint::nodes::store::Store()

node23.var_name = "second_pos"

_editor.add_node(node23, -703.58670420164, 369.3060091077)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "second_pos"

_editor.add_node(node24, -181.08981629439, 17.273890029764)

var node25 = ::blueprint::nodes::load::Load()

node25.var_name = "second_pos"

_editor.add_node(node25, -372.76247749715, -253.6844648477)

var node26 = ::blueprint::nodes::load::Load()

node26.var_name = "second_pos"

_editor.add_node(node26, -1155.2762807954, 282.98912811969)

var node27 = ::blueprint::nodes::store::Store()

node27.var_name = "circles"

_editor.add_node(node27, -995.87875591102, 161.70311568819)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "circles"

_editor.add_node(node28, -1145.2513951964, -233.1512765627)

var node29 = ::blueprint::nodes::load::Load()

node29.var_name = "circles"

_editor.add_node(node29, -343.00232596861, -505.49382353648)

var node30 = ::blueprint::nodes::commentary::Commentary()

node30.set_size(617.66668701172, 481.13125610352)
node30.title = "Variates"

_editor.add_node(node30, -933.35925694411, 441.87051090949)

var node31 = ::blueprint::nodes::commentary::Commentary()

node31.set_size(708.79168701172, 401.59167480469)
node31.title = "Draw"

_editor.add_node(node31, -978.51162127472, -178.24493738201)

var node32 = ::blueprint::nodes::commentary::Commentary()

node32.set_size(803.33959960938, 295.6484375)
node32.title = "First Point"

_editor.add_node(node32, -153.53536104565, 277.64134711665)

var node33 = ::blueprint::nodes::commentary::Commentary()

node33.set_size(798.20104980469, 244.79426574707)
node33.title = "Second Point"

_editor.add_node(node33, -208.62446338093, -80.696456911193)

var node34 = ::blueprint::nodes::commentary::Commentary()

node34.set_size(873.21459960938, 361.2151184082)
node34.title = "Finish"

_editor.add_node(node34, -137.58247738184, -394.37729775294)

var node35 = ::blueprint::nodes::subgraph::Subgraph()
node35.load_from_file(_editor, "right_down_remove.ves")

_editor.add_node(node35, -316.11103149702, -846.1862329551)

var node36 = ::blueprint::nodes::load::Load()

node36.var_name = "circles"

_editor.add_node(node36, -512.71436341633, -819.84368495513)

var node37 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node37, 87.396533927911, -546.33030541131)

var node38 = ::blueprint::nodes::load::Load()

node38.var_name = "center"

_editor.add_node(node38, -59.900329243535, -593.54938718939)

var node39 = ::blueprint::nodes::load::Load()

node39.var_name = "second_pos"

_editor.add_node(node39, -61.014697928161, -641.90837156439)

var node40 = ::blueprint::nodes::subgraph::Subgraph()
node40.load_from_file(_editor, "left_down_select.ves")

_editor.add_node(node40, -203.54943924396, 364.28865593501)

var node41 = ::blueprint::nodes::load::Load()

node41.var_name = "circles"

_editor.add_node(node41, -400.37335831855, 412.91042365333)

var node42 = ::blueprint::nodes::array::Array()
node42.query_param("serialize").value = false

_editor.add_node(node42, -846.24051131974, 159.66898755097)

var node43 = ::geograph::nodes::draw_geometry::DrawGeometry()
node43.query_param("skip").value = false

_editor.add_node(node43, -694.34141133436, -334.54805156567)

var node44 = ::blueprint::nodes::number3::Number3()

node44.value.set(0, 0.54259234666824, 0)

_editor.add_node(node44, -844.90937372538, -448.95042814492)

var node45 = ::blueprint::nodes::store::Store()

node45.var_name = "selected"

_editor.add_node(node45, -702.58726071236, 167.13324319384)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "selected"

_editor.add_node(node46, -841.22086307708, -366.97406964622)

var node47 = ::blueprint::nodes::load::Load()

node47.var_name = "selected"

_editor.add_node(node47, -399.8359838042, 365.37571670012)

var node48 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node48, -70.467302937154, -234.100393248)

var node49 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node49, -63.648935693575, -511.1808146994)

var node50 = ::blueprint::nodes::compare::Compare()

node50.cmp = "greater"

_editor.add_node(node50, -1002.8771403627, 60.999997596507)

var node51 = ::blueprint::nodes::input::Input()

node51.var_name = "circles"
node51.var_type = "array"

_editor.add_node(node51, -1147.5590407921, 166.6028180103)

var node52 = ::geograph::nodes::circle::Circle()
node52.query_param("x").value = -165.88253
node52.query_param("y").value = -83.74652
node52.query_param("r").value = 72.942436218262
node52.query_param("fill").value = true
node52.query_param("color").value.set(0.75072020292282, 0.39516466856003, 0)

_editor.add_node(node52, -344.13505339819, -642.99433180124)

var node53 = ::blueprint::nodes::length::Length()

_editor.add_node(node53, -874.01364177082, 271.98916302577)

var node54 = ::blueprint::nodes::store::Store()

node54.var_name = "radius"

_editor.add_node(node54, -731.31497602476, 260.32270655403)

var node55 = ::geograph::nodes::circle::Circle()
node55.query_param("x").value = 0
node55.query_param("y").value = 0
node55.query_param("r").value = 100
node55.query_param("fill").value = false
node55.query_param("color").value.set(0.611636698246, 0.34497001767159, 0.36719223856926)

_editor.add_node(node55, -1114.9463536203, -488.61345930128)

var node56 = ::blueprint::nodes::load::Load()

node56.var_name = "radius"

_editor.add_node(node56, -1258.9977076621, -452.53320071055)

var node57 = ::blueprint::nodes::load::Load()

node57.var_name = "radius"

_editor.add_node(node57, -1146.0557121191, 102.09720010675)

var node58 = ::blueprint::nodes::number::Number()

node58.value = 0.1

_editor.add_node(node58, -1140.2705604089, 38.460820581067)

var node59 = ::blueprint::nodes::load::Load()

node59.var_name = "radius"

_editor.add_node(node59, -493.25480502131, -605.3481126373)

var node60 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node60, -850.13643156134, -283.71395519527)

var node61 = ::blueprint::nodes::store::Store()

node61.var_name = "selected"

_editor.add_node(node61, -10.837202959183, 366.32659412273)

var node62 = ::blueprint::nodes::load::Load()

node62.var_name = "selected"

_editor.add_node(node62, -216.72815448122, -207.74477101382)

var node63 = ::blueprint::nodes::subgraph::Subgraph()
node63.load_from_file(_editor, "translate_selected.ves")

_editor.add_node(node63, -321.34830031718, -989.43558287066)

var node64 = ::blueprint::nodes::load::Load()

node64.var_name = "selected"

_editor.add_node(node64, -510.07341624224, -968.38043487706)

var node65 = ::blueprint::nodes::load::Load()

node65.var_name = "second_pos"

_editor.add_node(node65, -510.38435229688, -1013.2907768396)

var node66 = ::blueprint::nodes::a_n_d::AND()

_editor.add_node(node66, -866.0598265558, 31.957855783447)

var node67 = ::blueprint::nodes::load::Load()

node67.var_name = "selected"

_editor.add_node(node67, -1138.4096328758, -27.32820315863)

var node68 = ::blueprint::nodes::n_o_t::NOT()

_editor.add_node(node68, -1008.3401510799, -26.70710518982)

var node69 = ::blueprint::nodes::store::Store()

node69.var_name = "is_draw_mode"

_editor.add_node(node69, -730.56720135799, 31.530513781107)

var node70 = ::blueprint::nodes::load::Load()

node70.var_name = "is_draw_mode"

_editor.add_node(node70, -196.86268461873, -478.54853414741)

var node71 = ::blueprint::nodes::load::Load()

node71.var_name = "is_draw_mode"

_editor.add_node(node71, -999.13006596741, -341.9776836983)

var node72 = ::blueprint::nodes::load::Load()

node72.var_name = "selected"

_editor.add_node(node72, -512.00450748577, -865.94502488666)

var node73 = ::blueprint::nodes::input::Input()

node73.var_name = "cam_mat"
node73.var_type = "mat4"

_editor.add_node(node73, -1087.8448031298, -88.898859506153)

var node74 = ::blueprint::nodes::store::Store()

node74.var_name = "cam_mat"

_editor.add_node(node74, -950.65473158251, -89.725301751763)

var node75 = ::blueprint::nodes::load::Load()

node75.var_name = "cam_mat"

_editor.add_node(node75, -400.68603212971, 321.17814901052)

var node76 = ::blueprint::nodes::load::Load()

node76.var_name = "cam_mat"

_editor.add_node(node76, -515.24123778167, -218.10250531073)

var node77 = ::blueprint::nodes::load::Load()

node77.var_name = "cam_mat"

_editor.add_node(node77, -1109.6774977127, -625.18111321906)

var node78 = ::blueprint::nodes::load::Load()

node78.var_name = "cam_mat"

_editor.add_node(node78, -841.77892521084, -524.80613200193)

var node79 = ::blueprint::nodes::load::Load()

node79.var_name = "cam_mat"

_editor.add_node(node79, -1146.0283281329, -278.07597230097)

var node80 = ::blueprint::nodes::load::Load()

node80.var_name = "cam_mat"

_editor.add_node(node80, -479.05461478706, 137.0592371243)

var node81 = ::blueprint::nodes::load::Load()

node81.var_name = "cam_mat"

_editor.add_node(node81, -510.8442977127, -911.99531321906)

Blueprint.connect(node73, "var", node74, "var")
Blueprint.connect(node74, "var", node81, "var")
Blueprint.connect(node74, "var", node80, "var")
Blueprint.connect(node74, "var", node79, "var")
Blueprint.connect(node74, "var", node78, "var")
Blueprint.connect(node74, "var", node77, "var")
Blueprint.connect(node74, "var", node76, "var")
Blueprint.connect(node74, "var", node75, "var")
Blueprint.connect(node51, "var", node27, "var")
Blueprint.connect(node27, "var", node41, "var")
Blueprint.connect(node27, "var", node36, "var")
Blueprint.connect(node27, "var", node29, "var")
Blueprint.connect(node27, "var", node28, "var")
Blueprint.connect(node28, "var", node0, "geos")
Blueprint.connect(node79, "var", node0, "cam_mat")
Blueprint.connect(node42, "all", node45, "var")
Blueprint.connect(node45, "var", node72, "var")
Blueprint.connect(node36, "var", node35, "geos")
Blueprint.connect(node72, "var", node35, "selected")
Blueprint.connect(node81, "var", node35, "cam_mat")
Blueprint.connect(node45, "var", node64, "var")
Blueprint.connect(node45, "var", node47, "var")
Blueprint.connect(node41, "var", node40, "geos")
Blueprint.connect(node47, "var", node40, "selected")
Blueprint.connect(node75, "var", node40, "cam_mat")
Blueprint.connect(node40, "success", node61, "var")
Blueprint.connect(node61, "var", node67, "var")
Blueprint.connect(node67, "var", node68, "in")
Blueprint.connect(node61, "var", node62, "var")
Blueprint.connect(node62, "var", node48, "cond")
Blueprint.connect(node9, "next", node48, "false")
Blueprint.connect(node45, "var", node46, "var")
Blueprint.connect(node13, "event", node12, "event")
Blueprint.connect(node37, "next", node12, "action")
Blueprint.connect(node10, "v2", node23, "var")
Blueprint.connect(node23, "var", node65, "var")
Blueprint.connect(node64, "var", node63, "selected")
Blueprint.connect(node65, "var", node63, "last_pos")
Blueprint.connect(node81, "var", node63, "cam_mat")
Blueprint.connect(node23, "var", node39, "var")
Blueprint.connect(node23, "var", node26, "var")
Blueprint.connect(node23, "var", node25, "var")
Blueprint.connect(node23, "var", node24, "var")
Blueprint.connect(node7, "pos", node8, "screen")
Blueprint.connect(node76, "var", node8, "cam_mat")
Blueprint.connect(node8, "world", node9, "src")
Blueprint.connect(node25, "var", node9, "dst")
Blueprint.connect(node7, "event", node6, "event")
Blueprint.connect(node48, "next", node6, "action")
Blueprint.connect(node4, "v2", node17, "var")
Blueprint.connect(node17, "var", node38, "var")
Blueprint.connect(node17, "var", node22, "var")
Blueprint.connect(node17, "var", node21, "var")
Blueprint.connect(node17, "var", node20, "var")
Blueprint.connect(node26, "var", node11, "a")
Blueprint.connect(node20, "var", node11, "b")
Blueprint.connect(node11, "v", node53, "v")
Blueprint.connect(node53, "v", node54, "var")
Blueprint.connect(node54, "var", node59, "var")
Blueprint.connect(node22, "var", node52, "center")
Blueprint.connect(node59, "var", node52, "raduis")
Blueprint.connect(node29, "var", node14, "list")
Blueprint.connect(node52, "geo", node14, "add")
Blueprint.connect(node54, "var", node57, "var")
Blueprint.connect(node57, "var", node50, "a")
Blueprint.connect(node58, "v", node50, "b")
Blueprint.connect(node50, "out", node66, "a")
Blueprint.connect(node68, "out", node66, "b")
Blueprint.connect(node66, "out", node69, "var")
Blueprint.connect(node69, "var", node71, "var")
Blueprint.connect(node0, "next", node60, "prev")
Blueprint.connect(node71, "var", node60, "cond")
Blueprint.connect(node15, "next", node60, "true")
Blueprint.connect(node60, "next", node43, "prev")
Blueprint.connect(node46, "var", node43, "geos")
Blueprint.connect(node44, "v3", node43, "color")
Blueprint.connect(node78, "var", node43, "cam_mat")
Blueprint.connect(node69, "var", node70, "var")
Blueprint.connect(node70, "var", node49, "cond")
Blueprint.connect(node14, "next", node49, "true")
Blueprint.connect(node49, "next", node37, "prev")
Blueprint.connect(node38, "var", node37, "src")
Blueprint.connect(node39, "var", node37, "dst")
Blueprint.connect(node54, "var", node56, "var")
Blueprint.connect(node21, "var", node55, "center")
Blueprint.connect(node56, "var", node55, "raduis")
Blueprint.connect(node55, "geo", node15, "geos")
Blueprint.connect(node77, "var", node15, "cam_mat")
Blueprint.connect(node17, "var", node19, "var")
Blueprint.connect(node17, "var", node18, "var")
Blueprint.connect(node2, "pos", node5, "screen")
Blueprint.connect(node80, "var", node5, "cam_mat")
Blueprint.connect(node5, "world", node3, "src")
Blueprint.connect(node18, "var", node3, "dst")
Blueprint.connect(node3, "next", node16, "prev")
Blueprint.connect(node19, "var", node16, "src")
Blueprint.connect(node24, "var", node16, "dst")
Blueprint.connect(node40, "next", node1, "prev")
Blueprint.connect(node2, "event", node1, "event")
Blueprint.connect(node16, "next", node1, "action")
