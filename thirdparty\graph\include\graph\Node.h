#pragma once

#include <SM_Vector.h>

#include <memory>
#include <vector>
#include <string>
#include <map>

namespace graph
{

class Node
{
public:
	Node() {}

	void SetId(int id) { m_id = id; }
	int GetId() const { return m_id; }

	void AddConnect(const std::shared_ptr<Node>& conn);
	auto& GetConnects() const { return m_conns; }
	void ClearConnections() { m_conns.clear(); }

	auto& GetPos() const { return m_pos; }
	void SetPos(const sm::vec2& pos) { m_pos = pos; }

	// Additional methods needed by wrap_Graph.cpp
	const std::string& GetName() const { return m_name; }
	void SetName(const std::string& name) { m_name = name; }

	bool HasName() const { return !m_name.empty(); }

	// Component system for storing arbitrary data
	template<typename T>
	void SetComponent(const std::string& key, const T& value);

	template<typename T>
	T GetComponent(const std::string& key) const;

	bool HasComponent(const std::string& key) const;

	// Value system for storing string values
	void SetValue(const std::string& value) { m_value = value; }
	const std::string& GetValue() const { return m_value; }

private:
	int m_id = 0;
	std::string m_name;
	std::string m_value;

	std::vector<std::shared_ptr<Node>> m_conns;
	std::map<std::string, std::string> m_components; // Simple string-based component storage

	sm::vec2 m_pos;

}; // Node

}