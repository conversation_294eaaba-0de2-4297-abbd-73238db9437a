<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::StridedData Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_strided_data.html">StridedData</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::StridedData Struct Reference</h1><!-- doxytag: class="nv::cloth::StridedData" --><code>#include &lt;<a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a>&gt;</code>
<p>
<div class="dynheader">
Inheritance diagram for nv::cloth::StridedData:</div>
<div class="dynsection">

<p><center><img src="structnv_1_1cloth_1_1_strided_data.png" usemap="#nv::cloth::StridedData_map" border="0" alt=""></center>
<map name="nv::cloth::StridedData_map">
<area href="structnv_1_1cloth_1_1_bounded_data.html" alt="nv::cloth::BoundedData" shape="rect" coords="0,56,141,80">
</map>
</div>

<p>
<a href="structnv_1_1cloth_1_1_strided_data-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memTemplParams" nowrap colspan="2">template&lt;typename TDataType &gt; </td></tr>
<tr><td class="memTemplItemLeft" nowrap align="right" valign="top">PX_INLINE const TDataType &amp;&nbsp;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_strided_data.html#127ee8d050b77cd58ccece6eb3495ccb">at</a> (physx::PxU32 idx) const </td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_strided_data.html#06829ec148078b342bcf4bcdd11ff035">StridedData</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">const void *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">data</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxU32&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba">stride</a></td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">The offset in bytes between consecutive samples in the data.  <a href="#fa7d89f91e82b269c40ddaffb726e3ba"></a><br></td></tr>
</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="06829ec148078b342bcf4bcdd11ff035"></a><!-- doxytag: member="nv::cloth::StridedData::StridedData" ref="06829ec148078b342bcf4bcdd11ff035" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::StridedData::StridedData           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="127ee8d050b77cd58ccece6eb3495ccb"></a><!-- doxytag: member="nv::cloth::StridedData::at" ref="127ee8d050b77cd58ccece6eb3495ccb" args="(physx::PxU32 idx) const " -->
<div class="memitem">
<div class="memproto">
<div class="memtemplate">
template&lt;typename TDataType &gt; </div>
      <table class="memname">
        <tr>
          <td class="memname">PX_INLINE const TDataType&amp; nv::cloth::StridedData::at           </td>
          <td>(</td>
          <td class="paramtype">physx::PxU32&nbsp;</td>
          <td class="paramname"> <em>idx</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [inline]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="73e438c8aa4c46710a7f5933f131f5e1"></a><!-- doxytag: member="nv::cloth::StridedData::data" ref="73e438c8aa4c46710a7f5933f131f5e1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">const void* <a class="el" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">nv::cloth::StridedData::data</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="fa7d89f91e82b269c40ddaffb726e3ba"></a><!-- doxytag: member="nv::cloth::StridedData::stride" ref="fa7d89f91e82b269c40ddaffb726e3ba" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">physx::PxU32 <a class="el" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba">nv::cloth::StridedData::stride</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
The offset in bytes between consecutive samples in the data. 
<p>
<b>Default:</b> 0 
</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
