FUNCTION(SetOutputPaths 
	OUTPUT_EXE_DIR 
	OUTPUT_DLL_DIR 
	OUTPUT_LIB_DIR)

	SET(EXE_DIR ${OUTPUT_EXE_DIR})
	SET(DLL_DIR ${OUTPUT_DLL_DIR})
	SET(LIB_DIR ${OUTPUT_LIB_DIR})
	
	# Override the default output directories for all configurations.
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_CHECKED ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_CHECKED ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_CHECKED ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_PROFILE ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_PROFILE ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_PROFILE ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY ${LIB_DIR})
	
ENDFUNCTION(SetOutputPaths)

FUNCTION(SetExeOutputPath OUTPUT_EXE_DIR)

	SET(EXE_DIR ${OUTPUT_EXE_DIR})
	
	# Override the default output directories for all configurations.
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_CHECKED ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_PROFILE ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${EXE_DIR} PARENT_SCOPE)

ENDFUNCTION(SetExeOutputPath)

FUNCTION(SetDllOutputPath OUTPUT_DLL_DIR)

	SET(DLL_DIR ${OUTPUT_DLL_DIR})
	
	# Override the default output directories for all configurations.
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_CHECKED ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_PROFILE ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${DLL_DIR} PARENT_SCOPE)

ENDFUNCTION(SetDllOutputPath)

FUNCTION(SetLibOutputPath OUTPUT_LIB_DIR)

	SET(LIB_DIR ${OUTPUT_LIB_DIR})
	
	# Override the default output directories for all configurations.
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${LIB_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_CHECKED ${LIB_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_PROFILE ${LIB_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY ${LIB_DIR})

ENDFUNCTION(SetLibOutputPath)

FUNCTION(SetSingleOutputPath OUTPUT_ALL_DIR)

	SET(EXE_DIR ${OUTPUT_ALL_DIR})
	SET(DLL_DIR ${OUTPUT_ALL_DIR})
	SET(LIB_DIR ${OUTPUT_ALL_DIR})
	
	# Override the default output directories for all configurations.
	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_DEBUG ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_DEBUG ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_DEBUG ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_CHECKED ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_CHECKED ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_CHECKED ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_PROFILE ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_PROFILE ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_PROFILE ${LIB_DIR} PARENT_SCOPE)

	SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_RELEASE ${EXE_DIR} PARENT_SCOPE)
	SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_RELEASE ${DLL_DIR} PARENT_SCOPE)
	SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_RELEASE ${LIB_DIR} PARENT_SCOPE)
	
	SET(CMAKE_COMPILE_PDB_OUTPUT_DIRECTORY ${LIB_DIR})
	
ENDFUNCTION(SetSingleOutputPath)