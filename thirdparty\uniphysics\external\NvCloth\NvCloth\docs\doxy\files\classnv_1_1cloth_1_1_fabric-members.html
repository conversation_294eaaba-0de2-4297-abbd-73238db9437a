<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Fabric Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#dbabafe4f0954eb5cea92463de89dfa0">decRefCount</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [inline]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#2f603146b3133b7867cd9d008ab68ff3">Fabric</a>(const Fabric &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#bb5cffce0412bc67bafbb1c47b56886e">Fabric</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [inline, protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#e6ab4bb76335c9af1a67435eb2520d62">getFactory</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#057b35a8f4b7cce31a0be2eb0704e52d">getNumIndices</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8dd6c3990522e16832311a2b04b17619">getNumParticles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#4d9348de98c1c00498709dc591fa27ba">getNumPhases</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#52c968ff1b808ab00d994db25bc01d83">getNumRestvalues</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#d28fcf11b0c9ebb20325cafb5dbcde4d">getNumSets</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#24d3ef1c25d42d981a12f5b7a96114e4">getNumStiffnessValues</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#aa6b9b09786b98e3be8cc9f362c1f09d">getNumTethers</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8d15c9c15000eeaad9b855cb3ca1d8c8">getNumTriangles</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#7566de18b6640949fcce3839238fb9ce">incRefCount</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [inline]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">mRefCount</a></td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#3d850841d4168a5827731f3fa4cc07c6">operator=</a>(const Fabric &amp;)</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [protected]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8343cbc315361fc0ebb1322009076c86">scaleRestvalues</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#b884bf893050c00ec8bacb25a5dd76a0">scaleTetherLengths</a>(float)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html#8d3748f793f73d1cc5547ee99d052038">~Fabric</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_fabric.html">nv::cloth::Fabric</a></td><td><code> [inline, protected, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
