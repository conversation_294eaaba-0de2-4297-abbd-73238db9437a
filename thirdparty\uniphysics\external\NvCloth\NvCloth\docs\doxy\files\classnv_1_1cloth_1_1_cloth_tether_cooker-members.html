<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothTetherCooker Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#2bc514fcf01c15422f552f85756295d9">cook</a>(const ClothMeshDesc &amp;desc)=0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#e1670477f35e78f3ca1038a6093c1ac1">getCookerStatus</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#683c0c2fbe6ba9127e55b20864b04035">getNbTethersPerParticle</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#214d9ee202260d53da8ed8058994dc55">getTetherData</a>(physx::PxU32 *userTetherAnchors, physx::PxReal *userTetherLengths) const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html#fde8e2c9affc2ddf143feaab9c6a6a88">~ClothTetherCooker</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_tether_cooker.html">nv::cloth::ClothTetherCooker</a></td><td><code> [inline, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
