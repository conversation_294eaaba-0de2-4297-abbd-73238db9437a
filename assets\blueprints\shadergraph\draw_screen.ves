var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("tri_strip")
node0.render_state = { "stencil_test" : false, "rasterization" : "fill", "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : true }
node0.skip = false

_editor.add_node(node0, 385.43128084919, -78.250103479437)

var node1 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node1.type = "quad"
node1.layout = [ "position", "texture" ]
node1.shape_params = {  }

_editor.add_node(node1, 226.77067435382, -96.834332911197)

var node2 = ::shadergraph::nodes::shader_gen::ShaderGen()

_editor.add_node(node2, 226.09705577767, 26.799489927686)

var node3 = ::rendergraph::nodes::shader_code::ShaderCode()
node3.init_filepath("../../shaders/screen_vert.glsl")

_editor.add_node(node3, 84.323947015156, 54.297635222449)

var node4 = ::shadergraph::nodes::frag_color::FragColor()

_editor.add_node(node4, 86.868512974029, -37.27682119093)

var node5 = ::blueprint::nodes::input::Input()

node5.var_name = "rgb"
node5.var_type = "num3"

_editor.add_node(node5, -60.761200152837, -39.48162640132)

Blueprint.connect(node5, "var", node4, "rgb")
Blueprint.connect(node3, "code", node2, "vs")
Blueprint.connect(node4, "next", node2, "fs")
Blueprint.connect(node2, "shader", node0, "shader")
Blueprint.connect(node1, "out", node0, "va")
