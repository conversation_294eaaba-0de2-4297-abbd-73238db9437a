C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\AST.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\AST.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\BuildInFuncs.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\BuildInFuncs.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Declaration.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Declaration.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\DumpAST.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\DumpAST.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\EvalAST.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\EvalAST.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\ExprCheck.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\ExprCheck.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Expression.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Expression.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\GenCode.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\GenCode.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Parser.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Parser.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Statement.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Statement.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\StringPool.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\StringPool.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Symbol.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Symbol.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Tokenizer.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Tokenizer.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Type.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Type.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Utility.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Utility.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\source\Variant.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\cslang\platform\msvc\projects\x64\Release\cslang\Variant.obj
