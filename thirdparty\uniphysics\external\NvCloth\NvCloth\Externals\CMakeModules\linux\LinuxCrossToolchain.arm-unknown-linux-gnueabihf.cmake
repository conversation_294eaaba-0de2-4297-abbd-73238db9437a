IF(NOT $ENV{LINUX_ROOT} EQUAL "")
	SET(CMAKE_SYSTEM_NAME Linux)

	# FIXME: fix Linux toolchains to support architectures
	SET(LINUX_ROOT $ENV{UE_SDKS_ROOT}/HostWin64/Linux_x64/arm-unknown-linux-gnueabihf_v5_clang-3.5.0-ld-2.23.1-glibc-2.13/toolchain)
	STRING(REGEX REPLACE "\\\\" "/" LINUX_ROOT ${LINUX_ROOT})

	message (STATUS "LINUX_ROOT is '${LINUX_ROOT}'")
	SET(ARCHITECTURE_TRIPLE arm-unknown-linux-gnueabihf)

	SET(CMAKE_CROSSCOMPILING TRUE)
	SET(CMAKE_SYSTEM_NAME Linux)
	SET(CMAKE_SYSTEM_VERSION 1)

	# sysroot
	SET(CMAKE_SYSROOT ${LINUX_ROOT})

	SET(CMAKE_LIBRARY_ARCHITECTURE ${ARCHITECTURE_TRIPLE})

	# specify the cross compiler
	SET(CMAKE_C_COMPILER   ${CMAKE_SYSROOT}/bin/clang.exe)
	SET(CMAKE_C_COMPILER_TARGET ${ARCHITECTURE_TRIPLE})
	SET(CMAKE_C_FLAGS   "-target ${ARCHITECTURE_TRIPLE}  --sysroot ${LINUX_ROOT} ")

	SET(CMAKE_CXX_COMPILER   ${CMAKE_SYSROOT}/bin/clang++.exe)
	SET(CMAKE_CXX_COMPILER_TARGET ${ARCHITECTURE_TRIPLE})
	SET(CMAKE_CXX_FLAGS   "-target ${ARCHITECTURE_TRIPLE} --sysroot ${LINUX_ROOT} ")

	SET(CMAKE_FIND_ROOT_PATH  ${LINUX_ROOT})
	#set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM ONLY)	# hoping to force it to use ar
	#set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
	#set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
ELSE()
	MESSAGE("LINUX_ROOT environment variable not defined!")
ENDIF()


