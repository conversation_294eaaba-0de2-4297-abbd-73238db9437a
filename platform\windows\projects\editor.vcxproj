<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{CD2B2319-A388-4758-91D1-B32910D6DA22}</ProjectGuid>
    <RootNamespace>editor</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>editor</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IntDir>$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <IntDir>$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_WINDOWS;SPVGENTWO_LOGGING=1;SPVGENTWO_REPLACE_PLACEMENTNEW=1;SPVGENTWO_REPLACE_TRAITS=1;SPVGENTWO_BUILD_EXAMPLES=1;SPVGENTWO_BUILD_DISASSEMBLER=1;SPVGENTWO_BUILD_REFLECT=1;CMAKE_INTDIR="Debug";_DEBUG;_CONSOLE;NO_BOOST;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../include;../../../src;../../../thirdparty/glfw/include;../../../thirdparty/gl/include;../../../thirdparty/vessel/src/include</AdditionalIncludeDirectories>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>vulkan-1.lib;dxcompiler.lib;glslangd.lib;OSDependentd.lib;OGLCompilerd.lib;GenericCodeGend.lib;MachineIndependentd.lib;SPIRVd.lib;LLVMDxcSupportd.lib;glfw3d.lib;freetyped.lib;jpeg-staticd.lib;zlibstaticd.lib;libpng16_staticd.lib;assimp-vc142-mt.lib;libfbxsdk.lib;box2dd.lib;tiffd.lib;Ws2_32.lib;TKPrim.lib;TKernel.lib;TKMath.lib;TKTopAlgo.lib;TKBRep.lib;TKG3d.lib;TKG2d.lib;TKGeomBase.lib;TKMesh.lib;TKGeomAlgo.lib;TKFillet.lib;TKBool.lib;TKShHealing.lib;TKBO.lib;TKOffset.lib;OGDFd.lib;COINd.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <AdditionalLibraryDirectories>../lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;NO_BOOST;_CRT_SECURE_NO_WARNINGS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../include;../../../src;../../../thirdparty/glfw/include;../../../thirdparty/gl/include;../../../thirdparty/vessel/src/include</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>../lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>vulkan-1.lib;dxcompiler.lib;glslang.lib;OSDependent.lib;OGLCompiler.lib;GenericCodeGen.lib;MachineIndependent.lib;SPIRV.lib;LLVMDxcSupport.lib;glfw3.lib;freetype.lib;jpeg-static.lib;zlibstatic.lib;libpng16_static.lib;assimp-vc142-mt.lib;libfbxsdk.lib;box2d.lib;tiff.lib;Ws2_32.lib;TKPrim.lib;TKernel.lib;TKMath.lib;TKTopAlgo.lib;TKBRep.lib;TKG3d.lib;TKG2d.lib;TKGeomBase.lib;TKMesh.lib;TKGeomAlgo.lib;TKFillet.lib;TKBool.lib;TKShHealing.lib;TKBO.lib;TKOffset.lib;OGDF.lib;COIN.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\thirdparty\brepdb\platform\msvc\projects\brepdb.vcxproj">
      <Project>{d224df61-1af4-4049-92e3-0219e81a8f4f}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\brepom\platform\msvc\projects\brepom.vcxproj">
      <Project>{20ab7e71-accc-4de5-8ae6-42609fb066e2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\brepvm\platform\msvc\projects\brepvm.vcxproj">
      <Project>{10522ff2-fa95-459b-889c-e0c3a975e083}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\cslang\platform\msvc\projects\cslang.vcxproj">
      <Project>{e034b5be-6f9a-4bb6-8f85-7450431fd59a}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\dtex\platform\msvc\projects\dtex_new.vcxproj">
      <Project>{777baf91-dc51-4eef-8f46-6502630063a2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\easygui\platform\msvc\projects\easygui_new.vcxproj">
      <Project>{5130e358-9e8f-40a1-99c0-41f4feea5938}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\easyvm\platform\msvc\projects\easyvm.vcxproj">
      <Project>{cec8c868-0394-424c-a11c-f825d9ee17d0}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\geoshape\platform\msvc\projects\geoshape_new.vcxproj">
      <Project>{c1f3e970-2908-47e6-b884-f56a9926f313}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\graph\platform\msvc\projects\graph.vcxproj">
      <Project>{f4d2da89-be16-47e0-8a3a-ae1dad241fab}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\guard\platform\msvc\projects\guard_new.vcxproj">
      <Project>{c6ceb0ad-1d44-481f-8f05-f4eeef942cc8}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\halfedge\platform\msvc\projects\halfedge_new.vcxproj">
      <Project>{de81a903-bd00-4bc4-bbc5-f48dae4738f8}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\heightfield\platform\msvc\projects\heightfield_new.vcxproj">
      <Project>{9e60f2da-ccb3-4b66-984d-0bf28a862acf}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\lexer\platform\msvc\projects\lexer.vcxproj">
      <Project>{7719a1d1-af73-4e1d-bd99-e9b706195639}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\model\platform\msvc\projects\model_new.vcxproj">
      <Project>{248a1e4a-87eb-45cf-a10f-bab452c0098f}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\nurbs\platform\msvc\projects\nurbs.vcxproj">
      <Project>{2bf4d705-ec5e-4503-b8df-f53d5a2e1dc2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\objcomp\platform\msvc\projects\objcomp.vcxproj">
      <Project>{d09d6f32-cc62-4d45-8fe1-9e7242d4ba90}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\polymesh3\platform\msvc\projects\polymesh3_new.vcxproj">
      <Project>{aec6177d-1ac8-4db1-8b20-07cabbd69fb8}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\primitive\platform\msvc\projects\primitive_new.vcxproj">
      <Project>{58617bd0-9ac3-4d6c-bf12-3c9c7cb79946}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\shaderlink\platform\msvc\projects\shaderlink.vcxproj">
      <Project>{8013df52-454d-4f3b-b48b-88426f55bff5}</Project>
    </ProjectReference>

    <ProjectReference Include="..\..\..\thirdparty\shadertrans\platform\msvc\projects\shadertrans_new.vcxproj">
      <Project>{0af51930-759c-4389-8420-2bf6cb10beff}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\tessellation\platform\msvc\projects\tessellation_new.vcxproj">
      <Project>{a9af029b-59cb-45ad-b4fb-5cf528d5e5fa}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\uniphysics\platform\msvc\projects\uniphysics.vcxproj">
      <Project>{a94bc06e-93b2-4f30-ab4e-7d677a1d5b54}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\unirender\platform\msvc\projects\unirender_new.vcxproj">
      <Project>{637a0e9c-1867-459d-8611-65a064e3b2c2}</Project>
    </ProjectReference>
    <ProjectReference Include="..\..\..\thirdparty\vessel\build\msvc\projects\vessel.vcxproj">
      <Project>{956bf94f-151c-42a5-a699-16949a4c8a54}</Project>
    </ProjectReference>
    <ProjectReference Include="ds.vcxproj">
      <Project>{62718ca5-428a-41c6-85f3-40631f303ebe}</Project>
    </ProjectReference>
    <ProjectReference Include="etcpack.vcxproj">
      <Project>{c1f6db5e-e846-4fc9-bd3f-cda684d1bf60}</Project>
    </ProjectReference>
    <ProjectReference Include="fs.vcxproj">
      <Project>{646199d5-0810-4721-99e4-c0f6fab9408c}</Project>
    </ProjectReference>
    <ProjectReference Include="gimg.vcxproj">
      <Project>{db218925-2277-46f1-9039-1451603ee501}</Project>
    </ProjectReference>
    <ProjectReference Include="gl3w.vcxproj">
      <Project>{db280160-0076-49c1-9bc6-2dff820bd61c}</Project>
    </ProjectReference>
    <ProjectReference Include="gtxt.vcxproj">
      <Project>{1d4a131a-d03b-4215-bd90-34a2fc3ab261}</Project>
    </ProjectReference>
    <ProjectReference Include="logger.vcxproj">
      <Project>{b849151f-f797-4373-b424-847d13ade4f5}</Project>
    </ProjectReference>
    <ProjectReference Include="sm.vcxproj">
      <Project>{381efbed-a988-4a2d-82c9-07900f0ae7e8}</Project>
    </ProjectReference>
    <ProjectReference Include="tantien.vcxproj">
      <Project>{8d617a32-04c2-4b1c-b932-60eab5ccba04}</Project>
    </ProjectReference>
    <ProjectReference Include="texpack.vcxproj">
      <Project>{fb4e2ba0-1ad1-4e00-9e01-131731641918}</Project>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\src\editor\main.cpp" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>