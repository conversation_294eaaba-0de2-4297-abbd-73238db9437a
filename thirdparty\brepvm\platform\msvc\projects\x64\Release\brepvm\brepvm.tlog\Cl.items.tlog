C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\Bytecodes.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\Bytecodes.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\CodesBuilder.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\CodesBuilder.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\Compiler.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\Compiler.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\Decompiler.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\Decompiler.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\geo_opcodes.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\geo_opcodes.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\math_opcodes.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\math_opcodes.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\OpFieldMap.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\OpFieldMap.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\Optimizer.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\Optimizer.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\stl_opcodes.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\stl_opcodes.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\ValueCache.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\ValueCache.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\VM.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\VM.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\source\VMHelper.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\brepvm\platform\msvc\projects\x64\Release\brepvm\VMHelper.obj
