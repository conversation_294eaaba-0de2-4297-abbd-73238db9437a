<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members - Variables</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_func.html"><span>Functions</span></a></li>
      <li class="current"><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li><a href="functions_eval.html"><span>Enumerator</span></a></li>
      <li><a href="functions_rela.html"><span>Related&nbsp;Functions</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="#index_c"><span>c</span></a></li>
      <li><a href="#index_d"><span>d</span></a></li>
      <li><a href="#index_f"><span>f</span></a></li>
      <li><a href="#index_i"><span>i</span></a></li>
      <li><a href="#index_m"><span>m</span></a></li>
      <li><a href="#index_n"><span>n</span></a></li>
      <li><a href="#index_p"><span>p</span></a></li>
      <li><a href="#index_q"><span>q</span></a></li>
      <li><a href="#index_r"><span>r</span></a></li>
      <li><a href="#index_s"><span>s</span></a></li>
      <li><a href="#index_t"><span>t</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<h3><a class="anchor" name="index_c">- c -</a></h3><ul>
<li>count
: <a class="el" href="structnv_1_1cloth_1_1_bounded_data.html#e13bda6410e1f7a793d23c3492e1507b">nv::cloth::BoundedData</a>
</ul>
<h3><a class="anchor" name="index_d">- d -</a></h3><ul>
<li>data
: <a class="el" href="structnv_1_1cloth_1_1_strided_data.html#73e438c8aa4c46710a7f5933f131f5e1">nv::cloth::StridedData</a>
</ul>
<h3><a class="anchor" name="index_f">- f -</a></h3><ul>
<li>flags
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a">nv::cloth::ClothMeshDesc</a>
</ul>
<h3><a class="anchor" name="index_i">- i -</a></h3><ul>
<li>indices
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba">nv::cloth::ClothFabricDesc</a>
<li>invMasses
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de">nv::cloth::ClothMeshDesc</a>
</ul>
<h3><a class="anchor" name="index_m">- m -</a></h3><ul>
<li>mAnchors
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">nv::cloth::CookedData</a>
<li>mBuffer
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#c51319ddd95590ff62430e3f74c1ecc2">nv::cloth::GpuParticles</a>
<li>mCallback
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">nv::cloth::NvClothProfileScoped</a>
<li>mCompressionLimit
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">nv::cloth::PhaseConfig</a>
<li>mContextId
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">nv::cloth::NvClothProfileScoped</a>
<li>mCurrent
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#880bde551348e2ee87e3b94ffceafd71">nv::cloth::GpuParticles</a>
<li>mDetached
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">nv::cloth::NvClothProfileScoped</a>
<li>mEventName
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">nv::cloth::NvClothProfileScoped</a>
<li>mIndices
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">nv::cloth::CookedData</a>
<li>mNumParticles
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">nv::cloth::CookedData</a>
<li>mPadding
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">nv::cloth::PhaseConfig</a>
<li>mPhaseIndex
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">nv::cloth::PhaseConfig</a>
<li>mPhaseIndices
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">nv::cloth::CookedData</a>
<li>mPhaseTypes
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">nv::cloth::CookedData</a>
<li>mPrevious
: <a class="el" href="structnv_1_1cloth_1_1_gpu_particles.html#fc1d82619147076f4b9c0e8d9da93a69">nv::cloth::GpuParticles</a>
<li>mProfilerData
: <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">nv::cloth::NvClothProfileScoped</a>
<li>mRefCount
: <a class="el" href="classnv_1_1cloth_1_1_fabric.html#21c01654b6a8e398f5b39e24678f706d">nv::cloth::Fabric</a>
<li>mRestvalues
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">nv::cloth::CookedData</a>
<li>mSets
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">nv::cloth::CookedData</a>
<li>mStiffness
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">nv::cloth::PhaseConfig</a>
<li>mStiffnessMultiplier
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">nv::cloth::PhaseConfig</a>
<li>mStiffnessValues
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">nv::cloth::CookedData</a>
<li>mStretchLimit
: <a class="el" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">nv::cloth::PhaseConfig</a>
<li>mTetherLengths
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">nv::cloth::CookedData</a>
<li>mTriangles
: <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">nv::cloth::CookedData</a>
</ul>
<h3><a class="anchor" name="index_n">- n -</a></h3><ul>
<li>nbParticles
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1">nv::cloth::ClothFabricDesc</a>
<li>nbPhases
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78">nv::cloth::ClothFabricDesc</a>
<li>nbSets
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc">nv::cloth::ClothFabricDesc</a>
<li>nbTethers
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad">nv::cloth::ClothFabricDesc</a>
<li>nbTriangles
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nv::cloth::ClothFabricDesc</a>
</ul>
<h3><a class="anchor" name="index_p">- p -</a></h3><ul>
<li>phases
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f">nv::cloth::ClothFabricDesc</a>
<li>phaseType
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#f19b795dfc88f16a90a75621be1fbd0a">nv::cloth::ClothFabricPhase</a>
<li>points
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#********************************">nv::cloth::ClothMeshDesc</a>
<li>pointsStiffness
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598">nv::cloth::ClothMeshDesc</a>
</ul>
<h3><a class="anchor" name="index_q">- q -</a></h3><ul>
<li>quads
: <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb">nv::cloth::ClothMeshDesc</a>
</ul>
<h3><a class="anchor" name="index_r">- r -</a></h3><ul>
<li>restvalues
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd">nv::cloth::ClothFabricDesc</a>
</ul>
<h3><a class="anchor" name="index_s">- s -</a></h3><ul>
<li>setIndex
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#a9f1038529f5a7545c6f19c95be61015">nv::cloth::ClothFabricPhase</a>
<li>sets
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452">nv::cloth::ClothFabricDesc</a>
<li>stride
: <a class="el" href="structnv_1_1cloth_1_1_strided_data.html#fa7d89f91e82b269c40ddaffb726e3ba">nv::cloth::StridedData</a>
</ul>
<h3><a class="anchor" name="index_t">- t -</a></h3><ul>
<li>tetherAnchors
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83">nv::cloth::ClothFabricDesc</a>
<li>tetherLengths
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a">nv::cloth::ClothFabricDesc</a>
<li>triangles
: <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">nv::cloth::ClothFabricDesc</a>
, <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e">nv::cloth::ClothMeshDesc</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
