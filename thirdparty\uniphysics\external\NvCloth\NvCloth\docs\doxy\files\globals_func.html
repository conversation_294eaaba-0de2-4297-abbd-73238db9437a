<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="globals.html"><span>All</span></a></li>
      <li class="current"><a href="globals_func.html"><span>Functions</span></a></li>
      <li><a href="globals_vars.html"><span>Variables</span></a></li>
      <li><a href="globals_type.html"><span>Typedefs</span></a></li>
      <li><a href="globals_defs.html"><span>Defines</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<ul>
<li>GetNvClothAllocator()
: <a class="el" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">Callbacks.h</a>
<li>NV_CLOTH_API()
: <a class="el" href="_factory_8h.html#00a0355aec1b4fbf9cc00c5ab61939f8">Factory.h</a>
, <a class="el" href="group__extensions.html#gf7a3e36d6f91e96f595c90a191bdf4a6">ClothTetherCooker.h</a>
, <a class="el" href="group__extensions.html#g852139ee66f6f599c1041ab961286e8c">ClothMeshQuadifier.h</a>
, <a class="el" href="group__extensions.html#g927e2eff017f040fb3ed01823e46fc4a">ClothFabricCooker.h</a>
, <a class="el" href="_factory_8h.html#f3c25e9c1f8c212ac7c1c734dbecd9a6">Factory.h</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
