C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Arc.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Arc.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Arc3D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Arc3D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Bezier.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Bezier.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Box.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Box.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Circle.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Circle.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Ellipse.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Ellipse.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Line2D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Line2D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Line3D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Line3D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Point2D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Point2D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Point3D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Point3D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Polygon2D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Polygon2D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Polygon3D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Polygon3D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Polyline2D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Polyline2D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Polyline2DImpl.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Polyline2DImpl.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Polyline3D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Polyline3D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Rect.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Rect.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Serialize.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Serialize.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\source\Sphere.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\geoshape\platform\msvc\projects\x64\Release\geoshape\Sphere.obj
