var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("tri_strip")
node0.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node0.skip = false

_editor.add_node(node0, 319.30050275821, -169.04286659416)

var node1 = ::rendergraph::nodes::clear::Clear()

node1.masks = [ "color", "stencil" ]
node1.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node1, -195.87972269609, -10.621836605118)

var node2 = ::rendergraph::nodes::shader::Shader()
node2.query_param("inc_dir").value = ""

node2.vs = "

#version 330 core

layout (location = 0) in vec3 aPos;

layout (location = 1) in vec2 aTexCoord;



out vec2 TexCoord;



void main()

{

	gl_Position = vec4(aPos, 1.0);

	TexCoord = vec2(aTexCoord.x, aTexCoord.y);

}

"
node2.tcs = ""
node2.tes = ""
node2.gs = ""
node2.fs = "

#version 330 core

out vec4 FragColor;



in vec2 TexCoord;



// texture sampler

uniform sampler3D texture1;

uniform UBO
{
	float layer;	
};

void main()

{

	FragColor = texture(texture1, vec3(TexCoord, layer));

}

"
node2.cs = ""
node2.render_gen()

_editor.add_node(node2, 27.972000019549, -181.69253076011)

var node3 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node3.type = "quad"
node3.layout = [ "position", "texture" ]
node3.shape_params = {  }

_editor.add_node(node3, 53.109942610703, -338.81451019847)

var node4 = ::blueprint::nodes::input::Input()

node4.var_name = "texture1"
node4.var_type = "texture"

_editor.add_node(node4, -149.51314800902, -218.36353391162)

var node5 = ::blueprint::nodes::input::Input()

node5.var_name = "layer"
node5.var_type = "num"

_editor.add_node(node5, -145.08751065187, -150.16943958221)

var node6 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node6, 64.028979492189, 140.00662841797)

var node7 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node7, 499.62899169922, 11.306591796874)

var node8 = ::rendergraph::nodes::pass::Pass()

node8.once = false

_editor.add_node(node8, 642.62895507813, 12.406634521484)

Blueprint.connect(node4, "var", node6, "in")
Blueprint.connect(node6, "out", node7, "cond")
Blueprint.connect(node0, "next", node7, "false")
Blueprint.connect(node7, "next", node8, "prev")
Blueprint.connect(node5, "var", node2, "layer")
Blueprint.connect(node4, "var", node2, "texture1")
Blueprint.connect(node1, "next", node0, "prev")
Blueprint.connect(node2, "out", node0, "shader")
Blueprint.connect(node3, "out", node0, "va")
