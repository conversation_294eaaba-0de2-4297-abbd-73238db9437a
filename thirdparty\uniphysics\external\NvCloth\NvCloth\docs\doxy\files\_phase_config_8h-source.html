<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: PhaseConfig.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>PhaseConfig.h</h1><a href="_phase_config_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 <span class="preprocessor">#pragma once</span>
<a name="l00031"></a>00031 <span class="preprocessor"></span>
<a name="l00032"></a>00032 <span class="preprocessor">#include &lt;foundation/Px.h&gt;</span>
<a name="l00033"></a>00033 
<a name="l00034"></a>00034 <span class="keyword">namespace </span>nv
<a name="l00035"></a>00035 {
<a name="l00036"></a>00036 <span class="keyword">namespace </span>cloth
<a name="l00037"></a>00037 {
<a name="l00038"></a>00038 
<a name="l00039"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html">00039</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_phase_config.html">PhaseConfig</a>
<a name="l00040"></a>00040 {
<a name="l00041"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#44574dd7cc0f67ec460bb0c9bdeb0819">00041</a>     <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#44574dd7cc0f67ec460bb0c9bdeb0819">PhaseConfig</a>(uint16_t index = uint16_t(-1))
<a name="l00042"></a>00042         : <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">mStiffness</a>(1.0f)
<a name="l00043"></a>00043         , <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">mStiffnessMultiplier</a>(1.0f)
<a name="l00044"></a>00044         , <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">mCompressionLimit</a>(1.0f)
<a name="l00045"></a>00045         , <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">mStretchLimit</a>(1.0f)
<a name="l00046"></a>00046         , <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">mPhaseIndex</a>(index)
<a name="l00047"></a>00047         , <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">mPadding</a>(0xffff)
<a name="l00048"></a>00048     {
<a name="l00049"></a>00049     }
<a name="l00050"></a>00050 
<a name="l00051"></a>00051     <span class="comment">//These 4 floats need to be in order as they are loaded in to simd vectors in the solver</span>
<a name="l00052"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">00052</a>     <span class="keywordtype">float</span> <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#25a2498b7d86f3420cbe02914f442838">mStiffness</a>; <span class="comment">// target convergence rate per iteration (1/solverFrequency)</span>
<a name="l00053"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">00053</a>     <span class="keywordtype">float</span> <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#5b9466d183a7bcc02468f5bb16b00336">mStiffnessMultiplier</a>;
<a name="l00054"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">00054</a>     <span class="keywordtype">float</span> <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#862b9a1c68a6f98eb84b1f2f2777640f">mCompressionLimit</a>;
<a name="l00055"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">00055</a>     <span class="keywordtype">float</span> <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#9de88a355594846c6818c4439e46899b">mStretchLimit</a>;
<a name="l00056"></a>00056 
<a name="l00057"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">00057</a>     uint16_t <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#36a0e7d9261b54665b448f37fc8aa65f">mPhaseIndex</a>;
<a name="l00058"></a><a class="code" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">00058</a>     uint16_t <a class="code" href="structnv_1_1cloth_1_1_phase_config.html#a307116b5e6af2e2a97bf57f94e85a10">mPadding</a>;
<a name="l00059"></a>00059 };
<a name="l00060"></a>00060 
<a name="l00061"></a>00061 } <span class="comment">// namespace cloth</span>
<a name="l00062"></a>00062 } <span class="comment">// namespace nv</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
