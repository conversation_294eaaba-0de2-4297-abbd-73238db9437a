<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Allocator.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>Allocator.h</h1><a href="_allocator_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.</span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 
<a name="l00036"></a>00036 <span class="preprocessor">#pragma once</span>
<a name="l00037"></a>00037 <span class="preprocessor"></span>
<a name="l00038"></a>00038 <span class="preprocessor">#include &lt;PsArray.h&gt;</span>
<a name="l00039"></a>00039 <span class="preprocessor">#include &lt;PsHashMap.h&gt;</span>
<a name="l00040"></a>00040 <span class="preprocessor">#include &lt;PsAlignedMalloc.h&gt;</span>
<a name="l00041"></a>00041 <span class="preprocessor">#include "<a class="code" href="_callbacks_8h.html" title="All functions to initialize and use user provided callbacks are declared in this...">NvCloth/Callbacks.h</a>"</span>
<a name="l00042"></a>00042 
<a name="l00043"></a>00043 <span class="keyword">namespace </span>nv
<a name="l00044"></a>00044 {
<a name="l00045"></a>00045 <span class="keyword">namespace </span>cloth
<a name="l00046"></a>00046 {
<a name="l00047"></a>00047 
<a name="l00048"></a>00048 <span class="keywordtype">void</span>* allocate(<span class="keywordtype">size_t</span>);
<a name="l00049"></a>00049 <span class="keywordtype">void</span> deallocate(<span class="keywordtype">void</span>*);
<a name="l00050"></a>00050 
<a name="l00051"></a>00051 <span class="keyword">class </span>NonTrackingAllocator
<a name="l00052"></a>00052 {
<a name="l00053"></a>00053 <span class="keyword">public</span>:
<a name="l00054"></a>00054     PX_FORCE_INLINE NonTrackingAllocator(<span class="keyword">const</span> <span class="keywordtype">char</span>* = 0)
<a name="l00055"></a>00055     {
<a name="l00056"></a>00056     }
<a name="l00057"></a>00057     PX_FORCE_INLINE <span class="keywordtype">void</span>* allocate(<span class="keywordtype">size_t</span> size, <span class="keyword">const</span> <span class="keywordtype">char</span>* file, <span class="keywordtype">int</span> line)
<a name="l00058"></a>00058     {
<a name="l00059"></a>00059         <span class="keywordflow">return</span> !size ? 0 : <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;allocate(size, <span class="stringliteral">"NonTrackedAlloc"</span>, file, line);
<a name="l00060"></a>00060     }
<a name="l00061"></a>00061     PX_FORCE_INLINE <span class="keywordtype">void</span> deallocate(<span class="keywordtype">void</span>* ptr)
<a name="l00062"></a>00062     {
<a name="l00063"></a>00063         <span class="keywordflow">if</span> (ptr)
<a name="l00064"></a>00064             <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00065"></a>00065     }
<a name="l00066"></a>00066 };
<a name="l00067"></a>00067 
<a name="l00068"></a>00068 <span class="comment">/* templated typedefs for convenience */</span>
<a name="l00069"></a>00069 
<a name="l00070"></a>00070 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00071"></a>00071 <span class="keyword">struct </span>Vector
<a name="l00072"></a>00072 {
<a name="l00073"></a>00073     <span class="keyword">typedef</span> physx::shdfnd::Array&lt;T, NonTrackingAllocator&gt; Type;
<a name="l00074"></a>00074 };
<a name="l00075"></a>00075 
<a name="l00076"></a>00076 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, <span class="keywordtype">size_t</span> alignment&gt;
<a name="l00077"></a>00077 <span class="keyword">struct </span>AlignedVector
<a name="l00078"></a>00078 {
<a name="l00079"></a>00079     <span class="keyword">typedef</span> physx::shdfnd::Array&lt;T, physx::shdfnd::AlignedAllocator&lt;alignment, NonTrackingAllocator&gt; &gt; Type;
<a name="l00080"></a>00080 };
<a name="l00081"></a>00081 
<a name="l00082"></a>00082 <span class="keyword">template</span> &lt;<span class="keyword">class</span> Key, <span class="keyword">class</span> Value, <span class="keyword">class</span> HashFn = physx::shdfnd::Hash&lt;Key&gt; &gt;
<a name="l00083"></a>00083 <span class="keyword">struct </span>HashMap
<a name="l00084"></a>00084 {
<a name="l00085"></a>00085     <span class="keyword">typedef</span> physx::shdfnd::HashMap&lt;Key, Value, HashFn, NonTrackingAllocator&gt; Type;
<a name="l00086"></a>00086 };
<a name="l00087"></a>00087 
<a name="l00088"></a>00088 <span class="keyword">struct </span>NvClothOverload{};
<a name="l00089"></a>00089 <span class="preprocessor">#define NV_CLOTH_NEW(T) new (__FILE__, __LINE__, nv::cloth::NvClothOverload()) T</span>
<a name="l00090"></a>00090 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_ALLOC(n, name) GetNvClothAllocator()-&gt;allocate(n, name, __FILE__, __LINE__)</span>
<a name="l00091"></a>00091 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_FREE(x) GetNvClothAllocator()-&gt;deallocate(x)</span>
<a name="l00092"></a>00092 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_DELETE(x) delete x</span>
<a name="l00093"></a>00093 <span class="preprocessor"></span>
<a name="l00094"></a>00094 } <span class="comment">// namespace cloth</span>
<a name="l00095"></a>00095 } <span class="comment">// namespace nv</span>
<a name="l00096"></a>00096 
<a name="l00097"></a>00097 <span class="comment">//new/delete operators need to be declared in global scope</span>
<a name="l00098"></a>00098 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00099"></a>00099 PX_INLINE <span class="keywordtype">void</span>* operator new(<span class="keywordtype">size_t</span> size, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName,
<a name="l00100"></a>00100                              <span class="keyword">typename</span> physx::shdfnd::EnableIfPod&lt;T, int&gt;::Type line, nv::cloth::NvClothOverload overload)
<a name="l00101"></a>00101 {
<a name="l00102"></a>00102     PX_UNUSED(overload);
<a name="l00103"></a>00103     <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;allocate(size, <span class="stringliteral">"&lt;TypeName Unknown&gt;"</span>, fileName, line);
<a name="l00104"></a>00104 }
<a name="l00105"></a>00105 
<a name="l00106"></a>00106 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00107"></a>00107 PX_INLINE <span class="keywordtype">void</span>* operator new [](<span class="keywordtype">size_t</span> size, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName,
<a name="l00108"></a>00108                                 <span class="keyword">typename</span> physx::shdfnd::EnableIfPod&lt;T, int&gt;::Type line, nv::cloth::NvClothOverload overload)
<a name="l00109"></a>00109 {
<a name="l00110"></a>00110     PX_UNUSED(overload);
<a name="l00111"></a>00111     <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;allocate(size, <span class="stringliteral">"&lt;TypeName Unknown&gt;"</span>, fileName, line);
<a name="l00112"></a>00112 }
<a name="l00113"></a>00113 
<a name="l00114"></a>00114 <span class="comment">// If construction after placement new throws, this placement delete is being called.</span>
<a name="l00115"></a>00115 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00116"></a>00116 PX_INLINE <span class="keywordtype">void</span> operator delete(<span class="keywordtype">void</span>* ptr, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName,
<a name="l00117"></a>00117                                <span class="keyword">typename</span> physx::shdfnd::EnableIfPod&lt;T, int&gt;::Type line, nv::cloth::NvClothOverload overload)
<a name="l00118"></a>00118 {
<a name="l00119"></a>00119     PX_UNUSED(fileName);
<a name="l00120"></a>00120     PX_UNUSED(line);
<a name="l00121"></a>00121     PX_UNUSED(overload);
<a name="l00122"></a>00122 
<a name="l00123"></a>00123     <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00124"></a>00124 }
<a name="l00125"></a>00125 
<a name="l00126"></a>00126 <span class="comment">// If construction after placement new throws, this placement delete is being called.</span>
<a name="l00127"></a>00127 <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T&gt;
<a name="l00128"></a>00128 PX_INLINE <span class="keywordtype">void</span> operator delete [](<span class="keywordtype">void</span>* ptr, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName,
<a name="l00129"></a>00129                                   <span class="keyword">typename</span> physx::shdfnd::EnableIfPod&lt;T, int&gt;::Type line, nv::cloth::NvClothOverload overload)
<a name="l00130"></a>00130 {
<a name="l00131"></a>00131     PX_UNUSED(fileName);
<a name="l00132"></a>00132     PX_UNUSED(line);
<a name="l00133"></a>00133     PX_UNUSED(overload);
<a name="l00134"></a>00134 
<a name="l00135"></a>00135     <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00136"></a>00136 }
<a name="l00137"></a>00137 
<a name="l00138"></a>00138 <span class="keyword">namespace </span>nv
<a name="l00139"></a>00139 {
<a name="l00140"></a>00140 <span class="keyword">namespace </span>cloth
<a name="l00141"></a>00141 {
<a name="l00142"></a>00142 
<a name="l00143"></a>00143 <span class="keyword">class </span>UserAllocated
<a name="l00144"></a>00144 {
<a name="l00145"></a>00145   <span class="keyword">public</span>:
<a name="l00146"></a>00146     PX_INLINE <span class="keywordtype">void</span>* operator new(<span class="keywordtype">size_t</span> size, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> line, NvClothOverload overload)
<a name="l00147"></a>00147     {
<a name="l00148"></a>00148         PX_UNUSED(overload);
<a name="l00149"></a>00149         <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;allocate(size, <span class="stringliteral">"&lt;TypeName Unknown&gt;"</span>, fileName, line);
<a name="l00150"></a>00150     }
<a name="l00151"></a>00151     PX_INLINE <span class="keywordtype">void</span>* operator new [](<span class="keywordtype">size_t</span> size, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> line, NvClothOverload overload)
<a name="l00152"></a>00152     {
<a name="l00153"></a>00153         PX_UNUSED(overload);
<a name="l00154"></a>00154         <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;allocate(size, <span class="stringliteral">"&lt;TypeName Unknown&gt;"</span>, fileName, line);
<a name="l00155"></a>00155     }
<a name="l00156"></a>00156 
<a name="l00157"></a>00157     <span class="comment">// placement delete</span>
<a name="l00158"></a>00158     PX_INLINE <span class="keywordtype">void</span> operator delete(<span class="keywordtype">void</span>* ptr, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> line, NvClothOverload overload)
<a name="l00159"></a>00159     {
<a name="l00160"></a>00160         PX_UNUSED(fileName);
<a name="l00161"></a>00161         PX_UNUSED(line);
<a name="l00162"></a>00162         PX_UNUSED(overload);
<a name="l00163"></a>00163         <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00164"></a>00164     }
<a name="l00165"></a>00165     PX_INLINE <span class="keywordtype">void</span> operator delete [](<span class="keywordtype">void</span>* ptr, <span class="keyword">const</span> <span class="keywordtype">char</span>* fileName, <span class="keywordtype">int</span> line, NvClothOverload overload)
<a name="l00166"></a>00166     {
<a name="l00167"></a>00167         PX_UNUSED(fileName);
<a name="l00168"></a>00168         PX_UNUSED(line);
<a name="l00169"></a>00169         PX_UNUSED(overload);
<a name="l00170"></a>00170         <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00171"></a>00171     } 
<a name="l00172"></a>00172     PX_INLINE <span class="keywordtype">void</span> operator delete(<span class="keywordtype">void</span>* ptr)
<a name="l00173"></a>00173     {
<a name="l00174"></a>00174         <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00175"></a>00175     }
<a name="l00176"></a>00176     PX_INLINE <span class="keywordtype">void</span> operator delete [](<span class="keywordtype">void</span>* ptr)
<a name="l00177"></a>00177     {
<a name="l00178"></a>00178         <span class="keywordflow">return</span> <a class="code" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a>()-&gt;deallocate(ptr);
<a name="l00179"></a>00179     }
<a name="l00180"></a>00180 };
<a name="l00181"></a>00181 
<a name="l00182"></a>00182 } <span class="comment">// namespace cloth</span>
<a name="l00183"></a>00183 } <span class="comment">// namespace nv</span>
</span></pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
