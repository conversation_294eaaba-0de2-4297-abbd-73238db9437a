#pragma once

// Minimal OpenCLDebugInfo100.h for SPIRV-Tools compatibility
// This provides basic OpenCL debug info constants

namespace OpenCLDebugInfo100 {

// Debug info instructions
enum Instructions {
    DebugInfoNone = 0,
    DebugCompilationUnit = 1,
    DebugTypeBasic = 2,
    DebugTypePointer = 3,
    DebugTypeQualifier = 4,
    DebugTypeArray = 5,
    DebugTypeVector = 6,
    DebugTypedef = 7,
    DebugTypeFunction = 8,
    DebugTypeEnum = 9,
    DebugTypeComposite = 10,
    DebugTypeMember = 11,
    DebugTypeInheritance = 12,
    DebugTypePtrToMember = 13,
    DebugTypeTemplate = 14,
    DebugTypeTemplateParameter = 15,
    DebugTypeTemplateTemplateParameter = 16,
    DebugTypeTemplateParameterPack = 17,
    DebugGlobalVariable = 18,
    DebugFunctionDeclaration = 19,
    DebugFunction = 20,
    DebugLexicalBlock = 21,
    DebugLexicalBlockDiscriminator = 22,
    DebugScope = 23,
    DebugNoScope = 24,
    DebugInlinedAt = 25,
    DebugLocalVariable = 26,
    DebugInlinedVariable = 27,
    DebugDeclare = 28,
    DebugValue = 29,
    DebugOperation = 30,
    DebugExpression = 31,
    DebugMacroDef = 32,
    DebugMacroUndef = 33,
    DebugImportedEntity = 34,
    DebugSource = 35,
    InstructionsMax = 0x7fffffff
};

// Debug info flags
enum DebugInfoFlags {
    FlagIsProtected = 0x01,
    FlagIsPrivate = 0x02,
    FlagIsPublic = 0x03,
    FlagIsLocal = 0x04,
    FlagIsDefinition = 0x08,
    FlagFwdDecl = 0x10,
    FlagArtificial = 0x20,
    FlagExplicit = 0x40,
    FlagPrototyped = 0x80,
    FlagObjectPointer = 0x100,
    FlagStaticMember = 0x200,
    FlagIndirectVariable = 0x400,
    FlagLValueReference = 0x800,
    FlagRValueReference = 0x1000,
    FlagIsOptimized = 0x2000,
    FlagIsEnumClass = 0x4000,
    FlagTypePassByValue = 0x8000,
    FlagTypePassByReference = 0x10000
};

} // namespace OpenCLDebugInfo100
