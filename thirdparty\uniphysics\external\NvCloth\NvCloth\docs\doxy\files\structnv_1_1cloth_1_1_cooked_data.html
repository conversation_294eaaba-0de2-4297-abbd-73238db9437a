<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::CookedData Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_cooked_data.html">CookedData</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::CookedData Struct Reference</h1><!-- doxytag: class="nv::cloth::CookedData" --><code>#include &lt;<a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_cooked_data-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Attributes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">mAnchors</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">mIndices</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">mNumParticles</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">mPhaseIndices</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const int32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">mPhaseTypes</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">mRestvalues</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">mSets</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">mStiffnessValues</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">mTetherLengths</a></td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">mTriangles</a></td></tr>

</table>
<hr><h2>Member Data Documentation</h2>
<a class="anchor" name="a007ccb67a4839797735e5eb1194dc20"></a><!-- doxytag: member="nv::cloth::CookedData::mAnchors" ref="a007ccb67a4839797735e5eb1194dc20" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const uint32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">nv::cloth::CookedData::mAnchors</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="b8a3ec4f4c531de0e4702cedf8a74261"></a><!-- doxytag: member="nv::cloth::CookedData::mIndices" ref="b8a3ec4f4c531de0e4702cedf8a74261" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const uint32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">nv::cloth::CookedData::mIndices</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="00f9afd3833301fb02d20c779a6ec132"></a><!-- doxytag: member="nv::cloth::CookedData::mNumParticles" ref="00f9afd3833301fb02d20c779a6ec132" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">uint32_t <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">nv::cloth::CookedData::mNumParticles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="feabe61136d9cdcf6625494bf8cf2a89"></a><!-- doxytag: member="nv::cloth::CookedData::mPhaseIndices" ref="feabe61136d9cdcf6625494bf8cf2a89" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const uint32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">nv::cloth::CookedData::mPhaseIndices</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="17ceb5f81c8fd9c4f5af1e8c38b12b35"></a><!-- doxytag: member="nv::cloth::CookedData::mPhaseTypes" ref="17ceb5f81c8fd9c4f5af1e8c38b12b35" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const int32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">nv::cloth::CookedData::mPhaseTypes</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="accb8f8ffafaaf9e3a19753ce2167bc1"></a><!-- doxytag: member="nv::cloth::CookedData::mRestvalues" ref="accb8f8ffafaaf9e3a19753ce2167bc1" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const float&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">nv::cloth::CookedData::mRestvalues</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="c29c4d9fef1364ee124e81b05149925f"></a><!-- doxytag: member="nv::cloth::CookedData::mSets" ref="c29c4d9fef1364ee124e81b05149925f" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const uint32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">nv::cloth::CookedData::mSets</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="49a9c6e81b7c95174b30d3fd978ab409"></a><!-- doxytag: member="nv::cloth::CookedData::mStiffnessValues" ref="49a9c6e81b7c95174b30d3fd978ab409" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const float&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">nv::cloth::CookedData::mStiffnessValues</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="ca97240e8d092d9cac41fe557eb375bd"></a><!-- doxytag: member="nv::cloth::CookedData::mTetherLengths" ref="ca97240e8d092d9cac41fe557eb375bd" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const float&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">nv::cloth::CookedData::mTetherLengths</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="03c99508d108059b41e9dfd6fbda6412"></a><!-- doxytag: member="nv::cloth::CookedData::mTriangles" ref="03c99508d108059b41e9dfd6fbda6412" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt;const uint32_t&gt; <a class="el" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">nv::cloth::CookedData::mTriangles</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_fabric_cooker_8h-source.html">ClothFabricCooker.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
