body {
  background-color: #fcfcfc;
}
.example {
  padding: 20px;
  border: 1px solid #efefef;
}
.doc-section {
  padding-top: 60px;
  margin-top: -60px;
}
/*
 * Side navigation
 *
 * Scrollspy and affixed enhanced navigation to highlight sections and secondary
 * sections of docs content.
 */
/* By default it's not affixed in mobile views, so undo that */
.bs-sidebar.affix {
  position: static;
}
/* First level of nav */
.bs-sidenav {
  padding-top: 10px;
  padding-bottom: 10px;
  text-shadow: 0 1px 0 #fff;
  background-color: #f7f5fa;
  border-radius: 5px;
}
/* All levels of nav */
.bs-sidebar .nav > li > a {
  display: block;
  color: #716b7a;
  padding: 5px 20px;
}
.bs-sidebar .nav > li > a:hover,
.bs-sidebar .nav > li > a:focus {
  text-decoration: none;
  background-color: #e5e3e9;
  border-right: 1px solid #dbd8e0;
}
.bs-sidebar .nav > .active > a,
.bs-sidebar .nav > .active:hover > a,
.bs-sidebar .nav > .active:focus > a {
  font-weight: bold;
  color: #563d7c;
  background-color: transparent;
  border-right: 1px solid #563d7c;
}
/* Nav: second level (shown on .active) */
.bs-sidebar .nav .nav {
  margin-bottom: 8px;
}
.bs-sidebar .nav .nav > li > a {
  padding-top: 3px;
  padding-bottom: 3px;
  padding-left: 30px;
  font-size: 90%;
}
/* Show and affix the side nav when space allows it */
@media (min-width: 992px) {
  .bs-sidebar .nav > .active > ul {
    display: block;
  }
  /* Widen the fixed sidebar */
  .bs-sidebar.affix,
  .bs-sidebar.affix-bottom {
    width: 213px;
  }
  .bs-sidebar.affix {
    position: fixed;
    /* Undo the static from mobile first approach */
    top: 80px;
  }
  .bs-sidebar.affix-bottom {
    position: absolute;
    /* Undo the static from mobile first approach */
  }
  .bs-sidebar.affix-bottom .bs-sidenav,
  .bs-sidebar.affix .bs-sidenav {
    margin-top: 0;
    margin-bottom: 0;
  }
}
@media (min-width: 1200px) {
  /* Widen the fixed sidebar again */
  .bs-sidebar.affix-bottom,
  .bs-sidebar.affix {
    width: 263px;
  }
}
.show-grid {
  margin-bottom: 15px;
}
.show-grid [class^="col-"] {
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: #eee;
  border: 1px solid #ddd;
  background-color: rgba(86, 61, 124, 0.15);
  border: 1px solid rgba(86, 61, 124, 0.2);
}
