#pragma once

#include <SM_Vector.h>
#include <vector>
#include <memory>

namespace graph {

class Node;
class Graph;

class GraphLayout
{
public:
    GraphLayout();
    virtual ~GraphLayout();

    virtual void Layout(const Graph& graph) = 0;
    virtual void SetNodePosition(std::shared_ptr<Node> node, const sm::vec2& pos);
    virtual sm::vec2 GetNodePosition(std::shared_ptr<Node> node) const;

    // Static layout methods used by wrap_Graph.cpp
    static void StressMinimization(Graph& graph);
    static void OptimalHierarchy(Graph& graph);
    static void HierarchyRanking(Graph& graph);

protected:
    std::vector<std::pair<std::shared_ptr<Node>, sm::vec2>> m_node_positions;
};

class ForceDirectedLayout : public GraphLayout
{
public:
    ForceDirectedLayout();
    virtual ~ForceDirectedLayout();

    virtual void Layout(const Graph& graph) override;

    void SetIterations(int iterations) { m_iterations = iterations; }
    void SetRepulsionForce(float force) { m_repulsion_force = force; }
    void SetAttractionForce(float force) { m_attraction_force = force; }

private:
    int m_iterations;
    float m_repulsion_force;
    float m_attraction_force;
};

} // namespace graph
