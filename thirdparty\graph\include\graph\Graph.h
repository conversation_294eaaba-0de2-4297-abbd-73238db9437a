#pragma once

#include <vector>
#include <memory>
#include <map>

namespace graph
{

class Node;
class Edge;

class Graph
{
public:
	Graph() : m_is_directed(false) {}

	void AddNode(const std::shared_ptr<Node>& node);
	void AddEdge(size_t f_node, size_t t_node);

	auto& GetNodes() const { return m_nodes; }
	size_t GetNodesNum() const { return m_nodes.size(); }
	std::shared_ptr<Node> GetNode(size_t index) const;

	const std::map<size_t, std::shared_ptr<Edge>>& GetEdges() const { return m_edges; }

	bool IsDirected() const { return m_is_directed; }
	void SetDirected(bool directed) { m_is_directed = directed; }

	void ClearEdges(size_t node_idx);

private:
	std::vector<std::shared_ptr<Node>> m_nodes;
	std::map<size_t, std::shared_ptr<Edge>> m_edges;
	bool m_is_directed;

}; // Graph

}