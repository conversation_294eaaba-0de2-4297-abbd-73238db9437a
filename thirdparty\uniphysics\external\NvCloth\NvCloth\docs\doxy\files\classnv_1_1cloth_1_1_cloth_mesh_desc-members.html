<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothMeshDesc Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e475fee21a2442dd86f30d836a6ad1af">ClothMeshDesc</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e87cb1303f9939d674b448657abd434a">flags</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#73d92bee06c06b6748f1726990ca20de">invMasses</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#3dbb3dc26cddbdf72c5455a485f0a4f4">isValid</a>() const </td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#1e8ccce29d38eb316537a4a24eb76855">points</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#035d7ca18e3feef858f273e0afe16598">pointsStiffness</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#791bdd04c451e10b0155563766b25cdb">quads</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#d269d7bebc10b54088fc73e77c1372dd">setToDefault</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html#e4517fa952e6cf3ac848b1b7bc67714e">triangles</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">nv::cloth::ClothMeshDesc</a></td><td></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
