#include "logger/logger.h"

#include <iostream>
#include <fstream>
#include <mutex>

namespace logger {

static LogLevel g_log_level = LogLevel::Info;
static std::mutex g_log_mutex;
static std::ofstream g_log_file;

void SetLogLevel(LogLevel level) {
    g_log_level = level;
}

LogLevel GetLogLevel() {
    return g_log_level;
}

void SetLogFile(const std::string& filename) {
    std::lock_guard<std::mutex> lock(g_log_mutex);
    if (g_log_file.is_open()) {
        g_log_file.close();
    }
    g_log_file.open(filename, std::ios::app);
}

void Log(LogLevel level, const std::string& message) {
    if (level < g_log_level) {
        return;
    }
    
    std::lock_guard<std::mutex> lock(g_log_mutex);
    
    const char* level_str = "";
    switch (level) {
        case LogLevel::Debug: level_str = "DEBUG"; break;
        case LogLevel::Info: level_str = "INFO"; break;
        case LogLevel::Warning: level_str = "WARNING"; break;
        case LogLevel::Error: level_str = "ERROR"; break;
        case LogLevel::Fatal: level_str = "FATAL"; break;
    }
    
    std::string log_line = "[" + std::string(level_str) + "] " + message;
    
    if (g_log_file.is_open()) {
        g_log_file << log_line << std::endl;
        g_log_file.flush();
    } else {
        std::cout << log_line << std::endl;
    }
}

} // namespace logger
