C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\ArrowButton.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\ArrowButton.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Button.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Button.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Callback.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Callback.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Checkbox.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Checkbox.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Combo.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Combo.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Context.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Context.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\EntityFactory.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\EntityFactory.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Frame.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Frame.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\ImGui.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\ImGui.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\IOState.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\IOState.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Label.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Label.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\RadioButton.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\RadioButton.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\RenderBuffer.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\RenderBuffer.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\RenderStyle.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\RenderStyle.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Selectable.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Selectable.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Slider.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Slider.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\System.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\System.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Utility.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Utility.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\source\Window.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\easygui\platform\msvc\projects\x64\Release\easygui\Window.obj
