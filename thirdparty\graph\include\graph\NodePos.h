#pragma once

#include <SM_Vector.h>
#include <memory>

namespace graph {

class Node;

class NodePos
{
public:
    NodePos();
    NodePos(const sm::vec2& pos);
    virtual ~NodePos();
    
    void SetPosition(const sm::vec2& pos) { m_pos = pos; }
    const sm::vec2& GetPosition() const { return m_pos; }
    
    void SetNode(std::shared_ptr<Node> node) { m_node = node; }
    std::shared_ptr<Node> GetNode() const { return m_node; }
    
private:
    sm::vec2 m_pos;
    std::shared_ptr<Node> m_node;
};

} // namespace graph
