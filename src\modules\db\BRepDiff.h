#pragma once

#include <memory>
#include <vector>
#include <string>

namespace tt
{

class BRep;

class BRepDiff
{
public:
    enum class ChangeType
    {
        Added,
        Removed,
        Modified
    };
    
    struct Change
    {
        ChangeType type;
        std::string description;
        // Additional change details can be added here
    };

public:
    BRepDiff();
    ~BRepDiff();
    
    void SetOldBrep(const std::shared_ptr<BRep>& brep);
    void SetNewBrep(const std::shared_ptr<BRep>& brep);
    
    std::vector<Change> GetChanges() const;
    bool HasChanges() const;

private:
    std::shared_ptr<BRep> m_old_brep;
    std::shared_ptr<BRep> m_new_brep;
};

} // namespace tt
