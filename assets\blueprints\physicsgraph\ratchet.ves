var node0 = ::blueprint::nodes::subgraph::Subgraph()
node0.load_from_file(_editor, "../geograph/gear.ves")

_editor.add_node(node0, 448.42333268782, 632.99078992522)

var node1 = ::scenegraph::nodes::transform::Transform()

_editor.add_node(node1, 239.84111562882, 254.35358236215)

var node2 = ::physicsgraph::nodes::body::Body()
node2.query_param("type").value = "dynamic"
node2.query_param("gravity").value = 1
node2.query_param("density").value = 1.0230346918106
node2.query_param("restitution").value = 0
node2.query_param("friction").value = 0

_editor.add_node(node2, 94.35277551626, -18.249593178202)

var node3 = ::physicsgraph::nodes::revolute_joint::RevoluteJoint()
node3.query_param("limit").value = false
node3.query_param("lower").value = -0.1
node3.query_param("upper").value = 0.1
node3.query_param("motor").value = true
node3.query_param("torque").value = 10000
node3.query_param("speed").value = -1

_editor.add_node(node3, 547.16869184134, -22.776876979237)

var node4 = ::blueprint::nodes::load::Load()

node4.var_name = "body"

_editor.add_node(node4, 606.3462522948, 296.16492314267)

var node5 = ::blueprint::nodes::load::Load()

node5.var_name = "body"

_editor.add_node(node5, 414.87821606774, 10.29549584262)

var node6 = ::blueprint::nodes::subgraph::Subgraph()
node6.load_from_file(_editor, "../geograph/gear.ves")

_editor.add_node(node6, -100.89995807101, 643.74112200636)

var node7 = ::blueprint::nodes::store::Store()

node7.var_name = "pos"

_editor.add_node(node7, 101.86447928537, 830.00173994236)

var node8 = ::blueprint::nodes::load::Load()

node8.var_name = "pos"

_editor.add_node(node8, 91.84085509217, 235.53354023276)

var node9 = ::blueprint::nodes::store::Store()

node9.var_name = "body"

_editor.add_node(node9, 231.24900998812, 36.26305393746)

var node10 = ::scenegraph::nodes::combine::Combine()

_editor.add_node(node10, 92.151729919936, 305.9483460043)

var node11 = ::blueprint::nodes::store::Store()

node11.var_name = "gear"

_editor.add_node(node11, 373.80200319619, 285.10078465833)

var node12 = ::blueprint::nodes::load::Load()

node12.var_name = "gear"

_editor.add_node(node12, -44.36578675426, 41.46590482954)

var node13 = ::blueprint::nodes::load::Load()

node13.var_name = "gear"

_editor.add_node(node13, 606.2254282181, 347.35507145281)

var node14 = ::blueprint::nodes::output::Output()

node14.var_name = "spr"
node14.var_type = "sprite"

_editor.add_node(node14, 736.1632353516, 348.55627246094)

var node15 = ::blueprint::nodes::output::Output()

node15.var_name = "body"
node15.var_type = "body"

_editor.add_node(node15, 734.8583321207, 298.61832762474)

var node16 = ::blueprint::nodes::output::Output()

node16.var_name = "joint"
node16.var_type = "joint"

_editor.add_node(node16, 827.62348386113, -59.097037964997)

var node17 = ::physicsgraph::nodes::fixture::Fixture()

_editor.add_node(node17, -185.33120621611, 342.70814624629)

var node18 = ::blueprint::nodes::store::Store()

node18.var_name = "outer_shape"

_editor.add_node(node18, 86.388742368087, 655.39857621626)

var node19 = ::blueprint::nodes::store::Store()

node19.var_name = "inner_shape"

_editor.add_node(node19, 636.84576426104, 646.84097403635)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "outer_shape"

_editor.add_node(node20, -318.65230489526, 377.78621831789)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "inner_shape"

_editor.add_node(node21, -322.09206106694, 259.60470785443)

var node22 = ::scenegraph::nodes::sprite::Sprite()

_editor.add_node(node22, -50.40894317627, 341.44897460937)

var node23 = ::physicsgraph::nodes::fixture::Fixture()

_editor.add_node(node23, -183.40893554687, 252.44897460937)

var node24 = ::scenegraph::nodes::sprite::Sprite()

_editor.add_node(node24, -51.40894317627, 251.44897460937)

var node25 = ::blueprint::nodes::integer::Integer()

node25.value = 1

_editor.add_node(node25, -322.39441203606, 322.63859293966)

var node26 = ::blueprint::nodes::integer::Integer()

node26.value = 2

_editor.add_node(node26, -323.44588738316, 204.52213970468)

var node27 = ::blueprint::nodes::input::Input()

node27.var_name = "inner_radius"
node27.var_type = "num"

_editor.add_node(node27, 246.33787377958, 731.2143010603)

var node28 = ::blueprint::nodes::input::Input()

node28.var_name = "inner_count"
node28.var_type = "int"

_editor.add_node(node28, 249.09355394284, 667.50570267587)

var node29 = ::blueprint::nodes::input::Input()

node29.var_name = "inner_teeth_size"
node29.var_type = "num"

_editor.add_node(node29, 253.64320465072, 590.23700479928)

var node30 = ::blueprint::nodes::input::Input()

node30.var_name = "inner_color"
node30.var_type = "num3"

_editor.add_node(node30, 254.31448680054, 525.51644304873)

var node31 = ::blueprint::nodes::input::Input()

node31.var_name = "outer_radius"
node31.var_type = "num"

_editor.add_node(node31, -327.83864423553, 748.01511429502)

var node32 = ::blueprint::nodes::input::Input()

node32.var_name = "outer_count"
node32.var_type = "int"

_editor.add_node(node32, -325.97585919799, 681.57651472161)

var node33 = ::blueprint::nodes::input::Input()

node33.var_name = "outer_teeth_size"
node33.var_type = "num"

_editor.add_node(node33, -327.28771986163, 554.68080912075)

var node34 = ::blueprint::nodes::input::Input()

node34.var_name = "outer_color"
node34.var_type = "num3"

_editor.add_node(node34, -327.43212382063, 487.91123063344)

var node35 = ::blueprint::nodes::input::Input()

node35.var_name = "pos"
node35.var_type = "num2"

_editor.add_node(node35, -43.404826747701, 827.20862702543)

var node36 = ::blueprint::nodes::input::Input()

node36.var_name = "ground_body"
node36.var_type = "body"

_editor.add_node(node36, 413.46386098201, 65.543833099426)

var node37 = ::blueprint::nodes::integer::Integer()

node37.value = 2

_editor.add_node(node37, -327.46152505249, 618.75538502999)

var node38 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node38, 727.76005871709, -191.40958934082)

var node39 = ::blueprint::nodes::input::Input()

node39.var_name = "motor"
node39.var_type = "bool"

_editor.add_node(node39, 225.99325614012, -157.07640522795)

var node40 = ::physicsgraph::nodes::revolute_joint::RevoluteJoint()
node40.query_param("limit").value = false
node40.query_param("lower").value = -0.1
node40.query_param("upper").value = 0.1
node40.query_param("motor").value = false
node40.query_param("torque").value = 0
node40.query_param("speed").value = 0

_editor.add_node(node40, 564.10290974295, -323.07700776785)

var node41 = ::blueprint::nodes::load::Load()

node41.var_name = "body"

_editor.add_node(node41, 416.5716378579, -282.66647385529)

Blueprint.connect(node35, "var", node7, "var")
Blueprint.connect(node7, "var", node8, "var")
Blueprint.connect(node31, "var", node6, "radius")
Blueprint.connect(node32, "var", node6, "count")
Blueprint.connect(node37, "v", node6, "teeth_type")
Blueprint.connect(node33, "var", node6, "teeth_size")
Blueprint.connect(node34, "var", node6, "color")
Blueprint.connect(node6, "spr", node18, "var")
Blueprint.connect(node18, "var", node20, "var")
Blueprint.connect(node27, "var", node0, "radius")
Blueprint.connect(node28, "var", node0, "count")
Blueprint.connect(node29, "var", node0, "teeth_size")
Blueprint.connect(node30, "var", node0, "color")
Blueprint.connect(node0, "spr", node19, "var")
Blueprint.connect(node19, "var", node21, "var")
Blueprint.connect(node21, "var", node23, "shape")
Blueprint.connect(node26, "v", node23, "category")
Blueprint.connect(node26, "v", node23, "not_collide")
Blueprint.connect(node23, "fixture", node24, "symbol")
Blueprint.connect(node20, "var", node17, "shape")
Blueprint.connect(node25, "v", node17, "category")
Blueprint.connect(node25, "v", node17, "not_collide")
Blueprint.connect(node17, "fixture", node22, "symbol")
Blueprint.connect(node22, "spr", node10, "child0")
Blueprint.connect(node24, "spr", node10, "child1")
Blueprint.connect(node10, "parent", node1, "spr")
Blueprint.connect(node8, "var", node1, "translate")
Blueprint.connect(node1, "spr", node11, "var")
Blueprint.connect(node11, "var", node13, "var")
Blueprint.connect(node13, "var", node14, "var")
Blueprint.connect(node11, "var", node12, "var")
Blueprint.connect(node12, "var", node2, "geos")
Blueprint.connect(node2, "body", node9, "var")
Blueprint.connect(node9, "var", node41, "var")
Blueprint.connect(node36, "var", node40, "body_a")
Blueprint.connect(node41, "var", node40, "body_b")
Blueprint.connect(node9, "var", node5, "var")
Blueprint.connect(node36, "var", node3, "body_a")
Blueprint.connect(node5, "var", node3, "body_b")
Blueprint.connect(node39, "var", node38, "cond")
Blueprint.connect(node3, "joint", node38, "true")
Blueprint.connect(node40, "joint", node38, "false")
Blueprint.connect(node38, "result", node16, "var")
Blueprint.connect(node9, "var", node4, "var")
Blueprint.connect(node4, "var", node15, "var")
