#pragma once

#include <cstdint>

namespace graph {

enum class EdgeStyle : uint32_t {
    Solid = 0,
    Dashed = 1,
    Dotted = 2,
    DashDot = 3,
    DashDotDot = 4
};

enum class EdgeArrowType : uint32_t {
    None = 0,
    Arrow = 1,
    Diamond = 2,
    Circle = 3,
    Square = 4
};

struct EdgeStyleConfig {
    EdgeStyle style = EdgeStyle::Solid;
    EdgeArrowType start_arrow = EdgeArrowType::None;
    EdgeArrowType end_arrow = EdgeArrowType::Arrow;
    float width = 1.0f;
    uint32_t color = 0xFF000000; // ARGB format
    
    EdgeStyleConfig() = default;
    EdgeStyleConfig(EdgeStyle s, float w = 1.0f, uint32_t c = 0xFF000000)
        : style(s), width(w), color(c) {}
};

class EdgeStyleHelper {
public:
    static const char* GetStyleName(EdgeStyle style);
    static const char* GetArrowTypeName(EdgeArrowType type);
    static EdgeStyle ParseStyle(const char* name);
    static EdgeArrowType ParseArrowType(const char* name);
    
    static EdgeStyleConfig CreateSolidStyle(float width = 1.0f, uint32_t color = 0xFF000000);
    static EdgeStyleConfig CreateDashedStyle(float width = 1.0f, uint32_t color = 0xFF000000);
    static EdgeStyleConfig CreateDottedStyle(float width = 1.0f, uint32_t color = 0xFF000000);
};

} // namespace graph
