#pragma once

#include <memory>
#include <string>
#include <map>

namespace graph
{

class Node;

class Edge
{
public:
	Edge() {}
	Edge(size_t node1, size_t node2)
		: m_node1(node1), m_node2(node2) {}

	// Node access methods
	std::shared_ptr<Node> GetFromNode() const { return m_from_node; }
	std::shared_ptr<Node> GetToNode() const { return m_to_node; }

	void SetFromNode(std::shared_ptr<Node> node) { m_from_node = node; }
	void SetToNode(std::shared_ptr<Node> node) { m_to_node = node; }

	// Component system for storing arbitrary data
	bool HasComponent(const std::string& key) const;
	void SetComponent(const std::string& key, const std::string& value);
	std::string GetComponent(const std::string& key) const;

private:
	size_t m_node1, m_node2;
	std::shared_ptr<Node> m_from_node;
	std::shared_ptr<Node> m_to_node;
	std::map<std::string, std::string> m_components; // Simple string-based component storage

}; // Edge

}