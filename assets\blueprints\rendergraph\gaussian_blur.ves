var node0 = ::rendergraph::nodes::render_target::RenderTarget()

node0.width = 1024
node0.height = 1024

_editor.add_node(node0, -518.08635916386, -110.94944992639)

var node1 = ::rendergraph::nodes::render_target::RenderTarget()

node1.width = 1024
node1.height = 1024

_editor.add_node(node1, -519.08635916386, 85.050550073612)

var node2 = ::rendergraph::nodes::texture::Texture()
node2.query_param("unif_name").value = "u_tex"
node2.gamma_correction = false
node2.init_texture(1024, 1024, "rgba16f")

_editor.add_node(node2, -741.08635916386, 183.05055007361)

var node3 = ::rendergraph::nodes::texture::Texture()
node3.query_param("unif_name").value = "u_tex"
node3.gamma_correction = false
node3.init_texture(1024, 1024, "rgba16f")

_editor.add_node(node3, -737.08635916386, -155.94944992639)

var node4 = ::rendergraph::nodes::draw::Draw()

node4.set_prim_type("tri_strip")
node4.render_state = { "blend" : false, "depth_test" : false, "depth_func" : "less", "cull" : "disable", "clip_plane" : false }
node4.skip = false

_editor.add_node(node4, 354.00457548614, 88.464645445912)

var node5 = ::rendergraph::nodes::shader::Shader()

node5.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoords;

out vec2 TexCoords;

void main()
{
    TexCoords = aTexCoords;
    gl_Position = vec4(aPos, 1.0);
}
"
node5.tcs = ""
node5.tes = ""
node5.gs = ""
node5.fs = "
#version 330 core
out vec4 FragColor;

in vec2 TexCoords;

uniform sampler2D image;

uniform UBO
{
	bool horizontal;
};
const float weight[5] = float[] (0.2270270270, 0.1945945946, 0.1216216216, 0.0540540541, 0.0162162162);

void main()
{             
     vec2 tex_offset = 1.0 / textureSize(image, 0); // gets size of single texel
     vec3 result = texture(image, TexCoords).rgb * weight[0];
     if(horizontal)
     {
         for(int i = 1; i < 5; ++i)
         {
            result += texture(image, TexCoords + vec2(tex_offset.x * i, 0.0)).rgb * weight[i];
            result += texture(image, TexCoords - vec2(tex_offset.x * i, 0.0)).rgb * weight[i];
         }
     }
     else
     {
         for(int i = 1; i < 5; ++i)
         {
             result += texture(image, TexCoords + vec2(0.0, tex_offset.y * i)).rgb * weight[i];
             result += texture(image, TexCoords - vec2(0.0, tex_offset.y * i)).rgb * weight[i];
         }
     }
     FragColor = vec4(result, 1.0);
}
"
node5.cs = ""
node5.render_gen()

_editor.add_node(node5, 121.82353928574, 153.19417395174)

var node6 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node6.type = "quad"
node6.layout = [ "position", "texture" ]
node6.shape_params = {  }

_editor.add_node(node6, 163.76576242049, 22.62740349013)

var node7 = ::blueprint::nodes::array::Array()
node7.query_param("serialize").value = true

node7.list = [ 1, 0, 1, 0, 1, 0, 1, 0, 1, 0 ]

_editor.add_node(node7, -273.44351337606, 167.49047647809)

var node8 = ::blueprint::nodes::input::Input()

node8.var_name = "in0"
node8.var_type = "any"

_editor.add_node(node8, -501.2185341028, 423.14973289002)

var node9 = ::blueprint::nodes::merge::Merge()

_editor.add_node(node9, -331.72924010314, 333.95116452391)

var node10 = ::blueprint::nodes::merge::Merge()

_editor.add_node(node10, -328.56221313477, -14.278495788575)

var node11 = ::blueprint::nodes::output::Output()

node11.var_name = "tex"
node11.var_type = "texture"

_editor.add_node(node11, -566.0532968064, 329.16260623144)

var node12 = ::rendergraph::nodes::pass::Pass()

node12.once = false

_editor.add_node(node12, 521.00667770717, 167.55377219985)

var node13 = ::blueprint::nodes::loop::Loop()

node13.start = 0
node13.end = 10

_editor.add_node(node13, 693.09641900993, 148.51526800776)

var node14 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node14, -90.410966673975, 45.665278158901)

var node15 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node15, -58.548378514426, -124.27532337836)

var node16 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node16, -80.801389413993, 156.56147875648)

Blueprint.connect(node12, "next", node13, "do")
Blueprint.connect(node4, "next", node12, "do")
Blueprint.connect(node15, "item", node12, "fbo")
Blueprint.connect(node7, "all", node16, "items")
Blueprint.connect(node13, "index", node16, "index")
Blueprint.connect(node3, "tex", node0, "col0")
Blueprint.connect(node2, "tex", node11, "var")
Blueprint.connect(node8, "var", node9, "in0")
Blueprint.connect(node3, "tex", node9, "in1")
Blueprint.connect(node2, "tex", node9, "in2")
Blueprint.connect(node3, "tex", node9, "in3")
Blueprint.connect(node2, "tex", node9, "in4")
Blueprint.connect(node3, "tex", node9, "in5")
Blueprint.connect(node2, "tex", node9, "in6")
Blueprint.connect(node3, "tex", node9, "in7")
Blueprint.connect(node2, "tex", node9, "in8")
Blueprint.connect(node3, "tex", node9, "in9")
Blueprint.connect(node9, "list", node14, "items")
Blueprint.connect(node13, "index", node14, "index")
Blueprint.connect(node16, "item", node5, "horizontal")
Blueprint.connect(node14, "item", node5, "image")
Blueprint.connect(node5, "out", node4, "shader")
Blueprint.connect(node6, "out", node4, "va")
Blueprint.connect(node2, "tex", node1, "col0")
Blueprint.connect(node0, "fbo", node10, "in0")
Blueprint.connect(node1, "fbo", node10, "in1")
Blueprint.connect(node0, "fbo", node10, "in2")
Blueprint.connect(node1, "fbo", node10, "in3")
Blueprint.connect(node0, "fbo", node10, "in4")
Blueprint.connect(node1, "fbo", node10, "in5")
Blueprint.connect(node0, "fbo", node10, "in6")
Blueprint.connect(node1, "fbo", node10, "in7")
Blueprint.connect(node0, "fbo", node10, "in8")
Blueprint.connect(node1, "fbo", node10, "in9")
Blueprint.connect(node10, "list", node15, "items")
Blueprint.connect(node13, "index", node15, "index")
