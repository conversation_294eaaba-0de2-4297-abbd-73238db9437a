<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothFabricDesc Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987">ClothFabricDesc</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba">indices</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="group__extensions.html#gb65c431a270115915e78a73c37489dee">isValid</a>() const </td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1">nbParticles</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78">nbPhases</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc">nbSets</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad">nbTethers</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nbTriangles</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f">phases</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd">restvalues</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452">sets</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98">setToDefault</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83">tetherAnchors</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a">tetherLengths</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">triangles</a></td><td><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">nv::cloth::ClothFabricDesc</a></td><td></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
