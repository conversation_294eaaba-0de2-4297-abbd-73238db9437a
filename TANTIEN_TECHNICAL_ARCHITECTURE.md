# Tantien 游戏引擎技术架构详解

## 概述

Tantien 是一个基于**数据驱动**和**可视化编程**的现代游戏引擎，采用**节点式编辑器**作为核心设计理念。引擎通过多个专业编辑器提供完整的游戏开发工具链，从渲染管线到物理模拟，从着色器编程到AI行为树，全部通过直观的节点图形式进行构建。

## 核心架构设计

### 1. 整体架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    编辑器层 (Editor Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  RenderGraph │ ShaderGraph │ TerrainGraph │ PhysicsGraph │...│
├─────────────────────────────────────────────────────────────┤
│                    脚本层 (Script Layer)                     │
├─────────────────────────────────────────────────────────────┤
│                VES虚拟机 (Vessel VM)                        │
├─────────────────────────────────────────────────────────────┤
│                   模块层 (Module Layer)                      │
├─────────────────────────────────────────────────────────────┤
│ Render │Graphics│ Physics │ Model │ Scene │ Shader │ Math │...│
├─────────────────────────────────────────────────────────────┤
│                  渲染抽象层 (UniRender)                       │
├─────────────────────────────────────────────────────────────┤
│                   平台层 (Platform)                          │
└─────────────────────────────────────────────────────────────┘
```

### 2. 设计模式与原则

- **单例模式**: 核心系统管理器 (Render, Graphics, System等)
- **工厂模式**: 设备和上下文创建 (UniRender Factory)
- **观察者模式**: 节点连接和数据流更新
- **组件模式**: 场景节点的组件化设计
- **策略模式**: 不同渲染API的抽象化

## 核心系统详解

### 1. VES脚本系统 (Vessel Virtual Machine)

#### 1.1 虚拟机架构

VES是Tantien的核心脚本引擎，采用基于栈的虚拟机设计：

```cpp
// 虚拟机核心结构
typedef struct {
    CallFrame frames[FRAMES_MAX];
    int frame_count;
    Value stack[STACK_MAX];
    Value* stack_top;
    Table strings;
    Table modules;
    Obj* objects;
    VesselConfiguration config;
} VM;
```

#### 1.2 外部函数绑定机制

```cpp
// 外部方法绑定
VesselForeignMethodFn bind_foreign_method(const char* module, 
                                         const char* className, 
                                         bool isStatic, 
                                         const char* signature) {
    // 统一的方法签名查找
    char fullName[256];
    if (isStatic) strcat(fullName, "static ");
    strcat(fullName, className);
    strcat(fullName, ".");
    strcat(fullName, signature);
    
    // 各模块方法绑定
    method = tt::RenderBindMethod(fullName);
    if (method != NULL) return method;
    // ... 其他模块
}
```

#### 1.3 模块系统

- **动态模块加载**: 支持运行时模块导入
- **模块缓存**: 避免重复编译和加载
- **跨模块通信**: 通过统一的接口进行模块间调用

### 2. 渲染系统 (Render System)

#### 2.1 UniRender抽象层

UniRender提供了跨平台的渲染API抽象：

```cpp
class Device {
public:
    virtual std::shared_ptr<VertexArray> CreateVertexArray() const = 0;
    virtual std::shared_ptr<Framebuffer> CreateFramebuffer() const = 0;
    virtual std::shared_ptr<ShaderProgram> CreateShaderProgram() const = 0;
    // ... 其他创建方法
};

class Context {
public:
    virtual void Clear(const ClearState& clear_state) = 0;
    virtual void Draw(PrimitiveType prim_type, const DrawState& draw, 
                     const void* scene) = 0;
    virtual void Compute(const DrawState& draw, int num_groups_x, 
                        int num_groups_y, int num_groups_z) = 0;
};
```

#### 2.2 渲染状态管理

```cpp
struct DrawState {
    std::shared_ptr<ShaderProgram> program;
    std::shared_ptr<VertexArray> vertex_array;
    RenderState render_state;
    std::shared_ptr<DescriptorSet> desc_set;
    // ... 其他状态
};
```

#### 2.3 着色器系统

- **SPIR-V支持**: 使用SPIR-V作为中间表示
- **多语言支持**: GLSL和HLSL编译到SPIR-V
- **反射系统**: 自动提取uniform变量信息
- **热重载**: 支持着色器的实时编辑和重载

### 3. 节点系统 (Node System)

#### 3.1 节点基类设计

```cpp
class Node {
public:
    std::vector<Pin> imports;   // 输入引脚
    std::vector<Pin> exports;   // 输出引脚
    Style style;                // 视觉样式
    Vector2 pos;               // 节点位置
    
    virtual void update(Matrix mt);
    virtual void draw(Matrix mt, Matrix cam_mt);
    virtual Variant calc_value(int idx);  // 计算输出值
};
```

#### 3.2 数据流处理

```cpp
class Blueprint {
public:
    // 拓扑排序确保正确的计算顺序
    static std::vector<Node*> topo_sort(const std::vector<Node*>& nodes);
    
    // 连接管理
    static Connection* connect(Pin* from, Pin* to);
    static void disconnect(Connection* conn);
    
    // 值计算
    static Variant calc_input_value(Node* node, int input_slot);
    static Variant calc_output_value(Node* node, int output_slot);
};
```

#### 3.3 引脚连接机制

```cpp
class Pin {
public:
    Node* node;
    std::string name;
    VarType type;
    std::set<Connection*> conns;  // 连接集合
    
    bool is_input;
    int slot_idx;
    Color color;
};

class Connection {
public:
    Pin* from;
    Pin* to;
    Bezier curve;  // 贝塞尔曲线表示
    
    void update();  // 更新曲线形状
};
```

### 4. 编辑器系统

#### 4.1 多编辑器架构

每个编辑器都是独立的模块，但共享相同的基础设施：

- **Blueprint**: 基础节点编辑器
- **RenderGraph**: 渲染管线编辑器
- **ShaderGraph**: 着色器节点编辑器
- **TerrainGraph**: 地形生成编辑器
- **PhysicsGraph**: 物理模拟编辑器
- **等等...**

#### 4.2 编辑器通用功能

```cpp
class Editor {
protected:
    Camera camera;              // 视图相机
    std::vector<Node*> nodes;   // 节点列表
    std::set<Node*> selection;  // 选中节点
    
public:
    virtual void update();
    virtual void draw();
    virtual void on_mouse_event(MouseEvent& event);
    virtual void on_keyboard_event(KeyboardEvent& event);
};
```

## 专业编辑器详解

### 1. RenderGraph (渲染图编辑器)

#### 功能特性
- **可视化渲染管线**: 通过节点构建复杂的渲染流程
- **自动uniform生成**: 根据着色器自动生成uniform接口
- **实时预览**: 所见即所得的渲染效果
- **可操作节点**: 支持3D相机控制等交互节点

#### 核心节点类型
```cpp
// 顶点数据节点
class Model : public Node {
    // 加载和管理3D模型
};

// 着色器节点
class Shader : public Node {
    // 编译和管理着色器程序
};

// 渲染操作节点
class Draw : public Node {
    // 执行绘制调用
};
```

### 2. ShaderGraph (着色器编辑器)

#### 技术特点
- **SPIR-V AST**: 使用SPIR-V抽象语法树而非字符串拼接
- **类型安全**: 编译时类型检查
- **优化**: SPIR-V级别的优化

#### 节点示例
```cpp
class SampleTexture : public Node {
    // 纹理采样节点
    Variant calc_value(int idx) override {
        auto texture = calc_input_value(0);
        auto uv = calc_input_value(1);
        // 生成SPIR-V采样指令
        return sample_texture_spirv(texture, uv);
    }
};
```

### 3. TerrainGraph (地形编辑器)

#### 算法支持
- **噪声生成**: Perlin、Worley等多种噪声算法
- **侵蚀模拟**: 水力侵蚀、热力风化
- **实时编辑**: 画刷工具支持实时地形雕刻

### 4. PhysicsGraph (物理编辑器)

#### 物理引擎集成
- **2D物理**: 基于Box2D的2D物理模拟
- **约束系统**: 各种关节和约束
- **事件系统**: 碰撞检测和响应

## 第三方库集成

### 1. 图形和渲染
- **OpenGL**: 主要渲染API
- **GLFW**: 窗口和输入管理
- **gl3w**: OpenGL加载器

### 2. 数学和几何
- **Eigen**: 线性代数库
- **CGAL**: 计算几何算法库

### 3. 资源加载
- **Assimp**: 3D模型加载
- **stb_image**: 图像加载
- **TinyGLTF**: glTF格式支持

### 4. 物理模拟
- **Box2D**: 2D物理引擎
- **Bullet**: 3D物理引擎 (部分功能)

### 5. 着色器工具
- **SPIRV-Tools**: SPIR-V处理工具链
- **SPIRV-Cross**: SPIR-V交叉编译
- **glslang**: GLSL编译器

## 性能优化策略

### 1. 渲染优化
- **批处理**: 相同材质的对象批量渲染
- **视锥剔除**: 只渲染可见对象
- **LOD系统**: 距离相关的细节层次

### 2. 内存管理
- **对象池**: 频繁创建销毁的对象使用对象池
- **智能指针**: 使用shared_ptr管理资源生命周期
- **垃圾回收**: VES虚拟机的标记-清除GC

### 3. 计算优化
- **缓存机制**: 节点计算结果缓存
- **惰性求值**: 只在需要时计算节点值
- **并行计算**: 支持多线程和GPU计算

## 扩展性设计

### 1. 插件系统
- **模块化**: 每个功能都是独立模块
- **热插拔**: 支持运行时加载/卸载模块
- **API统一**: 统一的插件接口

### 2. 自定义节点
- **脚本节点**: 通过VES脚本创建自定义节点
- **C++扩展**: 原生C++节点扩展
- **子图**: 将节点组合封装为可复用的子图

### 3. 平台适配
- **抽象层**: 通过UniRender抽象不同平台
- **条件编译**: 平台特定代码的条件编译
- **配置系统**: 运行时配置不同平台参数

## 开发工具链

### 1. 构建系统
- **Visual Studio**: 主要开发环境
- **MSBuild**: 构建系统
- **Git Submodules**: 第三方库管理

### 2. 调试工具
- **节点调试**: 可视化节点执行状态
- **性能分析**: 内置性能分析器
- **日志系统**: 分级日志输出

### 3. 资源管理
- **资源缓存**: 智能资源缓存机制
- **热重载**: 资源文件的实时重载
- **版本控制**: 资源版本管理

## 数据流和消息传递

### 1. 节点间通信机制

#### 1.1 数据流模型
```cpp
// 数据流向: 输出节点 -> 连接 -> 输入节点
class DataFlow {
    static void propagate_dirty(Node* node, DirtyType type) {
        // 标记下游节点为脏数据
        for (auto& export_pin : node->exports) {
            for (auto& conn : export_pin.conns) {
                conn->to->node->on_pin_dirty(conn->to, type, nullptr);
            }
        }
    }
};
```

#### 1.2 消息时间戳系统
```cpp
class Node {
    int dirty_timestamp = 0;    // 脏数据时间戳
    int msg_timestamp = 0;      // 消息时间戳

    void on_pin_dirty(Pin* pin, DirtyType type, void* msg) {
        if (type == DIRTY_CONNECTION) {
            // 连接变化，清除缓存
            clear_cache();
        }
        // 更新时间戳，触发重新计算
        dirty_timestamp = get_global_timestamp();
    }
};
```

### 2. 场景图系统

#### 2.1 组件化实体系统
```cpp
class SceneNode {
    std::map<std::string, Component*> components;

    void update() {
        Matrix transform_matrix;
        auto transform = components["transform"];
        if (transform) {
            transform_matrix = transform->get_matrix();
        }

        // 更新所有组件
        for (auto& [name, component] : components) {
            component->update(transform_matrix);
        }
    }
};
```

#### 2.2 层次结构管理
```cpp
class TreeNode {
    TreeNode* parent = nullptr;
    std::vector<TreeNode*> children;

    void update_hierarchy() {
        // 更新父子关系的边连接
        if (parent && parent->edge) {
            parent->edge->update();
        }
        for (auto child : children) {
            if (child->edge) {
                child->edge->update();
            }
        }
    }
};
```

## 着色器编译系统

### 1. SPIR-V工作流

#### 1.1 多语言编译支持
```cpp
std::vector<uint32_t> compile_shader(const std::string& source,
                                     ShaderStage stage,
                                     const std::string& language,
                                     const std::string& entry_point) {
    std::vector<uint32_t> spirv;

    if (language == "glsl") {
        // GLSL -> SPIR-V
        auto preprocessed = ShaderPreprocess::PrepareGLSL(stage, source);
        ShaderTrans::GLSL2SpirV(stage, preprocessed, include_dir, spirv, false);
    } else if (language == "hlsl") {
        // HLSL -> SPIR-V
        ShaderTrans::HLSL2SpirV(stage, source, entry_point, spirv);
    }

    return spirv;
}
```

#### 1.2 着色器反射
```cpp
struct ShaderReflection {
    struct Variable {
        std::string name;
        UniformType type;
        int location;
        int binding;
    };

    static void GetUniforms(const std::vector<uint32_t>& spirv,
                           std::vector<Variable>& uniforms) {
        // 解析SPIR-V字节码，提取uniform信息
        spirv_cross::Compiler compiler(spirv);
        auto resources = compiler.get_shader_resources();

        // 处理uniform buffers
        for (auto& resource : resources.uniform_buffers) {
            // 提取uniform变量信息
        }
    }
};
```

### 2. 动态着色器生成

#### 2.1 节点图到SPIR-V
```cpp
class ShaderGraphCompiler {
    std::vector<uint32_t> compile_to_spirv(const std::vector<Node*>& nodes) {
        SpirVBuilder builder;

        // 拓扑排序节点
        auto sorted_nodes = Blueprint::topo_sort(nodes);

        // 为每个节点生成SPIR-V指令
        for (auto node : sorted_nodes) {
            node->generate_spirv(builder);
        }

        return builder.finalize();
    }
};
```

## 物理系统集成

### 1. 2D物理 (Box2D)

#### 1.1 物理世界管理
```cpp
class Physics2D {
    b2World* world;

    void step(float dt) {
        // Box2D物理步进
        world->Step(dt, velocity_iterations, position_iterations);

        // 同步物理对象到渲染对象
        sync_physics_to_render();
    }

    void sync_physics_to_render() {
        for (auto body = world->GetBodyList(); body; body = body->GetNext()) {
            auto user_data = body->GetUserData();
            if (user_data) {
                // 更新渲染对象的位置和旋转
                auto render_obj = static_cast<RenderObject*>(user_data);
                render_obj->set_transform(body->GetTransform());
            }
        }
    }
};
```

### 2. 约束和关节系统

#### 2.1 关节节点
```cpp
class JointNode : public Node {
    b2Joint* joint = nullptr;

    void create_joint(b2Body* bodyA, b2Body* bodyB) {
        b2RevoluteJointDef jointDef;
        jointDef.bodyA = bodyA;
        jointDef.bodyB = bodyB;
        jointDef.localAnchorA.Set(0, 0);
        jointDef.localAnchorB.Set(0, 0);

        joint = world->CreateJoint(&jointDef);
    }
};
```

## 资源管理系统

### 1. 资源加载器

#### 1.1 异步加载
```cpp
class ResourceManager {
    std::unordered_map<std::string, std::shared_ptr<Resource>> cache;
    ThreadPool loader_pool;

    template<typename T>
    std::future<std::shared_ptr<T>> load_async(const std::string& path) {
        return loader_pool.enqueue([this, path]() {
            return load_resource<T>(path);
        });
    }

    template<typename T>
    std::shared_ptr<T> load_resource(const std::string& path) {
        auto it = cache.find(path);
        if (it != cache.end()) {
            return std::static_pointer_cast<T>(it->second);
        }

        auto resource = T::load_from_file(path);
        cache[path] = resource;
        return resource;
    }
};
```

### 2. 热重载系统

#### 2.1 文件监控
```cpp
class FileWatcher {
    std::unordered_map<std::string, FileWatchCallback> watchers;

    void watch_file(const std::string& path, FileWatchCallback callback) {
        watchers[path] = callback;
        // 启动文件系统监控
        start_watching(path);
    }

    void on_file_changed(const std::string& path) {
        auto it = watchers.find(path);
        if (it != watchers.end()) {
            it->second(path);  // 触发回调
        }
    }
};
```

## 内存管理和性能优化

### 1. 对象池系统

#### 1.1 通用对象池
```cpp
template<typename T>
class ObjectPool {
    std::vector<std::unique_ptr<T>> pool;
    std::queue<T*> available;

public:
    T* acquire() {
        if (available.empty()) {
            pool.push_back(std::make_unique<T>());
            return pool.back().get();
        }

        T* obj = available.front();
        available.pop();
        return obj;
    }

    void release(T* obj) {
        obj->reset();  // 重置对象状态
        available.push(obj);
    }
};
```

### 2. 渲染批处理

#### 2.1 批处理管理器
```cpp
class BatchRenderer {
    struct Batch {
        std::shared_ptr<ShaderProgram> shader;
        std::shared_ptr<Texture> texture;
        std::vector<Vertex> vertices;
        std::vector<uint32_t> indices;
    };

    std::vector<Batch> batches;

    void add_sprite(const Sprite& sprite) {
        // 查找或创建合适的批次
        auto batch = find_or_create_batch(sprite.shader, sprite.texture);

        // 添加顶点数据到批次
        batch->vertices.insert(batch->vertices.end(),
                              sprite.vertices.begin(),
                              sprite.vertices.end());
    }

    void flush() {
        for (auto& batch : batches) {
            // 上传顶点数据并绘制
            upload_and_draw(batch);
        }
        batches.clear();
    }
};
```

## 调试和分析工具

### 1. 性能分析器

#### 1.1 分层性能统计
```cpp
class Profiler {
    struct ProfileBlock {
        std::string name;
        double start_time;
        double total_time;
        int call_count;
    };

    std::unordered_map<std::string, ProfileBlock> blocks;

public:
    class ScopedTimer {
        Profiler* profiler;
        std::string block_name;
        double start_time;

    public:
        ScopedTimer(Profiler* p, const std::string& name)
            : profiler(p), block_name(name) {
            start_time = get_current_time();
        }

        ~ScopedTimer() {
            double elapsed = get_current_time() - start_time;
            profiler->add_sample(block_name, elapsed);
        }
    };

    void add_sample(const std::string& name, double time) {
        auto& block = blocks[name];
        block.name = name;
        block.total_time += time;
        block.call_count++;
    }
};

#define PROFILE_SCOPE(profiler, name) \
    Profiler::ScopedTimer timer(profiler, name)
```

### 2. 节点调试系统

#### 2.1 可视化调试信息
```cpp
class NodeDebugger {
    void draw_debug_info(Node* node, const Matrix& cam_matrix) {
        // 绘制节点计算次数
        draw_text(node->pos.x, node->pos.y - 20,
                 "Calc: " + std::to_string(node->calc_times));

        // 绘制节点渲染次数
        draw_text(node->pos.x, node->pos.y - 35,
                 "Render: " + std::to_string(node->render_times));

        // 绘制数据流状态
        for (auto& pin : node->exports) {
            if (!pin.conns.empty()) {
                draw_data_flow_arrow(pin, cam_matrix);
            }
        }
    }
};
```

## 总结

Tantien引擎通过其独特的节点式可视化编程范式，为游戏开发提供了一个强大而灵活的工具集。其模块化的架构设计、完善的抽象层次和丰富的专业编辑器，使得从技术美术到程序员都能高效地进行游戏开发工作。引擎的开源特性也为社区贡献和定制化开发提供了良好的基础。

### 技术亮点

1. **统一的节点系统**: 所有功能都通过节点图形式实现，提供一致的用户体验
2. **强大的脚本引擎**: VES虚拟机提供高性能的脚本执行环境
3. **现代渲染架构**: 基于SPIR-V的着色器系统和抽象化的渲染API
4. **实时编辑能力**: 支持热重载和实时预览的开发工作流
5. **可扩展设计**: 模块化架构支持自定义节点和插件开发

### 应用场景

- **技术美术工作流**: 着色器开发、材质制作、特效设计
- **关卡设计**: 地形编辑、场景布置、光照设计
- **游戏逻辑**: AI行为树、状态机、事件系统
- **渲染管线**: 自定义渲染效果、后处理链
- **工具开发**: 基于节点的专业工具制作
