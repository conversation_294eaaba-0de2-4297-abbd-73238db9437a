#pragma once

#include <memory>
#include <unordered_map>
#include <vector>
#include <cstdint>
#include <string>

namespace tt
{

class EntityMap
{
public:
    struct Entity
    {
        uint64_t id;
        std::string name;
        std::string type;
        // Additional entity properties can be added here
        
        Entity() : id(0) {}
        Entity(uint64_t _id, const std::string& _name, const std::string& _type)
            : id(_id), name(_name), type(_type) {}
    };

public:
    EntityMap();
    ~EntityMap();
    
    void AddEntity(uint64_t id, const std::shared_ptr<Entity>& entity);
    void RemoveEntity(uint64_t id);
    std::shared_ptr<Entity> GetEntity(uint64_t id) const;
    
    std::vector<uint64_t> GetAllEntityIds() const;
    size_t GetEntityCount() const;
    void Clear();
    bool HasEntity(uint64_t id) const;

private:
    std::unordered_map<uint64_t, std::shared_ptr<Entity>> m_entities;
};

} // namespace tt
