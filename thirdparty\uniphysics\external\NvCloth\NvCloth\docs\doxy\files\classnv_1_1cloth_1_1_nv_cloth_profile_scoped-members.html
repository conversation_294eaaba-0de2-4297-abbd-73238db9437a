<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::NvClothProfileScoped Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#561c535d463cb4ef349db1b13b52761b">mCallback</a></td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#dbbaf92e01e8d8cd7c2a80242a60c5a3">mContextId</a></td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#3a008841539b432550e139510d84d987">mDetached</a></td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#e4bae4fa99fd522f226143f9b7f8a0fb">mEventName</a></td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#45731a72d188fd61afab53f65793ae6b">mProfilerData</a></td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#eb4cd36d11aa03f05d05a6f18f8b41cb">NvClothProfileScoped</a>(const char *eventName, bool detached, uint64_t contextId, const char *fileName, int lineno, physx::PxProfilerCallback *callback)</td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td><code> [inline]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html#803ffc5af708346f086454ca7c6948d9">~NvClothProfileScoped</a>(void)</td><td><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td><td><code> [inline]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
