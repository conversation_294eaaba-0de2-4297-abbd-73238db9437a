<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <!-- CAX external dependencies commented out - files do not exist -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\BrepTopo.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\CompGraph.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\comp_nodes.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\HistGraph.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\TopoGraphBuilder.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\breptopo\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\breptopo\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\TopoGraph.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\breptopo_c\wrap_BrepTopo.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\nurbslib\wrap_NurbsLib.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\BRepBuilder.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\BRepHistory.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\BRepSelector.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\BRepTools.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\PrimMaker.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\TopoAdapter.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\TopoAlgo.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\TransHelper.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\partgraph\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\cax\partgraph_c\wrap_PartGraph.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\sketchlib\Scene.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)/sketchlib/</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)/sketchlib/</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\cax\sketchlib\wrap_SketchLib.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\thirdparty\PlaneGCS\Constraints.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\thirdparty\PlaneGCS\GCS.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\thirdparty\PlaneGCS\Geo.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\thirdparty\PlaneGCS\qp_eq.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\cax\thirdparty\PlaneGCS\SubSystem.cpp" /> -->
    <!-- LITTLEWORLD external dependencies commented out - files do not exist -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\MeshBuilder.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\archgen\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\archgen\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\PolytopeTools.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\RoofEditor.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\RoofSkeleton.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\ScopeTools.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\archgen\wrap_ArchGen.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Chaikin.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Extrude.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Graph.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\citygen\</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\citygen\</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\KMeans.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Math.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\ParcelsOBB.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\ParcelsSS.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\PolyBuilder.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Reshape.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\RotatingCalipers.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\StraightSkeleton.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\Streets.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\TensorField.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\citygen\wrap_CityGen.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\globegen\ImageTools.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\globegen\ShapeBatching.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\globegen\VirtualTexture.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\globegen\VTexBuilder.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\littleworld\globegen\wrap_GlobeGen.cpp" /> -->
    <!-- TT analysis external dependencies commented out - files do not exist -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\BasicBlock.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\BBlockBuilder.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\AstToCfg.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\BBlockConnect.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\BBlockTools.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\NodeExpand.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\AstNode.cpp"> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)/codegraph/</ObjectFileName> -->
    <!--   <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)/codegraph/</ObjectFileName> -->
    <!-- </ClCompile> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\AstNode.h" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\NodeName.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\NodePrint.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\NodeRename.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\NodeTraversal.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\VarAnalysis.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\codegraph_c\wrap_CodeGraph.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\Diff.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\LogParser.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\Node.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\NodeAdapter.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\ProtoParser.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\Regex.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\Traceback.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\Variant.cpp" /> -->
    <!-- <ClCompile Include="..\..\..\..\tt\analysis\loggraph_c\wrap_LogGraph.cpp" /> -->
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\src\Mesh.cpp" />
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\src\Scene.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)/pathtracer/</ObjectFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)/pathtracer/</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\src\wrap_PathTracer.cpp" />
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bbox.cpp" />
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bvh.cpp" />
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bvh_translator.cpp" />
    <ClCompile Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\split_bvh.cpp" />
    <ClCompile Include="..\..\..\include\tantien.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\BRepDiff.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\BRepSerialize.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\db\</ObjectFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\db\</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\src\modules\db\DB.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\PickVisitor.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\RegionVisitor.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\RTreeBuilder.cpp">
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(IntDir)\db\</ObjectFileName>
      <ObjectFileName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(IntDir)\db\</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\..\..\src\modules\db\EntityMap.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\RTreeUpdate.cpp" />
    <ClCompile Include="..\..\..\src\modules\db\wrap_DB.cpp" />
    <ClCompile Include="..\..\..\src\modules\filesystem\Filesystem.cpp" />
    <ClCompile Include="..\..\..\src\modules\filesystem\ResourceUID.cpp" />
    <ClCompile Include="..\..\..\src\modules\filesystem\wrap_Filesystem.cpp" />
    <ClCompile Include="..\..\..\src\modules\geometry\ShapeMaths.cpp" />
    <ClCompile Include="..\..\..\src\modules\geometry\wrap_Geometry.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\DTex.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\Graphics.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\GTxt.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\LoadingList.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\SpriteRenderer.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\Viewport.cpp" />
    <ClCompile Include="..\..\..\src\modules\graphics\wrap_Graphics.cpp" />
    <ClCompile Include="..\..\..\src\modules\graph\Graph.cpp" />
    <ClCompile Include="..\..\..\src\modules\graph\wrap_Graph.cpp" />
    <ClCompile Include="..\..\..\src\modules\gui\GUI.cpp" />
    <ClCompile Include="..\..\..\src\modules\gui\wrap_GUI.cpp" />
    <ClCompile Include="..\..\..\src\modules\image\wrap_Image.cpp" />
    <ClCompile Include="..\..\..\src\modules\io\Keyboard.cpp" />
    <ClCompile Include="..\..\..\src\modules\io\wrap_Keyboard.cpp" />
    <ClCompile Include="..\..\..\src\modules\maths\float16.cpp" />
    <ClCompile Include="..\..\..\src\modules\maths\wrap_Maths.cpp" />
    <ClCompile Include="..\..\..\src\modules\model\Model.cpp" />
    <ClCompile Include="..\..\..\src\modules\model\wrap_Model.cpp" />
    <ClCompile Include="..\..\..\src\modules\om\wrap_OM.cpp" />
    <ClCompile Include="..\..\..\src\modules\physics\Physics.cpp" />
    <ClCompile Include="..\..\..\src\modules\physics\wrap_Physics.cpp" />
    <ClCompile Include="..\..\..\src\modules\regen\PolyDiff.cpp" />
    <ClCompile Include="..\..\..\src\modules\regen\wrap_Regen.cpp" />
    <ClCompile Include="..\..\..\src\modules\render\Render.cpp" />
    <ClCompile Include="..\..\..\src\modules\render\wrap_Render.cpp" />
    <ClCompile Include="..\..\..\src\modules\scene\SceneTree.cpp" />
    <ClCompile Include="..\..\..\src\modules\scene\wrap_Scene.cpp" />
    <ClCompile Include="..\..\..\src\modules\script\TransHelper.cpp" />
    <ClCompile Include="..\..\..\src\modules\shader\wrap_Shader.cpp" />
    <ClCompile Include="..\..\..\src\modules\system\System.cpp" />
    <ClCompile Include="..\..\..\src\modules\system\wrap_System.cpp" />
    <ClCompile Include="..\..\..\src\modules\vm\wrap_VM.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\..\..\cax\breptopo_c\BrepTopo.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\CompGraph.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\CompNode.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\CompVariant.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\comp_nodes.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\comp_variants.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\HistGraph.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\NodeComp.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\NodeFlags.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\NodeId.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\TopoGraph.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\GraphShape.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\NodeShape.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\TopoGraphBuilder.h" />
    <ClInclude Include="..\..\..\..\cax\breptopo_c\wrap_BrepTopo.h" />
    <ClInclude Include="..\..\..\..\cax\nurbslib\wrap_NurbsLib.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\BRepBuilder.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\BRepHistory.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\BRepSelector.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\BRepTools.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\GeomDataset.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\occt_adapter.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\PrimMaker.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\TopoAdapter.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\TopoAlgo.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\TopoDataset.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\TransHelper.h" />
    <ClInclude Include="..\..\..\..\cax\partgraph_c\wrap_PartGraph.h" />
    <ClInclude Include="..\..\..\..\cax\sketchlib\Constraint.h" />
    <ClInclude Include="..\..\..\..\cax\sketchlib\Geometry.h" />
    <ClInclude Include="..\..\..\..\cax\sketchlib\Scene.h" />
    <ClInclude Include="..\..\..\..\cax\sketchlib\Util.h" />
    <ClInclude Include="..\..\..\..\cax\sketchlib\wrap_SketchLib.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\Constraints.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\GCS.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\Geo.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\qp_eq.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\SubSystem.h" />
    <ClInclude Include="..\..\..\..\cax\thirdparty\PlaneGCS\Util.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\MeshBuilder.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\PolytopeTools.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\RoofEditor.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\RoofSkeleton.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\ScopeTools.h" />
    <ClInclude Include="..\..\..\..\littleworld\archgen\wrap_ArchGen.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Chaikin.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Extrude.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Graph.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\KMeans.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Math.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\ParcelsOBB.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\ParcelsSS.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\PolyBuilder.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Random.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Reshape.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\RotatingCalipers.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\StraightSkeleton.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\Streets.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\TensorField.h" />
    <ClInclude Include="..\..\..\..\littleworld\citygen\wrap_CityGen.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\ImageTools.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\ShapeBatching.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\VirtualTexture.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\VTexBuilder.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\VTexInfo.h" />
    <ClInclude Include="..\..\..\..\littleworld\globegen\wrap_GlobeGen.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\AstNodeImpl.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\BasicBlock.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\BBlockBuilder.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\AstToCfg.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\BBlockConnect.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\BBlockTools.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\FuncContext.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\NodeTraversal.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\VarAnalysis.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\codegraph_c\wrap_CodeGraph.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\Diff.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\LogParser.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\Node.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\NodeAdapter.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\ProtoParser.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\Regex.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\Traceback.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\Variant.h" />
    <ClInclude Include="..\..\..\..\tt\analysis\loggraph_c\wrap_LogGraph.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\src\Light.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\src\Material.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\src\Mesh.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\src\Scene.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\src\wrap_PathTracer.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bbox.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bvh.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\bvh_translator.h" />
    <ClInclude Include="..\..\..\..\tt_sim\pathtracer\thirdparty\RadeonRays\split_bvh.h" />
    <ClInclude Include="..\..\..\include\tantien.h" />
    <ClInclude Include="..\..\..\src\core\macro.h" />
    <ClInclude Include="..\..\..\src\modules\db\BRepDiff.h" />
    <ClInclude Include="..\..\..\src\modules\db\BRepKey.h" />
    <ClInclude Include="..\..\..\src\modules\db\BRepSerialize.h" />
    <ClInclude Include="..\..\..\src\modules\db\DB.h" />
    <ClInclude Include="..\..\..\src\modules\db\EntityID.h" />
    <ClInclude Include="..\..\..\src\modules\db\PickVisitor.h" />
    <ClInclude Include="..\..\..\src\modules\db\RegionVisitor.h" />
    <ClInclude Include="..\..\..\src\modules\db\RTreeBuilder.h" />
    <ClInclude Include="..\..\..\src\modules\db\EntityMap.h" />
    <ClInclude Include="..\..\..\src\modules\db\RTreeUpdate.h" />
    <ClInclude Include="..\..\..\src\modules\db\wrap_DB.h" />
    <ClInclude Include="..\..\..\src\modules\filesystem\Filesystem.h" />
    <ClInclude Include="..\..\..\src\modules\filesystem\ResourceUID.h" />
    <ClInclude Include="..\..\..\src\modules\filesystem\wrap_Filesystem.h" />
    <ClInclude Include="..\..\..\src\modules\geometry\ShapeMaths.h" />
    <ClInclude Include="..\..\..\src\modules\geometry\wrap_Geometry.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\DTex.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\Graphics.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\GTxt.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\LoadingList.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\RenderBuffer.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\SpriteRenderer.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\Viewport.h" />
    <ClInclude Include="..\..\..\src\modules\graphics\wrap_Graphics.h" />
    <ClInclude Include="..\..\..\src\modules\graph\Graph.h" />
    <ClInclude Include="..\..\..\src\modules\graph\wrap_Graph.h" />
    <ClInclude Include="..\..\..\src\modules\gui\GUI.h" />
    <ClInclude Include="..\..\..\src\modules\gui\wrap_GUI.h" />
    <ClInclude Include="..\..\..\src\modules\image\ImageData.h" />
    <ClInclude Include="..\..\..\src\modules\image\wrap_Image.h" />
    <ClInclude Include="..\..\..\src\modules\io\Keyboard.h" />
    <ClInclude Include="..\..\..\src\modules\io\wrap_Keyboard.h" />
    <ClInclude Include="..\..\..\src\modules\maths\float16.h" />
    <ClInclude Include="..\..\..\src\modules\maths\wrap_Maths.h" />
    <ClInclude Include="..\..\..\src\modules\model\Model.h" />
    <ClInclude Include="..\..\..\src\modules\model\wrap_Model.h" />
    <ClInclude Include="..\..\..\src\modules\om\wrap_OM.h" />
    <ClInclude Include="..\..\..\src\modules\physics\Physics.h" />
    <ClInclude Include="..\..\..\src\modules\physics\wrap_Physics.h" />
    <ClInclude Include="..\..\..\src\modules\regen\PolyDiff.h" />
    <ClInclude Include="..\..\..\src\modules\regen\wrap_Regen.h" />
    <ClInclude Include="..\..\..\src\modules\render\Render.h" />
    <ClInclude Include="..\..\..\src\modules\render\wrap_Render.h" />
    <ClInclude Include="..\..\..\src\modules\scene\SceneTree.h" />
    <ClInclude Include="..\..\..\src\modules\scene\wrap_Scene.h" />
    <ClInclude Include="..\..\..\src\modules\script\Proxy.h" />
    <ClInclude Include="..\..\..\src\modules\script\TransHelper.h" />
    <ClInclude Include="..\..\..\src\modules\shader\wrap_Shader.h" />
    <ClInclude Include="..\..\..\src\modules\system\System.h" />
    <ClInclude Include="..\..\..\src\modules\system\wrap_System.h" />
    <ClInclude Include="..\..\..\src\modules\vm\wrap_VM.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\..\..\..\cax\breptopo_c\breptopo.ves.inc" />
    <None Include="..\..\..\..\cax\nurbslib\nurbslib.ves.inc" />
    <None Include="..\..\..\..\cax\partgraph_c\partgraph.ves.inc" />
    <None Include="..\..\..\..\cax\sketchlib\sketchlib.ves.inc" />
    <None Include="..\..\..\..\littleworld\archgen\archgen.ves.inc" />
    <None Include="..\..\..\..\littleworld\citygen\citygen.ves.inc" />
    <None Include="..\..\..\..\littleworld\globegen\globegen.ves.inc" />
    <None Include="..\..\..\..\littleworld\globegen\ImageTools.inl" />
    <None Include="..\..\..\..\tt\analysis\codegraph_c\codegraph.ves.inc" />
    <None Include="..\..\..\..\tt\analysis\loggraph_c\loggraph.ves.inc" />
    <None Include="..\..\..\..\tt_sim\pathtracer\src\pathtracer.ves.inc" />
    <None Include="..\..\..\src\modules\db\db.ves.inc" />
    <None Include="..\..\..\src\modules\filesystem\filesystem.ves.inc" />
    <None Include="..\..\..\src\modules\geometry\geometry.ves.inc" />
    <None Include="..\..\..\src\modules\graphics\graphics.ves.inc" />
    <None Include="..\..\..\src\modules\graph\graph.ves.inc" />
    <None Include="..\..\..\src\modules\gui\gui.ves.inc" />
    <None Include="..\..\..\src\modules\image\image.ves.inc" />
    <None Include="..\..\..\src\modules\io\keyboard.ves.inc" />
    <None Include="..\..\..\src\modules\maths\maths.ves.inc" />
    <None Include="..\..\..\src\modules\model\model.ves.inc" />
    <None Include="..\..\..\src\modules\om\om.ves.inc" />
    <None Include="..\..\..\src\modules\physics\physics.ves.inc" />
    <None Include="..\..\..\src\modules\regen\regen.ves.inc" />
    <None Include="..\..\..\src\modules\render\render.ves.inc" />
    <None Include="..\..\..\src\modules\scene\scene.ves.inc" />
    <None Include="..\..\..\src\modules\script\TransHelper.inl" />
    <None Include="..\..\..\src\modules\shader\shader.ves.inc" />
    <None Include="..\..\..\src\modules\system\system.ves.inc" />
    <None Include="..\..\..\src\modules\vm\vm.ves.inc" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{8D617A32-04C2-4B1C-B932-60EAB5CCBA04}</ProjectGuid>
    <RootNamespace>tantien</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>..\tantien\x86\Debug\</OutDir>
    <IntDir>..\tantien\x86\Debug\obj\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>..\tantien\x86\Debug\</OutDir>
    <IntDir>..\tantien\x86\Debug\obj\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)$(Platform)\$(Configuration)\</OutDir>
    <IntDir>$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;NO_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../thirdparty/vessel/src/include;../../../thirdparty/unirender/include;../../../thirdparty/shadertrans/include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;NO_BOOST;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../thirdparty/vessel/src/include;../../../thirdparty/unirender/include;../../../thirdparty/shadertrans/include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;NO_BOOST;NO_RTTR;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../src;../../../thirdparty;../../../thirdparty/vessel/src/include;../../../thirdparty/unirender/include;../../../thirdparty/shadertrans/include;../../../thirdparty/shaderlink/include;../../../thirdparty/tessellation/include;../../../thirdparty/dtex/include;../../../thirdparty/geoshape/include;../../../thirdparty/primitive/include;../../../thirdparty/easygui/include;../../../thirdparty/model/include;../../../thirdparty/guard/include;../../../thirdparty/sm;../../../thirdparty/gtxt;../../../thirdparty/gimg;../../../thirdparty/glslang;../../../thirdparty/glfw/include;../../../thirdparty/uniphysics/include;../../../thirdparty/constraints2/include;../../../thirdparty/polymesh3/include;../../../thirdparty/halfedge/include;../../../thirdparty/CGAL-5.3.1/include;../../../thirdparty/boost;../../../thirdparty/heightfield/include;../../../thirdparty/eigen;../../../thirdparty/nurbs/include;../../../thirdparty/lexer/include;../../../thirdparty/brepdb/include;../../../thirdparty/cslang/include;../../../thirdparty/easyvm/include;../../../thirdparty/brepvm/include;../../../thirdparty/brepom/include;../../../thirdparty/tinyobjloader;../../../thirdparty/oneTBB/include;../../../thirdparty/graph/include;../../../thirdparty/objcomp/include;../../../../cax/thirdparty/OCCT/include</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;NO_BOOST;NO_RTTR;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <AdditionalIncludeDirectories>../../../src;../../../thirdparty;../../../thirdparty/vessel/src/include;../../../thirdparty/unirender/include;../../../thirdparty/shadertrans/include;../../../thirdparty/shaderlink/include;../../../thirdparty/tessellation/include;../../../thirdparty/dtex/include;../../../thirdparty/geoshape/include;../../../thirdparty/primitive/include;../../../thirdparty/easygui/include;../../../thirdparty/model/include;../../../thirdparty/guard/include;../../../thirdparty/sm;../../../thirdparty/gtxt;../../../thirdparty/gimg;../../../thirdparty/glslang;../../../thirdparty/glfw/include;../../../thirdparty/uniphysics/include;../../../thirdparty/constraints2/include;../../../thirdparty/polymesh3/include;../../../thirdparty/halfedge/include;../../../thirdparty/CGAL-5.3.1/include;../../../thirdparty/boost;../../../thirdparty/heightfield/include;../../../thirdparty/eigen;../../../thirdparty/nurbs/include;../../../thirdparty/lexer/include;../../../thirdparty/brepdb/include;../../../thirdparty/cslang/include;../../../thirdparty/easyvm/include;../../../thirdparty/brepvm/include;../../../thirdparty/brepom/include;../../../thirdparty/tinyobjloader;../../../thirdparty/oneTBB/include;../../../thirdparty/graph/include;../../../thirdparty/objcomp/include;../../../../cax/thirdparty/OCCT/include</AdditionalIncludeDirectories>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>