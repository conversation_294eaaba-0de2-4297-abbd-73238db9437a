var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("triangles")
node0.render_state = { "blend" : false, "depth_test" : false, "depth_func" : "less", "cull" : "disable", "clip_plane" : false }
node0.skip = false

_editor.add_node(node0, 201.5802241559, 331.0448724385)

var node1 = ::rendergraph::nodes::pass::Pass()

node1.once = true

_editor.add_node(node1, 622.79212901553, -78.613740923133)

var node2 = ::rendergraph::nodes::shader::Shader()

node2.vs = "
#version 330 core
layout (location = 0) in vec3 aPos;

out vec3 WorldPos;

uniform UBO
{
    mat4 projection;
    mat4 view;    
};

void main()
{
    WorldPos = aPos;
    gl_Position =  projection * view * vec4(WorldPos, 1.0);
}
"
node2.tcs = ""
node2.tes = ""
node2.gs = ""
node2.fs = "
#version 330 core
out vec4 FragColor;
in vec3 WorldPos;

uniform samplerCube environmentMap;

const float PI = 3.14159265359;

void main()
{       
    // The world vector acts as the normal of a tangent surface
    // from the origin, aligned to WorldPos. Given this normal, calculate all
    // incoming radiance of the environment. The result of this radiance
    // is the radiance of light coming from -Normal direction, which is what
    // we use in the PBR shader to sample irradiance.
    vec3 N = normalize(WorldPos);

    vec3 irradiance = vec3(0.0);   
    
    // tangent space calculation from origin point
    vec3 up    = vec3(0.0, 1.0, 0.0);
    vec3 right = cross(up, N);
    up            = cross(N, right);
       
    float sampleDelta = 0.025;
    float nrSamples = 0.0;
    for(float phi = 0.0; phi < 2.0 * PI; phi += sampleDelta)
    {
        for(float theta = 0.0; theta < 0.5 * PI; theta += sampleDelta)
        {
            // spherical to cartesian (in tangent space)
            vec3 tangentSample = vec3(sin(theta) * cos(phi),  sin(theta) * sin(phi), cos(theta));
            // tangent space to world
            vec3 sampleVec = tangentSample.x * right + tangentSample.y * up + tangentSample.z * N; 

            irradiance += textureLod(environmentMap, sampleVec, 0.0).rgb * cos(theta) * sin(theta);
            nrSamples++;
        }
    }
    irradiance = PI * irradiance * (1.0 / float(nrSamples));
    
    FragColor = vec4(irradiance, 1.0);
}
"
node2.cs = ""
node2.render_gen()

_editor.add_node(node2, 10.613133775691, 362.0657401001)

var node3 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node3.type = "cube"
node3.layout = [ "position" ]
node3.shape_params = {  }

_editor.add_node(node3, 17.574015125081, 228.0304245447)

var node4 = ::blueprint::nodes::perspective::Perspective()

node4.fovy = 90
node4.aspect = 1
node4.znear = 0.1
node4.zfar = 10

_editor.add_node(node4, -196.36238572382, 483.8702304338)

var node5 = ::rendergraph::nodes::clear::Clear()

node5.masks = [ "color" ]
node5.values = { "color" : [ 255, 255, 255, 255 ] }

_editor.add_node(node5, -2.4988506432387, 519.1085693723)

var node6 = ::rendergraph::nodes::render_target::RenderTarget()

node6.width = 32
node6.height = 32

_editor.add_node(node6, 16.711829403841, 38.321903012524)

var node7 = ::blueprint::nodes::loop::Loop()

node7.start = 0
node7.end = 6

_editor.add_node(node7, 428.63060203633, -97.89321188616)

var node8 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node8, -170.23870943051, 20.126739094496)

var node9 = ::blueprint::nodes::custom::Custom()

node9.code = "
import \"maths\" for Matrix44
import \"maths.vector\" for Vector3
var capture_views = [
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3( 1.0,  0.0,  0.0), Vector3(0.0, -1.0,  0.0)),
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3(-1.0,  0.0,  0.0), Vector3(0.0, -1.0,  0.0)),
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3( 0.0,  1.0,  0.0), Vector3(0.0,  0.0,  1.0)),
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3( 0.0, -1.0,  0.0), Vector3(0.0,  0.0, -1.0)),
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3( 0.0,  0.0,  1.0), Vector3(0.0, -1.0,  0.0)),
    Matrix44.init().lookat(Vector3(0.0, 0.0, 0.0), Vector3( 0.0,  0.0, -1.0), Vector3(0.0, -1.0,  0.0))
]
_editor.script_stack.clear()
_editor.script_stack.add(capture_views)
"
node9.init_ports(0, 1)
node9.use_cache = false

_editor.add_node(node9, -354.78446284544, 355.3719837015)

var node10 = ::blueprint::nodes::fetch::Fetch()

_editor.add_node(node10, -176.34202485646, 343.21350549888)

var node11 = ::rendergraph::nodes::cubemap::Cubemap()
node11.query_param("unif_name").value = "u_irradiance_cubemap"

node11.init_texture(32, 32, "rgb16f")

_editor.add_node(node11, -340.65024463212, -12.842887032788)

var node12 = ::blueprint::nodes::input::Input()

node12.var_name = "environmentMap"
node12.var_type = "unknown"

_editor.add_node(node12, -195.05360857502, 219.01846172174)

var node13 = ::blueprint::nodes::output::Output()

node13.var_name = "tex"
node13.var_type = "texture"

_editor.add_node(node13, -189.10556446422, 82.644868393575)

var node14 = ::rendergraph::nodes::pass::Pass()

node14.once = false

_editor.add_node(node14, 369.28287515848, 418.27006772655)

Blueprint.connect(node0, "next", node14, "do")
Blueprint.connect(node6, "fbo", node14, "fbo")
Blueprint.connect(node11, "tex", node13, "var")
Blueprint.connect(node9, "next", node5, "prev")
Blueprint.connect(node14, "next", node7, "do")
Blueprint.connect(node9, "out0", node10, "items")
Blueprint.connect(node7, "index", node10, "index")
Blueprint.connect(node11, "sides", node8, "items")
Blueprint.connect(node7, "index", node8, "index")
Blueprint.connect(node8, "item", node6, "col0")
Blueprint.connect(node4, "mat", node2, "projection")
Blueprint.connect(node10, "item", node2, "view")
Blueprint.connect(node12, "var", node2, "environmentMap")
Blueprint.connect(node5, "next", node0, "prev")
Blueprint.connect(node2, "out", node0, "shader")
Blueprint.connect(node3, "out", node0, "va")
Blueprint.connect(node7, "next", node1, "do")
