var node0 = ::blueprint::nodes::input::Input()

node0.var_name = "vao"
node0.var_type = "array"

_editor.add_node(node0, 621.0938585934, 162.22430090309)

var node1 = ::rendergraph::nodes::draw::Draw()

node1.set_prim_type("triangles")
node1.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : false }
node1.skip = false

_editor.add_node(node1, 780.67380359571, -21.20447758435)

var node2 = ::rendergraph::nodes::shader::Shader()
node2.query_param("inc_dir").value = ""

node2.vs = "
#version 330 core
layout (location = 0) in vec3  aPos;
layout (location = 1) in vec3  aCol;
layout (location = 2) in float aMatID;
layout (location = 3) in float aOffset;
layout (location = 4) in mat4 aInstanceMatrix;

out VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;

	float offset;

	bool use_inst_mat;	
};

void main()
{
	float dy = aOffset * 1.0;
	dy = max(0.0, dy - offset);

	vec3 pos = aPos;
	pos.y += dy;

	mat4 model_mat = use_inst_mat ? aInstanceMatrix : model;

	vs_out.frag_pos = vec3(model_mat * vec4(pos, 1.0));

	vs_out.frag_col = aCol;

    gl_Position = projection * view * model_mat * vec4(pos, 1.0);
}
"
node2.tcs = ""
node2.tes = ""
node2.gs = ""
node2.fs = "
#version 330 core
layout (location = 0) out vec3 gMain;
layout (location = 1) out vec3 gDepth;
layout (location = 2) out vec3 gNormal;

in VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} fs_in;

uniform UBO
{
	vec3 light_pos;
	vec3 cam_pos;

	float geo_idx;	
};

float near = 0.01;
float far = 100.0; 
float LinearizeDepth(float depth) 
{
    float z = depth * 2.0 - 1.0; // back to NDC 
    return (2.0 * near * far) / (far + near - z * (far - near));	
}

void main()
{
 	vec3 dFdxPos = dFdx( fs_in.frag_pos );
    vec3 dFdyPos = dFdy( fs_in.frag_pos );
    vec3 N = normalize( cross(dFdxPos,dFdyPos ));

	// ambient
	vec3 ambient = vec3(0.25);

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * fs_in.frag_col;

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(N, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    float depth = LinearizeDepth(gl_FragCoord.z) / far; // divide by far to get depth in range [0,1] for visualization purposes
//    depth = depth + 0.001 * mod(geo_idx, 10);
    depth = depth + 0.001 * geo_idx;
    gDepth = vec3(depth);

    gMain = ambient + diffuse + specular; 
    gNormal = N;
}
"
node2.cs = ""
node2.render_gen()
node2.set_uniform("use_inst_mat", [ 0 ])

_editor.add_node(node2, 597.47156861689, -150.79476664733)

var node3 = ::rendergraph::nodes::clear::Clear()

node3.masks = [ "color", "depth" ]
node3.values = { "color" : [ 0, 0, 0, 0 ] }

_editor.add_node(node3, 463.69572492314, 407.94936384054)

var node4 = ::blueprint::nodes::perspective::Perspective()

node4.fovy = 45
node4.aspect = 0
node4.znear = 0.01
node4.zfar = 100

_editor.add_node(node4, -352.41599251269, 324.3399246789)

var node5 = ::blueprint::nodes::proxy::Proxy()

node5.real_name = "view_cam"
node5.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node5, -511.87449701681, 123.46442709966)

var node6 = ::blueprint::nodes::number3::Number3()

node6.value.set(5.9766573905945, 5.6433238983154, 5.3099908828735)

_editor.add_node(node6, -304.79293980443, -123.54976708242)

var node7 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node7, 971.5941409146, 136.38451606488)

var node8 = ::rendergraph::nodes::render_target::RenderTarget()

_editor.add_node(node8, 1006.2254582471, -601.81666748678)

var node9 = ::rendergraph::nodes::texture::Texture()
node9.query_param("unif_name").value = "u_tex"
node9.gamma_correction = false
node9.mipmap = false
node9.init_texture(0, 0, "rgb")

_editor.add_node(node9, 474.40405076971, -784.61954261786)

var node10 = ::rendergraph::nodes::texture::Texture()
node10.query_param("unif_name").value = "u_tex"
node10.gamma_correction = false
node10.mipmap = false
node10.init_texture(0, 0, "r16f")

_editor.add_node(node10, 720.05358296636, -779.70827458377)

var node11 = ::rendergraph::nodes::texture::Texture()
node11.query_param("unif_name").value = "u_tex"
node11.gamma_correction = false
node11.mipmap = false
node11.init_texture(0, 0, "rgb16f")

_editor.add_node(node11, 942.95412422541, -781.75098051994)

var node12 = ::rendergraph::nodes::render_buffer::RenderBuffer()

node12.init_rbo(0, 0, "depth_component")

_editor.add_node(node12, 761.28070305769, -623.97703707472)

var node13 = ::blueprint::nodes::commentary::Commentary()

node13.set_size(934.75103759766, 556.74530029297)
node13.title = "GBuffer"

_editor.add_node(node13, 797.3810625679, -480.95532357993)

var node14 = ::rendergraph::nodes::pass::Pass()

node14.once = false

_editor.add_node(node14, 2291.3510039844, -262.34109367542)

var node15 = ::rendergraph::nodes::draw::Draw()

node15.set_prim_type("tri_strip")
node15.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node15.skip = false

_editor.add_node(node15, 1850.7890513854, -817.59943915683)

var node16 = ::rendergraph::nodes::shader::Shader()
node16.query_param("inc_dir").value = ""

node16.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;

out vec2 TexCoord;

void main()
{
	gl_Position = vec4(aPos, 1.0);
	TexCoord = vec2(aTexCoord.x, aTexCoord.y);
}
"
node16.tcs = ""
node16.tes = ""
node16.gs = ""
node16.fs = "
#version 330 core
out vec4 FragColor;

in vec2 TexCoord;

uniform UBO
{
	float scale;
	float depthThreshold;
	float normalThreshold;
};

uniform sampler2D main_tex;
uniform sampler2D cam_depth_tex;
uniform sampler2D cam_normal_tex;

vec4 alphaBlend(vec4 top, vec4 bottom)
{
	vec3 color = (top.rgb * top.a) + (bottom.rgb * (1 - top.a));
	float alpha = top.a + bottom.a * (1 - top.a);

	return vec4(color, alpha);
}

void main()
{
	float depth = texture(cam_depth_tex, TexCoord).r;

	float halfScaleFloor = floor(scale * 0.5);
	float halfScaleCeil = ceil(scale * 0.5);

	vec2 _MainTex_TexelSize = vec2(1.0/1024.0, 1.0/768.0);

	vec2 bottomLeftUV = TexCoord - vec2(_MainTex_TexelSize.x, _MainTex_TexelSize.y) * halfScaleFloor;
	vec2 topRightUV = TexCoord + vec2(_MainTex_TexelSize.x, _MainTex_TexelSize.y) * halfScaleCeil;  
	vec2 bottomRightUV = TexCoord + vec2(_MainTex_TexelSize.x * halfScaleCeil, -_MainTex_TexelSize.y * halfScaleFloor);
	vec2 topLeftUV = TexCoord + vec2(-_MainTex_TexelSize.x * halfScaleFloor, _MainTex_TexelSize.y * halfScaleCeil);

	vec3 normal0 = texture(cam_normal_tex, bottomLeftUV).rgb;
	vec3 normal1 = texture(cam_normal_tex, topRightUV).rgb;
	vec3 normal2 = texture(cam_normal_tex, bottomRightUV).rgb;
	vec3 normal3 = texture(cam_normal_tex, topLeftUV).rgb;

	vec3 normalFiniteDifference0 = normal1 - normal0;
	vec3 normalFiniteDifference1 = normal3 - normal2;
	float edgeNormal = sqrt(dot(normalFiniteDifference0, normalFiniteDifference0) + dot(normalFiniteDifference1, normalFiniteDifference1));
	edgeNormal = edgeNormal > normalThreshold ? 1 : 0;

	float depth0 = texture(cam_depth_tex, bottomLeftUV).r;
	float depth1 = texture(cam_depth_tex, topRightUV).r;
	float depth2 = texture(cam_depth_tex, bottomRightUV).r;
	float depth3 = texture(cam_depth_tex, topLeftUV).r;

	float depthFiniteDifference0 = depth1 - depth0;
	float depthFiniteDifference1 = depth3 - depth2;
	float edgeDepth = sqrt(pow(depthFiniteDifference0, 2) + pow(depthFiniteDifference1, 2)) * 100;	
	edgeDepth = (edgeDepth) > depthThreshold ? 1 : 0;

	float edge = max(edgeDepth, edgeNormal);

	vec4 _Color = vec4(0.0, 0.0, 0.0, 1.0);
	//vec4 _Color = vec4(1.0);
	vec4 edgeColor = vec4(_Color.rgb, _Color.a * edge);

	vec4 color = texture(main_tex, TexCoord);
	FragColor = alphaBlend(edgeColor, color);
}
"
node16.cs = ""
node16.render_gen()
node16.set_uniform("scale", [ 1 ])
node16.set_uniform("normalThreshold", [ 0.35301756858826 ])

_editor.add_node(node16, 1456.0473424022, -843.15563683398)

var node17 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node17.type = "quad"
node17.layout = [ "position", "texture" ]
node17.shape_params = {  }

_editor.add_node(node17, 1639.493374151, -884.79448653931)

var node18 = ::rendergraph::nodes::clear::Clear()

node18.masks = [ "color" ]
node18.values = { "color" : [ 128, 128, 128, 255 ] }

_editor.add_node(node18, 1620.5813407813, -636.93494254883)

var node19 = ::blueprint::nodes::commentary::Commentary()

node19.set_size(635.08331298828, 464.41665649414)
node19.title = "Outline"

_editor.add_node(node19, 1629.0608817969, -515.58837882813)

var node20 = ::rendergraph::nodes::pass::Pass()

node20.once = false

_editor.add_node(node20, 2594.1057567941, -694.26369554953)

var node21 = ::rendergraph::nodes::render_target::RenderTarget()

_editor.add_node(node21, 2532.6442579609, -820.59767915388)

var node22 = ::rendergraph::nodes::texture::Texture()
node22.query_param("unif_name").value = "u_tex"
node22.gamma_correction = false
node22.mipmap = false
node22.init_texture(0, 0, "rgb")

_editor.add_node(node22, 2169.5975193976, -1011.5945192769)

var node23 = ::rendergraph::nodes::draw::Draw()

node23.set_prim_type("tri_strip")
node23.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : false, "depth_func" : "less", "clip_plane" : false }
node23.skip = false

_editor.add_node(node23, 2659.8492689063, -983.98777384766)

var node24 = ::rendergraph::nodes::shader::Shader()
node24.query_param("inc_dir").value = ""

node24.vs = "
#version 330 core

layout (location = 0) in vec3 aPos;
layout (location = 1) in vec2 aTexCoord;

out vec2 TexCoord;

void main()
{
	gl_Position = vec4(aPos, 1.0);
	TexCoord = vec2(aTexCoord.x, aTexCoord.y);
}
"
node24.tcs = ""
node24.tes = ""
node24.gs = ""
node24.fs = "
#version 330
out vec4 FragColor;

uniform sampler2D tex;

in vec2 TexCoord;

void main() 
{
	const float FXAA_REDUCE_MIN = 1.0 / 128.0;
	const float FXAA_REDUCE_MUL = 1.0 / 8.0;
	const float FXAA_SPAN_MAX = 8.0;

	vec2 screenSizeInv = 1.0 / textureSize(tex, 0);
	
	vec2 tcrgbNW = (TexCoord + vec2(-1.0, -1.0) * screenSizeInv);
	vec2 tcrgbNE = (TexCoord + vec2(1.0, -1.0) * screenSizeInv);
	vec2 tcrgbSW = (TexCoord + vec2(-1.0, 1.0) * screenSizeInv);
	vec2 tcrgbSE = (TexCoord + vec2(1.0, 1.0) * screenSizeInv);
	vec2 tcrgbM = vec2(TexCoord);
	
	vec3 rgbNW = textureLod(tex, tcrgbNW, 0.0).rgb;
	vec3 rgbNE = textureLod(tex, tcrgbNE, 0.0).rgb;
	vec3 rgbSW = textureLod(tex, tcrgbSW, 0.0).rgb;
	vec3 rgbSE = textureLod(tex, tcrgbSE, 0.0).rgb;
	vec4 texColor = textureLod(tex, tcrgbM, 0.0);
	vec3 rgbM  = texColor.rgb;
	vec3 luma = vec3(0.299, 0.587, 0.114);
	float lumaNW = dot(rgbNW, luma);
	float lumaNE = dot(rgbNE, luma);
	float lumaSW = dot(rgbSW, luma);
	float lumaSE = dot(rgbSE, luma);
	float lumaM  = dot(rgbM,  luma);
	float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));
	float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));
	
	vec2 dir;
	dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));
	dir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));
	
	float dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) *
						  (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);
	
	float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);
	dir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX),
			  max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),
			  dir * rcpDirMin)) * screenSizeInv;
			  
	vec3 rgbA = 0.5 * (
		textureLod(tex, TexCoord + dir * (1.0 / 3.0 - 0.5), 0.0).rgb +
		textureLod(tex, TexCoord + dir * (2.0 / 3.0 - 0.5), 0.0).rgb);
	FragColor.rgb = rgbA * 0.5 + 0.25 * ( // vec3 rgbB
		textureLod(tex, TexCoord + dir * -0.5, 0.0).rgb +
		textureLod(tex, TexCoord + dir * 0.5, 0.0).rgb);
		
	// float lumaB = dot(rgbB, luma);
	float lumaB = dot(FragColor.rgb, luma);
	if ((lumaB < lumaMin) || (lumaB > lumaMax)) FragColor.rgb = rgbA;
	// else FragColor.rgb = rgbB;
}
"
node24.cs = ""
node24.render_gen()

_editor.add_node(node24, 2439.6102256368, -918.82419357788)

var node25 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node25.type = "quad"
node25.layout = [ "position", "texture" ]
node25.shape_params = {  }

_editor.add_node(node25, 2504.1860373729, -1082.7474231804)

var node26 = ::blueprint::nodes::commentary::Commentary()

node26.set_size(682.41668701172, 682.41668701172)
node26.title = "FXAA"

_editor.add_node(node26, 2394.868311875, -602.64295939453)

var node27 = ::blueprint::nodes::input::Input()

node27.var_name = "light_pos"
node27.var_type = "num3"

_editor.add_node(node27, -398.85992270034, -25.380448707439)

var node28 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node28, -146.01292106246, -39.375665956796)

var node29 = ::blueprint::nodes::is_null::IsNull()

_editor.add_node(node29, -271.96991281516, -16.516804689201)

var node30 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node30, 439.55755615234, -635.83148803711)

var node31 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node31, 2133.8637990142, -891.18750354357)

var node32 = ::blueprint::nodes::input::Input()

node32.var_name = "offset"
node32.var_type = "num"

_editor.add_node(node32, -350.04396696405, -231.78197136594)

var node33 = ::blueprint::nodes::orthographic::Orthographic()

node33.left = -1.5
node33.right = 1
node33.bottom = 0
node33.top = 1
node33.near = 1
node33.far = -1

_editor.add_node(node33, -384.47467389827, 508.73770768744)

var node34 = ::blueprint::nodes::viewport::Viewport()

_editor.add_node(node34, -802.23415431579, 519.3275287042)

var node35 = ::blueprint::nodes::split::Split()

_editor.add_node(node35, -671.27621321593, 518.76306990171)

var node36 = ::blueprint::nodes::divide::Divide()

_editor.add_node(node36, -533.54458011229, 548.68017137206)

var node37 = ::blueprint::nodes::number::Number()

node37.value = 0.32784762978554

_editor.add_node(node37, -785.81628134814, 410.54876159183)

var node38 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node38, -127.06674448974, 524.7661433197)

var node39 = ::blueprint::nodes::input::Input()

node39.var_name = "ortho"
node39.var_type = "bool"

_editor.add_node(node39, -791.28773756783, -52.387507851666)

var node40 = ::blueprint::nodes::store::Store()

node40.var_name = "ortho"

_editor.add_node(node40, -645.95391590779, -51.713429245096)

var node41 = ::blueprint::nodes::load::Load()

node41.var_name = "ortho"

_editor.add_node(node41, -347.05605833723, 633.36617959175)

var node42 = ::blueprint::nodes::load::Load()

node42.var_name = "ortho"

_editor.add_node(node42, 997.57368297679, -1026.9346942078)

var node43 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node43, 1136.9570067209, -1047.0249629162)

var node44 = ::blueprint::nodes::number::Number()

node44.value = 0.0001

_editor.add_node(node44, 996.93920714177, -1083.2246731741)

var node45 = ::blueprint::nodes::number::Number()

node45.value = 0.15

_editor.add_node(node45, 993.5241920331, -1150.159968538)

var node46 = ::blueprint::nodes::number2::Number2()

node46.value.set(0.18203972280025, 0.87092864513397)

_editor.add_node(node46, -657.6935868223, 308.29406105164)

var node47 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node47, -660.04064421295, 383.99628710736)

var node48 = ::blueprint::nodes::integer::Integer()

node48.value = 10

_editor.add_node(node48, -791.05505929333, 348.60376363056)

var node49 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node49, -522.19611742774, 284.64887819146)

var node50 = ::blueprint::nodes::integer::Integer()

node50.value = 3

_editor.add_node(node50, -655.07329006827, 234.35425306656)

var node51 = ::blueprint::nodes::input::Input()

node51.var_name = "model"
node51.var_type = "mat4"

_editor.add_node(node51, -798.62925669488, -169.17677268459)

var node52 = ::blueprint::nodes::property::Property()

node52.var_name = "no_bg"
node52.var_type = "bool"

_editor.add_node(node52, 432.94838882721, 535.35704210362)

var node53 = ::rendergraph::nodes::clear::Clear()

node53.masks = [ "color", "depth" ]
node53.values = { "color" : [ 0.5030083656311, 0.5, 0.5, 1 ] }

_editor.add_node(node53, 320.54519270889, 404.91232475466)

var node54 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node54, 605.72947070459, 463.87112068618)

var node55 = ::blueprint::nodes::input::Input()

node55.var_name = "selected"
node55.var_type = "array"

_editor.add_node(node55, 1726.6034469979, 98.4881219226)

var node56 = ::rendergraph::nodes::draw::Draw()

node56.set_prim_type("triangles")
node56.render_state = { "stencil_test" : false, "stencil_func" : "always", "stencil_mask" : 255, "cull" : "disable", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "lequal", "clip_plane" : false }
node56.skip = false

_editor.add_node(node56, 1786.3615084957, -145.97735018435)

var node57 = ::rendergraph::nodes::shader::Shader()
node57.query_param("inc_dir").value = ""

node57.vs = "
#version 330 core
layout (location = 0) in vec3  aPos;
layout (location = 1) in vec3  aCol;
layout (location = 2) in float aMatID;
layout (location = 3) in float aOffset;
layout (location = 4) in mat4 aInstanceMatrix;

out VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} vs_out;

uniform UBO
{
	mat4 model;
	mat4 view;
	mat4 projection;

	float offset;

	bool use_inst_mat;	
};

void main()
{
	float dy = aOffset * 1.0;
	dy = max(0.0, dy - offset);

	vec3 pos = aPos;
	pos.y += dy;

	mat4 model_mat = use_inst_mat ? aInstanceMatrix : model;

	vs_out.frag_pos = vec3(model_mat * vec4(pos, 1.0));

	vs_out.frag_col = aCol;

    gl_Position = projection * view * model_mat * vec4(pos, 1.0);
}
"
node57.tcs = ""
node57.tes = ""
node57.gs = ""
node57.fs = "
#version 330 core
layout (location = 0) out vec3 gMain;
layout (location = 1) out vec3 gDepth;
layout (location = 2) out vec3 gNormal;

in VS_OUT {
    vec3 frag_pos;
    vec3 frag_col;
} fs_in;

uniform UBO
{
	vec3 light_pos;
	vec3 cam_pos;

	float geo_idx;	
};

float near = 0.01;
float far = 100.0; 
float LinearizeDepth(float depth) 
{
    float z = depth * 2.0 - 1.0; // back to NDC 
    return (2.0 * near * far) / (far + near - z * (far - near));	
}

void main()
{
 	vec3 dFdxPos = dFdx( fs_in.frag_pos );
    vec3 dFdyPos = dFdy( fs_in.frag_pos );
    vec3 N = normalize( cross(dFdxPos,dFdyPos ));

	// ambient
	vec3 ambient = vec3(0.25) * vec3(1, 0, 0);

	// diffuse
	const vec3 LIGHT_POS = vec3(-5.0, -5.0, 10);
	vec3 light_dir = normalize(light_pos - fs_in.frag_pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * fs_in.frag_col;

    // specular
    vec3 view_dir = normalize(cam_pos - fs_in.frag_pos);
    vec3 halfway_dir = normalize(light_dir + view_dir);  
    float spec = pow(max(dot(N, halfway_dir), 0.0), 32.0);
    vec3 specular = spec * vec3(1.0);

    float depth = LinearizeDepth(gl_FragCoord.z) / far; // divide by far to get depth in range [0,1] for visualization purposes
//    depth = depth + 0.001 * mod(geo_idx, 10);
    depth = depth + 0.001 * geo_idx;
    gDepth = vec3(depth);

    gMain = ambient + diffuse + specular; 
    gNormal = N;
}
"
node57.cs = ""
node57.render_gen()
node57.set_uniform("use_inst_mat", [ 0 ])

_editor.add_node(node57, 1512.8489508971, -84.684069247328)

var node58 = ::blueprint::nodes::for_each::ForEach()

_editor.add_node(node58, 1892.5550158146, 132.31521346488)

var node59 = ::geograph::nodes::brush_to_gltf::BrushToGltf()
node59.query_param("adjacencies").value = false

_editor.add_node(node59, 1358.2418686514, 259.72756385935)

var node60 = ::blueprint::nodes::fetch::Fetch()
node60.index = "node0"

_editor.add_node(node60, 1634.5918179411, 248.23326912543)

var node61 = ::blueprint::nodes::fetch::Fetch()
node61.index = "va"

_editor.add_node(node61, 1861.518941804, 254.28131791724)

var node62 = ::blueprint::nodes::store::Store()

node62.var_name = "view_mat"

_editor.add_node(node62, -359.58337473972, 196.08194180575)

var node63 = ::blueprint::nodes::load::Load()

node63.var_name = "view_mat"

_editor.add_node(node63, 435.08124355368, 20.624839391389)

var node64 = ::blueprint::nodes::load::Load()

node64.var_name = "view_mat"

_editor.add_node(node64, 1345.0902229712, 72.55770943379)

var node65 = ::blueprint::nodes::store::Store()

node65.var_name = "model_mat"

_editor.add_node(node65, -658.3437407937, -166.22757711295)

var node66 = ::blueprint::nodes::load::Load()

node66.var_name = "model_mat"

_editor.add_node(node66, 1346.9338240341, 117.90807997159)

var node67 = ::blueprint::nodes::load::Load()

node67.var_name = "model_mat"

_editor.add_node(node67, 429.43162727132, 65.094269566442)

var node68 = ::blueprint::nodes::store::Store()

node68.var_name = "proj_mat"

_editor.add_node(node68, 14.777436059369, 535.7368839989)

var node69 = ::blueprint::nodes::load::Load()

node69.var_name = "proj_mat"

_editor.add_node(node69, 434.27762369939, -22.638904301784)

var node70 = ::blueprint::nodes::load::Load()

node70.var_name = "proj_mat"

_editor.add_node(node70, 1348.3574680504, 25.55050012313)

var node71 = ::blueprint::nodes::store::Store()

node71.var_name = "light_pos"

_editor.add_node(node71, -5.6943543823255, -29.656614599616)

var node72 = ::blueprint::nodes::load::Load()

node72.var_name = "light_pos"

_editor.add_node(node72, 434.84899445109, -114.86894684831)

var node73 = ::blueprint::nodes::store::Store()

node73.var_name = "offset"

_editor.add_node(node73, -216.97795354226, -231.14041253996)

var node74 = ::blueprint::nodes::load::Load()

node74.var_name = "offset"

_editor.add_node(node74, 434.11021275081, -67.183982446621)

var node75 = ::blueprint::nodes::load::Load()

node75.var_name = "light_pos"

_editor.add_node(node75, 1349.382022732, -64.52333305945)

var node76 = ::blueprint::nodes::load::Load()

node76.var_name = "offset"

_editor.add_node(node76, 1348.6432410317, -16.83836865776)

var node77 = ::blueprint::nodes::store::Store()

node77.var_name = "view_pos"

_editor.add_node(node77, -361.69903709475, 126.80044580885)

var node78 = ::blueprint::nodes::load::Load()

node78.var_name = "view_pos"

_editor.add_node(node78, 433.34228704785, -158.32352365542)

var node79 = ::blueprint::nodes::load::Load()

node79.var_name = "view_pos"

_editor.add_node(node79, 1352.1348916173, -109.50792464572)

var node80 = ::blueprint::nodes::commentary::Commentary()

node80.set_size(970.623341222, 956.35775769145)
node80.title = "Params"

_editor.add_node(node80, -391.65968468556, 678.94201329252)

var node81 = ::blueprint::nodes::commentary::Commentary()

node81.set_size(689.8918, 647.4329)
node81.title = "Draw scene"

_editor.add_node(node81, 709.73575067383, 222.51465212109)

var node82 = ::blueprint::nodes::commentary::Commentary()

node82.set_size(734.828572122, 680.595775229)
node82.title = "Draw selected"

_editor.add_node(node82, 1628.8904438268, 331.19936271794)

Blueprint.connect(node58, "out", node59, "brush")
Blueprint.connect(node59, "gltf", node60, "items")
Blueprint.connect(node60, "item", node61, "items")
Blueprint.connect(node52, "var", node54, "cond")
Blueprint.connect(node3, "next", node54, "true")
Blueprint.connect(node53, "next", node54, "false")
Blueprint.connect(node54, "next", node7, "prev")
Blueprint.connect(node0, "var", node7, "in")
Blueprint.connect(node1, "next", node7, "do")
Blueprint.connect(node7, "next", node58, "prev")
Blueprint.connect(node55, "var", node58, "in")
Blueprint.connect(node56, "next", node58, "do")
Blueprint.connect(node51, "var", node65, "var")
Blueprint.connect(node65, "var", node67, "var")
Blueprint.connect(node65, "var", node66, "var")
Blueprint.connect(node46, "v2", node49, "a")
Blueprint.connect(node50, "v", node49, "b")
Blueprint.connect(node39, "var", node40, "var")
Blueprint.connect(node40, "var", node42, "var")
Blueprint.connect(node42, "var", node43, "cond")
Blueprint.connect(node44, "v", node43, "true")
Blueprint.connect(node45, "v", node43, "false")
Blueprint.connect(node40, "var", node41, "var")
Blueprint.connect(node37, "v", node47, "a")
Blueprint.connect(node48, "v", node47, "b")
Blueprint.connect(node34, "size", node35, "xy")
Blueprint.connect(node35, "x", node36, "a")
Blueprint.connect(node35, "y", node36, "b")
Blueprint.connect(node36, "v", node33, "aspect")
Blueprint.connect(node47, "v", node33, "scale")
Blueprint.connect(node49, "v", node33, "offset")
Blueprint.connect(node32, "var", node73, "var")
Blueprint.connect(node73, "var", node76, "var")
Blueprint.connect(node73, "var", node74, "var")
Blueprint.connect(node31, "size", node22, "size")
Blueprint.connect(node22, "tex", node24, "tex")
Blueprint.connect(node22, "tex", node21, "col0")
Blueprint.connect(node30, "size", node12, "size")
Blueprint.connect(node30, "size", node11, "size")
Blueprint.connect(node30, "size", node10, "size")
Blueprint.connect(node30, "size", node9, "size")
Blueprint.connect(node43, "result", node16, "depthThreshold")
Blueprint.connect(node10, "tex", node16, "cam_depth_tex")
Blueprint.connect(node11, "tex", node16, "cam_normal_tex")
Blueprint.connect(node9, "tex", node16, "main_tex")
Blueprint.connect(node9, "tex", node8, "col0")
Blueprint.connect(node10, "tex", node8, "col1")
Blueprint.connect(node11, "tex", node8, "col2")
Blueprint.connect(node12, "rbo", node8, "depth")
Blueprint.connect(node27, "var", node29, "in")
Blueprint.connect(node24, "out", node23, "shader")
Blueprint.connect(node25, "out", node23, "va")
Blueprint.connect(node18, "next", node15, "prev")
Blueprint.connect(node16, "out", node15, "shader")
Blueprint.connect(node17, "out", node15, "va")
Blueprint.connect(node58, "next", node14, "do")
Blueprint.connect(node8, "fbo", node14, "fbo")
Blueprint.connect(node14, "next", node20, "prev")
Blueprint.connect(node15, "next", node20, "do")
Blueprint.connect(node21, "fbo", node20, "fbo")
Blueprint.connect(node29, "out", node28, "cond")
Blueprint.connect(node6, "v3", node28, "true")
Blueprint.connect(node27, "var", node28, "false")
Blueprint.connect(node28, "result", node71, "var")
Blueprint.connect(node71, "var", node75, "var")
Blueprint.connect(node71, "var", node72, "var")
Blueprint.connect(node5, "pos", node77, "var")
Blueprint.connect(node77, "var", node79, "var")
Blueprint.connect(node77, "var", node78, "var")
Blueprint.connect(node5, "mat", node62, "var")
Blueprint.connect(node62, "var", node64, "var")
Blueprint.connect(node62, "var", node63, "var")
Blueprint.connect(node5, "zoom", node4, "fovy")
Blueprint.connect(node41, "var", node38, "cond")
Blueprint.connect(node33, "mat", node38, "true")
Blueprint.connect(node4, "mat", node38, "false")
Blueprint.connect(node38, "result", node68, "var")
Blueprint.connect(node68, "var", node70, "var")
Blueprint.connect(node66, "var", node57, "model")
Blueprint.connect(node64, "var", node57, "view")
Blueprint.connect(node70, "var", node57, "projection")
Blueprint.connect(node76, "var", node57, "offset")
Blueprint.connect(node75, "var", node57, "light_pos")
Blueprint.connect(node79, "var", node57, "cam_pos")
Blueprint.connect(node58, "curr_idx", node57, "geo_idx")
Blueprint.connect(node57, "out", node56, "shader")
Blueprint.connect(node61, "item", node56, "va")
Blueprint.connect(node68, "var", node69, "var")
Blueprint.connect(node67, "var", node2, "model")
Blueprint.connect(node63, "var", node2, "view")
Blueprint.connect(node69, "var", node2, "projection")
Blueprint.connect(node74, "var", node2, "offset")
Blueprint.connect(node72, "var", node2, "light_pos")
Blueprint.connect(node78, "var", node2, "cam_pos")
Blueprint.connect(node7, "curr_idx", node2, "geo_idx")
Blueprint.connect(node2, "out", node1, "shader")
Blueprint.connect(node7, "out", node1, "va")
