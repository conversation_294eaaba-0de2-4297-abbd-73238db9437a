C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\HalfEdge.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\HalfEdge.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polygon.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polygon.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polygon_Clip.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polygon_Clip.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polygon_Edit.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polygon_Edit.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron_Boolean.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron_Boolean.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron_Build.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron_Build.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron_Clip.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron_Clip.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron_Edit.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron_Edit.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyhedron_Test.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyhedron_Test.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Polyline.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Polyline.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\TopoID.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\TopoID.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\source\Utility.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\halfedge\platform\msvc\projects\x64\Release\halfedge\Utility.obj
