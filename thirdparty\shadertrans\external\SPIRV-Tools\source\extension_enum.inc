// Minimal extension_enum.inc for Tantien build
// This file is auto-generated by SPIRV-Tools build system
// Providing minimal definitions to resolve compilation issues

// Extension enums for SPIRV-Tools
enum Extension {
    kSPV_KHR_16bit_storage = 0,
    kSPV_KHR_device_group = 1,
    kSPV_KHR_multiview = 2,
    kSPV_KHR_storage_buffer_storage_class = 3,
    kSPV_KHR_variable_pointers = 4,
    kSPV_AMD_gcn_shader = 5,
    kSPV_AMD_shader_ballot = 6,
    kSPV_AMD_shader_explicit_vertex_parameter = 7,
    kSPV_AMD_shader_trinary_minmax = 8,
    kSPV_AMD_gpu_shader_half_float = 9,
    kSPV_AMD_texture_gather_bias_lod = 10,
    kSPV_AMD_gpu_shader_int16 = 11,
    kSPV_AMD_shader_image_load_store_lod = 12,
    kSPV_NV_sample_mask_override_coverage = 13,
    kSPV_NV_geometry_shader_passthrough = 14,
    kSPV_NV_viewport_array2 = 15,
    kSPV_NV_stereo_view_rendering = 16,
    kSPV_NV_shader_subgroup_partitioned = 17,
    kSPV_EXT_shader_stencil_export = 18,
    kSPV_EXT_shader_viewport_index_layer = 19,
    kSPV_EXT_fragment_fully_covered = 20,
    kSPV_EXT_fragment_invocation_density = 21,
    kSPV_EXT_descriptor_indexing = 22,
    kSPV_EXT_shader_atomic_float_add = 23,
    kSPV_EXT_fragment_shader_interlock = 24,
    kSPV_EXT_demote_to_helper_invocation = 25,
    kSPV_EXT_shader_image_int64 = 26,
    kSPV_EXT_shader_atomic_float_min_max = 27,
    kSPV_GOOGLE_hlsl_functionality1 = 28,
    kSPV_GOOGLE_user_type = 29,
    kSPV_GOOGLE_decorate_string = 30,
    kSPV_INTEL_subgroups = 31,
    kSPV_INTEL_media_block_io = 32,
    kSPV_INTEL_device_side_avc_motion_estimation = 33,
    kSPV_INTEL_fpga_memory_attributes = 34,
    kSPV_INTEL_fpga_loop_controls = 35,
    kSPV_INTEL_unstructured_loop_controls = 36,
    kSPV_INTEL_fpga_reg = 37,
    kSPV_INTEL_blocking_pipes = 38,
    kSPV_INTEL_function_pointers = 39,
    kSPV_INTEL_kernel_attributes = 40,
    kSPV_INTEL_io_pipes = 41,
    kSPV_INTEL_inline_assembly = 42,
    kSPV_INTEL_arbitrary_precision_integers = 43,
    kSPV_INTEL_float_controls2 = 44,
    kSPV_INTEL_vector_compute = 45,
    kSPV_INTEL_fast_composite = 46,
    kSPV_INTEL_arbitrary_precision_fixed_point = 47,
    kSPV_INTEL_arbitrary_precision_floating_point = 48,
    kSPV_INTEL_variable_length_array = 49,
    kSPV_INTEL_fp_fast_math_mode = 50,
    kSPV_INTEL_long_constant_composite = 51,
    kSPV_INTEL_optnone = 52,
    kSPV_INTEL_usm_storage_classes = 53,
    kSPV_INTEL_io_pipes_v2 = 54,
    kSPV_INTEL_runtime_aligned = 55,
    kSPV_INTEL_fpga_cluster_attributes = 56,
    kSPV_INTEL_loop_fuse = 57,
    kSPV_INTEL_fpga_buffer_location = 58,
    kSPV_INTEL_arbitrary_precision_integers_v2 = 59,
    kSPV_INTEL_bfloat16_conversion = 60,
    kSPV_INTEL_split_barrier = 61,
    kSPV_INTEL_fpga_argument_interfaces = 62,
    kSPV_INTEL_global_variable_host_access = 63,
    kSPV_INTEL_global_variable_fpga_decorations = 64,
    kSPV_INTEL_fpga_latency_control = 65,
    kSPV_INTEL_fpga_invocation_pipelining_attributes = 66,
    kSPV_INTEL_arbitrary_precision_fixed_point_v2 = 67,
    kSPV_INTEL_arbitrary_precision_floating_point_v2 = 68,
    kSPV_INTEL_cache_controls = 69,
    kSPV_INTEL_fpga_dsp_control = 70,
    kSPV_INTEL_memory_access_aliasing = 71,
    kSPV_KHR_expect_assume = 72,
    kSPV_KHR_linkonce_odr = 73,
    kSPV_KHR_vulkan_memory_model = 74,
    kSPV_KHR_terminate_invocation = 75,
    kSPV_KHR_subgroup_uniform_control_flow = 76,
    kSPV_KHR_integer_dot_product = 77,
    kSPV_KHR_bit_instructions = 78,
    kSPV_KHR_uniform_group_instructions = 79,
    kSPV_KHR_subgroup_rotate = 80,
    kSPV_KHR_cooperative_matrix = 81,
    kSPV_KHR_shader_clock = 82,
    kSPV_KHR_global_priority = 83,
    kSPV_KHR_task_sequence = 84,
    kSPV_KHR_memory_scope_semantics = 85,
    kSPV_KHR_vulkan_memory_model_device_scope = 86,
    kSPV_KHR_physical_storage_buffer = 87,
    kSPV_EXT_physical_storage_buffer = 88,
    kSPV_KHR_ray_tracing = 89,
    kSPV_KHR_ray_query = 90,
    kSPV_KHR_shader_atomic_counter_ops = 91,
    kSPV_KHR_shader_draw_parameters = 92,
    kSPV_KHR_shader_non_semantic_info = 93,
    kSPV_KHR_post_depth_coverage = 94,
    kSPV_KHR_float_controls = 95,
    kSPV_KHR_8bit_storage = 96,
    kSPV_KHR_workgroup_memory_explicit_layout = 97,
    kSPV_KHR_no_integer_wrap_decoration = 98,
    kSPV_KHR_fragment_shading_rate = 99,
    kSPV_NV_ray_tracing = 100,
    kSPV_NV_ray_tracing_motion_blur = 101,
    kSPV_NV_compute_shader_derivatives = 102,
    kSPV_NV_fragment_shader_barycentric = 103,
    kSPV_NV_mesh_shader = 104,
    kSPV_NV_image_footprint = 105,
    kSPV_NV_shading_rate = 106,
    kSPV_NV_cooperative_matrix = 107,
    kSPV_NV_shader_sm_builtins = 108,
    kSPV_EXT_mesh_shader = 109,
    kSPV_EXT_opacity_micromap = 110,
    kSPV_EXT_shader_tile_image = 111,
    kSPV_EXT_shader_image_int64 = 112,
    kSPV_EXT_shader_atomic_float16_add = 113,
    kSPV_EXT_replicated_composites = 114,
    kSPV_EXT_shader_realtime_clock = 115,
    kSPV_EXT_ray_tracing_position_fetch = 116,
    kSPV_EXT_ray_flags_primitive_culling = 117,
    kSPV_HUAWEI_subpass_shading = 118,
    kSPV_HUAWEI_cluster_culling_shader = 119,
    kSPV_QCOM_image_processing = 120,
    kSPV_QCOM_image_processing2 = 121,
    kSPV_ARM_core_builtins = 122,
    kSPV_KHR_maximal_reconvergence = 123,
    kSPV_KHR_quad_control = 124,
    kSPV_KHR_raw_access_chains = 125,
    kSPV_NV_displacement_micromap = 126,
    kSPV_KHR_relaxed_extended_instruction = 127,
    kSPV_KHR_cooperative_matrix2 = 128,
    kSPV_MSFT_layered_driver = 129,
    kSPV_NV_descriptor_block_flags = 130,
    kSPV_KHR_float_controls2 = 131,
    kSPV_KHR_shader_quad_control = 132,
    kSPV_KHR_shader_maximal_reconvergence = 133,
    kSPV_KHR_shader_subgroup_rotate = 134,
    kSPV_KHR_shader_expect_assume = 135,
    kSPV_KHR_shader_subgroup_uniform_control_flow = 136,
    kSPV_KHR_integer_dot_product2 = 137,
    kSPV_KHR_shader_atomic_counter_ops2 = 138,
    kSPV_KHR_vulkan_memory_model2 = 139,
    kSPV_KHR_cooperative_matrix3 = 140,
    kSPV_KHR_shader_ballot = 141,
    kSPV_KHR_shader_subgroup_extended_types = 142,
    kSPV_KHR_shader_subgroup_arithmetic = 143,
    kSPV_KHR_shader_subgroup_clustered = 144,
    kSPV_KHR_shader_subgroup_quad = 145,
    kSPV_KHR_shader_subgroup_vote = 146,
    kSPV_KHR_shader_subgroup_basic = 147,
    kSPV_KHR_shader_subgroup_shuffle = 148,
    kSPV_KHR_shader_subgroup_shuffle_relative = 149,
    kSPV_KHR_shader_atomic_int64 = 150,
    kSPV_KHR_vulkan_memory_model_device_scope2 = 151,
    kSPV_KHR_physical_storage_buffer2 = 152,
    kSPV_KHR_ray_cull_mask = 153,
    kSPV_KHR_ray_tracing_position_fetch = 154,
    kSPV_KHR_shader_invocation_reorder = 155,
    kSPV_NV_shader_invocation_reorder = 156,
    kSPV_EXT_shader_atomic_float = 157,
    kSPV_EXT_shader_atomic_float2 = 158,
    kSPV_EXT_shader_atomic_float_min_max2 = 159,
    kSPV_EXT_shader_atomic_float16_add2 = 160,
    kSPV_EXT_shader_image_int642 = 161,
    kSPV_EXT_shader_tile_image2 = 162,
    kSPV_EXT_opacity_micromap2 = 163,
    kSPV_EXT_mesh_shader2 = 164,
    kSPV_EXT_replicated_composites2 = 165,
    kSPV_EXT_shader_realtime_clock2 = 166,
    kSPV_EXT_ray_tracing_position_fetch2 = 167,
    kSPV_EXT_ray_flags_primitive_culling2 = 168,
    kSPV_HUAWEI_subpass_shading2 = 169,
    kSPV_HUAWEI_cluster_culling_shader2 = 170,
    kSPV_QCOM_image_processing3 = 171,
    kSPV_ARM_core_builtins2 = 172,
    kSPV_NV_displacement_micromap2 = 173,
    kSPV_MSFT_layered_driver2 = 174,
    kSPV_NV_descriptor_block_flags2 = 175,
    kSPV_INTEL_shader_integer_functions2 = 176,
    kSPV_INTEL_shader_integer_functions3 = 177,
    kSPV_INTEL_shader_integer_functions4 = 178,
    kSPV_INTEL_shader_integer_functions5 = 179,
    kSPV_INTEL_shader_integer_functions6 = 180,
    kSPV_INTEL_shader_integer_functions7 = 181,
    kSPV_INTEL_shader_integer_functions8 = 182,
    kSPV_INTEL_shader_integer_functions9 = 183,
    kSPV_INTEL_shader_integer_functions10 = 184,
    kSPV_INTEL_shader_integer_functions11 = 185,
    kSPV_INTEL_shader_integer_functions12 = 186,
    kSPV_INTEL_shader_integer_functions13 = 187,
    kSPV_INTEL_shader_integer_functions14 = 188,
    kSPV_INTEL_shader_integer_functions15 = 189,
    kSPV_INTEL_shader_integer_functions16 = 190,
    kSPV_INTEL_shader_integer_functions17 = 191,
    kSPV_INTEL_shader_integer_functions18 = 192,
    kSPV_INTEL_shader_integer_functions19 = 193,
    kSPV_INTEL_shader_integer_functions20 = 194,
    kSPV_INTEL_shader_integer_functions21 = 195,
    kSPV_INTEL_shader_integer_functions22 = 196,
    kSPV_INTEL_shader_integer_functions23 = 197,
    kSPV_INTEL_shader_integer_functions24 = 198,
    kSPV_INTEL_shader_integer_functions25 = 199,
    kSPV_INTEL_shader_integer_functions26 = 200,
    kSPV_INTEL_shader_integer_functions27 = 201,
    kSPV_INTEL_shader_integer_functions28 = 202,
    kSPV_INTEL_shader_integer_functions29 = 203,
    kSPV_INTEL_shader_integer_functions30 = 204,
    kSPV_INTEL_shader_integer_functions31 = 205,
    kSPV_INTEL_shader_integer_functions32 = 206,
    kSPV_INTEL_shader_integer_functions33 = 207,
    kSPV_INTEL_shader_integer_functions34 = 208,
    kSPV_INTEL_shader_integer_functions35 = 209,
    kSPV_INTEL_shader_integer_functions36 = 210,
    kSPV_INTEL_shader_integer_functions37 = 211,
    kSPV_INTEL_shader_integer_functions38 = 212,
    kSPV_INTEL_shader_integer_functions39 = 213,
    kSPV_INTEL_shader_integer_functions40 = 214,
    kSPV_INTEL_shader_integer_functions41 = 215,
    kSPV_INTEL_shader_integer_functions42 = 216,
    kSPV_INTEL_shader_integer_functions43 = 217,
    kSPV_INTEL_shader_integer_functions44 = 218,
    kSPV_INTEL_shader_integer_functions45 = 219,
    kSPV_INTEL_shader_integer_functions46 = 220,
    kSPV_INTEL_shader_integer_functions47 = 221,
    kSPV_INTEL_shader_integer_functions48 = 222,
    kSPV_INTEL_shader_integer_functions49 = 223,
    kSPV_INTEL_shader_integer_functions50 = 224,
    kSPV_INTEL_shader_integer_functions51 = 225,
    kSPV_INTEL_shader_integer_functions52 = 226,
    kSPV_INTEL_shader_integer_functions53 = 227,
    kSPV_INTEL_shader_integer_functions54 = 228,
    kSPV_INTEL_shader_integer_functions55 = 229,
    kSPV_INTEL_shader_integer_functions56 = 230,
    kSPV_INTEL_shader_integer_functions57 = 231,
    kSPV_INTEL_shader_integer_functions58 = 232,
    kSPV_INTEL_shader_integer_functions59 = 233,
    kSPV_INTEL_shader_integer_functions60 = 234,
    kSPV_INTEL_shader_integer_functions61 = 235,
    kSPV_INTEL_shader_integer_functions62 = 236,
    kSPV_INTEL_shader_integer_functions63 = 237,
    kSPV_INTEL_shader_integer_functions64 = 238,
    kSPV_INTEL_shader_integer_functions65 = 239,
    kSPV_INTEL_shader_integer_functions66 = 240,
    kSPV_INTEL_shader_integer_functions67 = 241,
    kSPV_INTEL_shader_integer_functions68 = 242,
    kSPV_INTEL_shader_integer_functions69 = 243,
    kSPV_INTEL_shader_integer_functions70 = 244,
    kSPV_INTEL_shader_integer_functions71 = 245,
    kSPV_INTEL_shader_integer_functions72 = 246,
    kSPV_INTEL_shader_integer_functions73 = 247,
    kSPV_INTEL_shader_integer_functions74 = 248,
    kSPV_INTEL_shader_integer_functions75 = 249,
    kSPV_INTEL_shader_integer_functions76 = 250,
    kSPV_INTEL_shader_integer_functions77 = 251,
    kSPV_INTEL_shader_integer_functions78 = 252,
    kSPV_INTEL_shader_integer_functions79 = 253,
    kSPV_INTEL_shader_integer_functions80 = 254,
    kSPV_INTEL_shader_integer_functions81 = 255,
    kSPV_INTEL_shader_integer_functions82 = 256,
    kSPV_INTEL_shader_integer_functions83 = 257,
    kSPV_INTEL_shader_integer_functions84 = 258,
    kSPV_INTEL_shader_integer_functions85 = 259,
    kSPV_INTEL_shader_integer_functions86 = 260,
    kSPV_INTEL_shader_integer_functions87 = 261,
    kSPV_INTEL_shader_integer_functions88 = 262,
    kSPV_INTEL_shader_integer_functions89 = 263,
    kSPV_INTEL_shader_integer_functions90 = 264,
    kSPV_INTEL_shader_integer_functions91 = 265,
    kSPV_INTEL_shader_integer_functions92 = 266,
    kSPV_INTEL_shader_integer_functions93 = 267,
    kSPV_INTEL_shader_integer_functions94 = 268,
    kSPV_INTEL_shader_integer_functions95 = 269,
    kSPV_INTEL_shader_integer_functions96 = 270,
    kSPV_INTEL_shader_integer_functions97 = 271,
    kSPV_INTEL_shader_integer_functions98 = 272,
    kSPV_INTEL_shader_integer_functions99 = 273,
    kSPV_INTEL_shader_integer_functions100 = 274,
    kSPV_INTEL_shader_integer_functions101 = 275,
    kSPV_INTEL_shader_integer_functions102 = 276,
    kSPV_INTEL_shader_integer_functions103 = 277,
    kSPV_INTEL_shader_integer_functions104 = 278,
    kSPV_INTEL_shader_integer_functions105 = 279,
    kSPV_INTEL_shader_integer_functions106 = 280,
    kSPV_INTEL_shader_integer_functions107 = 281,
    kSPV_INTEL_shader_integer_functions108 = 282,
    kSPV_INTEL_shader_integer_functions109 = 283,
    kSPV_INTEL_shader_integer_functions110 = 284,
    kSPV_INTEL_shader_integer_functions111 = 285,
    kSPV_INTEL_shader_integer_functions112 = 286,
    kSPV_INTEL_shader_integer_functions113 = 287,
    kSPV_INTEL_shader_integer_functions114 = 288,
    kSPV_INTEL_shader_integer_functions115 = 289,
    kSPV_INTEL_shader_integer_functions116 = 290,
    kSPV_INTEL_shader_integer_functions117 = 291,
    kSPV_INTEL_shader_integer_functions118 = 292,
    kSPV_INTEL_shader_integer_functions119 = 293,
    kSPV_INTEL_shader_integer_functions120 = 294,
    kSPV_INTEL_shader_integer_functions121 = 295,
    kSPV_INTEL_shader_integer_functions122 = 296,
    kSPV_INTEL_shader_integer_functions123 = 297,
    kSPV_INTEL_shader_integer_functions124 = 298,
    kSPV_INTEL_shader_integer_functions125 = 299,
};

// Extension count
static const int kExtensionCount = 300;
