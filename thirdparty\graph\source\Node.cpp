#include "graph/Node.h"

namespace graph
{

void Node::AddConnect(const std::shared_ptr<Node>& conn)
{
	m_conns.push_back(conn);
}

template<typename T>
void Node::SetComponent(const std::string& key, const T& value)
{
	// For simplicity, convert everything to string
	m_components[key] = std::to_string(value);
}

// Explicit template specialization for string
template<>
void Node::SetComponent<std::string>(const std::string& key, const std::string& value)
{
	m_components[key] = value;
}

template<typename T>
T Node::GetComponent(const std::string& key) const
{
	auto it = m_components.find(key);
	if (it != m_components.end()) {
		// This is a simplified implementation - in practice you'd need proper type conversion
		return T{}; // Return default value for now
	}
	return T{};
}

// Explicit template specialization for string
template<>
std::string Node::GetComponent<std::string>(const std::string& key) const
{
	auto it = m_components.find(key);
	if (it != m_components.end()) {
		return it->second;
	}
	return "";
}

bool Node::HasComponent(const std::string& key) const
{
	return m_components.find(key) != m_components.end();
}

}