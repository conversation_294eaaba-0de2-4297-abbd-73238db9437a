var node0 = ::blueprint::nodes::for_each_f::ForEachF()

_editor.add_node(node0, 112.69432338565, 141.10273615629)

var node1 = ::blueprint::nodes::input::Input()

node1.var_name = "geo"
node1.var_type = "geo"

_editor.add_node(node1, -399.45512629822, 67.126804834827)

var node2 = ::blueprint::nodes::output::Output()

node2.var_name = "result"
node2.var_type = "array"

_editor.add_node(node2, 245.95604266199, 162.36382270037)

var node3 = ::blueprint::nodes::input::Input()

node3.var_name = "count"
node3.var_type = "int"

_editor.add_node(node3, -397.01042656435, 137.03256960204)

var node4 = ::blueprint::nodes::input::Input()

node4.var_name = "translate"
node4.var_type = "num3"

_editor.add_node(node4, -395.88144457154, -3.5214290326274)

var node5 = ::blueprint::nodes::multiply::Multiply()

_editor.add_node(node5, -258.59889648315, -3.4353175994983)

var node6 = ::geograph::nodes::translate_f::TranslateF()

_editor.add_node(node6, -110.85973321412, 48.879482579743)

var node7 = ::blueprint::nodes::add::Add()

_editor.add_node(node7, -749.69236423572, -83.235296662199)

var node8 = ::blueprint::nodes::integer::Integer()

node8.value = 1

_editor.add_node(node8, -889.68189654895, -126.13531300619)

var node9 = ::blueprint::nodes::property::Property()

node9.var_name = "include_ori"
node9.var_type = "bool"

_editor.add_node(node9, -759.96614314308, -0.47431264829827)

var node10 = ::blueprint::nodes::branch_f::BranchF()

_editor.add_node(node10, -568.23059586454, -28.573488093607)

Blueprint.connect(node0, "curr_item", node7, "a")
Blueprint.connect(node8, "v", node7, "b")
Blueprint.connect(node9, "var", node10, "cond")
Blueprint.connect(node0, "curr_item", node10, "true")
Blueprint.connect(node7, "v", node10, "false")
Blueprint.connect(node4, "var", node5, "a")
Blueprint.connect(node10, "result", node5, "b")
Blueprint.connect(node1, "var", node6, "geo")
Blueprint.connect(node5, "v", node6, "xyz")
Blueprint.connect(node3, "var", node0, "count")
Blueprint.connect(node6, "geo", node0, "eval")
Blueprint.connect(node0, "result", node2, "var")
