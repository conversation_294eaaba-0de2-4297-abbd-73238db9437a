<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::ClothMeshQuadifier Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html">ClothMeshQuadifier</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::ClothMeshQuadifier Class Reference</h1><!-- doxytag: class="nv::cloth::ClothMeshQuadifier" --><code>#include &lt;<a class="el" href="_cloth_mesh_quadifier_8h-source.html">ClothMeshQuadifier.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_cloth_mesh_quadifier-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a>&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html#35d0eeb9e4dc7156bf4993cd5c13a9d8">getDescriptor</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns a mesh descriptor with some triangle pairs converted to quads.  <a href="#35d0eeb9e4dc7156bf4993cd5c13a9d8"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html#916f94812118a1d780821611cb0e5a66">quadify</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;desc)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Convert triangles of <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a> to quads.  <a href="#916f94812118a1d780821611cb0e5a66"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html#b0e8f813c56c2eed16597c90fb438245">~ClothMeshQuadifier</a> ()</td></tr>

</table>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="b0e8f813c56c2eed16597c90fb438245"></a><!-- doxytag: member="nv::cloth::ClothMeshQuadifier::~ClothMeshQuadifier" ref="b0e8f813c56c2eed16597c90fb438245" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::ClothMeshQuadifier::~ClothMeshQuadifier           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="35d0eeb9e4dc7156bf4993cd5c13a9d8"></a><!-- doxytag: member="nv::cloth::ClothMeshQuadifier::getDescriptor" ref="35d0eeb9e4dc7156bf4993cd5c13a9d8" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> nv::cloth::ClothMeshQuadifier::getDescriptor           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns a mesh descriptor with some triangle pairs converted to quads. 
<p>
<dl class="note" compact><dt><b>Note:</b></dt><dd>The returned descriptor is valid only within the lifespan of <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_quadifier.html">ClothMeshQuadifier</a> class. </dd></dl>

</div>
</div><p>
<a class="anchor" name="916f94812118a1d780821611cb0e5a66"></a><!-- doxytag: member="nv::cloth::ClothMeshQuadifier::quadify" ref="916f94812118a1d780821611cb0e5a66" args="(const ClothMeshDesc &amp;desc)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::ClothMeshQuadifier::quadify           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html">ClothMeshDesc</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>desc</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Convert triangles of <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a> to quads. 
<p>
In NvCloth, quad dominant mesh representations are preferable to pre-triangulated versions. In cases where the mesh has been already triangulated, this class provides a meachanism to convert (quadify) some triangles back to quad representations. <dl class="see" compact><dt><b>See also:</b></dt><dd><a class="el" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html" title="Use NvClothCreateFabricCooker() to create an implemented instance.">ClothFabricCooker</a> </dd></dl>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>desc</em>&nbsp;</td><td>The cloth mesh descriptor prepared for cooking </td></tr>
  </table>
</dl>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_cloth_mesh_quadifier_8h-source.html">ClothMeshQuadifier.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
