<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::Factory Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Factory Class Reference</h1><!-- doxytag: class="nv::cloth::Factory" -->abstract factory to create context-specific simulation components such as cloth, solver, collision, etc.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_factory_8h-source.html">Factory.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_factory-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#d7b0ba6b9fd6a304b6a2b2560a96b472">clone</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Create a copy of a cloth instance.  <a href="#d7b0ba6b9fd6a304b6a2b2560a96b472"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#942308b0a218726c7316382228771e7e">createCloth</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt; particles, <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;fabric)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Create cloth object.  <a href="#942308b0a218726c7316382228771e7e"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#b49c2a8f3dcdd015505fa18e8337bb7a">createFabric</a> (uint32_t numParticles, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; phaseIndices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; sets, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt; restvalues, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt; stiffnessValues, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; indices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; anchors, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt; tetherLengths, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt; triangles)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Create fabric data used to setup cloth object.  <a href="#b49c2a8f3dcdd015505fa18e8337bb7a"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#28a6ad0896774886b214be9573fc3ca2">createSolver</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Create cloth solver object.  <a href="#28a6ad0896774886b214be9573fc3ca2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#8c4537f687c38c8055ef54299463b7d1">extractCollisionData</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; spheres, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; capsules, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; planes, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; convexes, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec3 &gt; triangles) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract current collision spheres and capsules from a cloth object.  <a href="#8c4537f687c38c8055ef54299463b7d1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#b745e0c37ad99ee0efde69fe0883c00c">extractFabricData</a> (const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;fabric, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; phaseIndices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; sets, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt; restvalues, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt; stiffnessValues, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; indices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; anchors, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt; tetherLengths, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; triangles) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract original data from a fabric object.  <a href="#b745e0c37ad99ee0efde69fe0883c00c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#51b24b732ffcf2fba082dc73ce0d6953">extractMotionConstraints</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; destConstraints) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract current motion constraints from a cloth object Use the getNum* methods on <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> to get the memory requirements before calling this function.  <a href="#51b24b732ffcf2fba082dc73ce0d6953"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#********************************">extractParticleAccelerations</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; destAccelerations) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract current particle accelerations from a cloth object.  <a href="#********************************"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#f268db5221c421f9167c5f0fbe894e19">extractRestPositions</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; destRestPositions) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract particle rest positions from cloth object.  <a href="#f268db5221c421f9167c5f0fbe894e19"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#615dc5d0f0642e8ce9a2cb94d50c0ebe">extractSelfCollisionIndices</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt; destIndices) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract self collision indices from cloth object.  <a href="#615dc5d0f0642e8ce9a2cb94d50c0ebe"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#b52ffcc03824042c4eef65e5df80ee21">extractSeparationConstraints</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt; destConstraints) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract current separation constraints from a cloth object.  <a href="#b52ffcc03824042c4eef65e5df80ee21"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#41571a2afa49108e0bc03516cdd080c4">extractVirtualParticles</a> (const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;cloth, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t[4]&gt; destIndices, <a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec3 &gt; destWeights) const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Extract virtual particles from a cloth object.  <a href="#41571a2afa49108e0bc03516cdd080c4"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual Platform&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#de78d96c7cd81520176d4bfd4e488b04">getPlatform</a> () const =0</td></tr>

<tr><td colspan="2"><br><h2>Protected Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#7568b20d666221ff9e5b28038c1c63c6">Factory</a> (const <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#0fe2d6c93b96ccbf2a38b5d7a0c55949">Factory</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#0daf294ea3c4c8713cbdfda320e46e16">operator=</a> (const <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#edab1136aacbffe558d8b00be6cb2257">~Factory</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Friends</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">NV_CLOTH_IMPORT void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_factory.html#a9517fa38c45084af39183dd3dd34c8a">NV_CLOTH_CALL_CONV::NvClothDestroyFactory</a> (<a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> *)</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
abstract factory to create context-specific simulation components such as cloth, solver, collision, etc. 
<p>
<hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="0fe2d6c93b96ccbf2a38b5d7a0c55949"></a><!-- doxytag: member="nv::cloth::Factory::Factory" ref="0fe2d6c93b96ccbf2a38b5d7a0c55949" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Factory::Factory           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="7568b20d666221ff9e5b28038c1c63c6"></a><!-- doxytag: member="nv::cloth::Factory::Factory" ref="7568b20d666221ff9e5b28038c1c63c6" args="(const Factory &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Factory::Factory           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="edab1136aacbffe558d8b00be6cb2257"></a><!-- doxytag: member="nv::cloth::Factory::~Factory" ref="edab1136aacbffe558d8b00be6cb2257" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::Factory::~Factory           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="d7b0ba6b9fd6a304b6a2b2560a96b472"></a><!-- doxytag: member="nv::cloth::Factory::clone" ref="d7b0ba6b9fd6a304b6a2b2560a96b472" args="(const Cloth &amp;cloth)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* nv::cloth::Factory::clone           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Create a copy of a cloth instance. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to be cloned, need not match the factory type </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="942308b0a218726c7316382228771e7e"></a><!-- doxytag: member="nv::cloth::Factory::createCloth" ref="942308b0a218726c7316382228771e7e" args="(Range&lt; const physx::PxVec4 &gt; particles, Fabric &amp;fabric)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* nv::cloth::Factory::createCloth           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>particles</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>fabric</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Create cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>particles</em>&nbsp;</td><td>initial particle positions. </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>fabric</em>&nbsp;</td><td>edge distance constraint structure </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="b49c2a8f3dcdd015505fa18e8337bb7a"></a><!-- doxytag: member="nv::cloth::Factory::createFabric" ref="b49c2a8f3dcdd015505fa18e8337bb7a" args="(uint32_t numParticles, Range&lt; const uint32_t &gt; phaseIndices, Range&lt; const uint32_t &gt; sets, Range&lt; const float &gt; restvalues, Range&lt; const float &gt; stiffnessValues, Range&lt; const uint32_t &gt; indices, Range&lt; const uint32_t &gt; anchors, Range&lt; const float &gt; tetherLengths, Range&lt; const uint32_t &gt; triangles)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a>* nv::cloth::Factory::createFabric           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>numParticles</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>phaseIndices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>sets</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td>
          <td class="paramname"> <em>restvalues</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td>
          <td class="paramname"> <em>stiffnessValues</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>indices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>anchors</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const float &gt;&nbsp;</td>
          <td class="paramname"> <em>tetherLengths</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; const uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>triangles</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Create fabric data used to setup cloth object. 
<p>
Look at the cooking extension for helper functions to create fabrics from meshes. The returned fabric will have a refcount of 1. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>numParticles</em>&nbsp;</td><td>number of particles, must be larger than any particle index </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>phaseIndices</em>&nbsp;</td><td>map from phase to set index </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>sets</em>&nbsp;</td><td>inclusive prefix sum of restvalue count per set </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>restvalues</em>&nbsp;</td><td>array of constraint rest values </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>indices</em>&nbsp;</td><td>array of particle index pair per constraint </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="28a6ad0896774886b214be9573fc3ca2"></a><!-- doxytag: member="nv::cloth::Factory::createSolver" ref="28a6ad0896774886b214be9573fc3ca2" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a>* nv::cloth::Factory::createSolver           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Create cloth solver object. 
<p>

</div>
</div><p>
<a class="anchor" name="8c4537f687c38c8055ef54299463b7d1"></a><!-- doxytag: member="nv::cloth::Factory::extractCollisionData" ref="8c4537f687c38c8055ef54299463b7d1" args="(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; spheres, Range&lt; uint32_t &gt; capsules, Range&lt; physx::PxVec4 &gt; planes, Range&lt; uint32_t &gt; convexes, Range&lt; physx::PxVec3 &gt; triangles) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractCollisionData           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>spheres</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>capsules</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>planes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>convexes</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname"> <em>triangles</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract current collision spheres and capsules from a cloth object. 
<p>
Use the getNum* methods on <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> to get the memory requirements before calling this function. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>spheres</em>&nbsp;</td><td>pre-allocated memory range to write spheres </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>capsules</em>&nbsp;</td><td>pre-allocated memory range to write capsules </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>planes</em>&nbsp;</td><td>pre-allocated memory range to write planes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>convexes</em>&nbsp;</td><td>pre-allocated memory range to write convexes </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>triangles</em>&nbsp;</td><td>pre-allocated memory range to write triangles </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="b745e0c37ad99ee0efde69fe0883c00c"></a><!-- doxytag: member="nv::cloth::Factory::extractFabricData" ref="b745e0c37ad99ee0efde69fe0883c00c" args="(const Fabric &amp;fabric, Range&lt; uint32_t &gt; phaseIndices, Range&lt; uint32_t &gt; sets, Range&lt; float &gt; restvalues, Range&lt; float &gt; stiffnessValues, Range&lt; uint32_t &gt; indices, Range&lt; uint32_t &gt; anchors, Range&lt; float &gt; tetherLengths, Range&lt; uint32_t &gt; triangles) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractFabricData           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_fabric.html">Fabric</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>fabric</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>phaseIndices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>sets</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt;&nbsp;</td>
          <td class="paramname"> <em>restvalues</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt;&nbsp;</td>
          <td class="paramname"> <em>stiffnessValues</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>indices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>anchors</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; float &gt;&nbsp;</td>
          <td class="paramname"> <em>tetherLengths</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>triangles</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract original data from a fabric object. 
<p>
Use the getNum* methods on <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> to get the memory requirements before calling this function. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>fabric</em>&nbsp;</td><td>to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>phaseIndices</em>&nbsp;</td><td>pre-allocated memory range to write phase =&gt; set indices </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>sets</em>&nbsp;</td><td>pre-allocated memory range to write sets </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>restvalues</em>&nbsp;</td><td>pre-allocated memory range to write restvalues </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>indices</em>&nbsp;</td><td>pre-allocated memory range to write indices </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="51b24b732ffcf2fba082dc73ce0d6953"></a><!-- doxytag: member="nv::cloth::Factory::extractMotionConstraints" ref="51b24b732ffcf2fba082dc73ce0d6953" args="(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destConstraints) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractMotionConstraints           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>destConstraints</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract current motion constraints from a cloth object Use the getNum* methods on <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> to get the memory requirements before calling this function. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destConstraints</em>&nbsp;</td><td>pre-allocated memory range to write constraints </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="********************************"></a><!-- doxytag: member="nv::cloth::Factory::extractParticleAccelerations" ref="********************************" args="(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destAccelerations) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractParticleAccelerations           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>destAccelerations</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract current particle accelerations from a cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destAccelerations</em>&nbsp;</td><td>pre-allocated memory range to write accelerations </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="f268db5221c421f9167c5f0fbe894e19"></a><!-- doxytag: member="nv::cloth::Factory::extractRestPositions" ref="f268db5221c421f9167c5f0fbe894e19" args="(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destRestPositions) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractRestPositions           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>destRestPositions</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract particle rest positions from cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destRestPositions</em>&nbsp;</td><td>pre-allocated memory range to write rest positions </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="615dc5d0f0642e8ce9a2cb94d50c0ebe"></a><!-- doxytag: member="nv::cloth::Factory::extractSelfCollisionIndices" ref="615dc5d0f0642e8ce9a2cb94d50c0ebe" args="(const Cloth &amp;cloth, Range&lt; uint32_t &gt; destIndices) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractSelfCollisionIndices           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t &gt;&nbsp;</td>
          <td class="paramname"> <em>destIndices</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract self collision indices from cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destIndices</em>&nbsp;</td><td>pre-allocated memory range to write indices </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="b52ffcc03824042c4eef65e5df80ee21"></a><!-- doxytag: member="nv::cloth::Factory::extractSeparationConstraints" ref="b52ffcc03824042c4eef65e5df80ee21" args="(const Cloth &amp;cloth, Range&lt; physx::PxVec4 &gt; destConstraints) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractSeparationConstraints           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec4 &gt;&nbsp;</td>
          <td class="paramname"> <em>destConstraints</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract current separation constraints from a cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destConstraints</em>&nbsp;</td><td>pre-allocated memory range to write constraints </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="41571a2afa49108e0bc03516cdd080c4"></a><!-- doxytag: member="nv::cloth::Factory::extractVirtualParticles" ref="41571a2afa49108e0bc03516cdd080c4" args="(const Cloth &amp;cloth, Range&lt; uint32_t[4]&gt; destIndices, Range&lt; physx::PxVec3 &gt; destWeights) const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Factory::extractVirtualParticles           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> &amp;&nbsp;</td>
          <td class="paramname"> <em>cloth</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; uint32_t[4]&gt;&nbsp;</td>
          <td class="paramname"> <em>destIndices</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; physx::PxVec3 &gt;&nbsp;</td>
          <td class="paramname"> <em>destWeights</em></td><td>&nbsp;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td><td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Extract virtual particles from a cloth object. 
<p>
<dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>cloth</em>&nbsp;</td><td>the instance to extract from, must match factory type </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destIndices</em>&nbsp;</td><td>pre-allocated memory range to write indices </td></tr>
    <tr><td valign="top"></td><td valign="top"><em>destWeights</em>&nbsp;</td><td>pre-allocated memory range to write weights </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="de78d96c7cd81520176d4bfd4e488b04"></a><!-- doxytag: member="nv::cloth::Factory::getPlatform" ref="de78d96c7cd81520176d4bfd4e488b04" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual Platform nv::cloth::Factory::getPlatform           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="0daf294ea3c4c8713cbdfda320e46e16"></a><!-- doxytag: member="nv::cloth::Factory::operator=" ref="0daf294ea3c4c8713cbdfda320e46e16" args="(const Factory &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a>&amp; nv::cloth::Factory::operator=           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_factory.html">Factory</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Friends And Related Function Documentation</h2>
<a class="anchor" name="a9517fa38c45084af39183dd3dd34c8a"></a><!-- doxytag: member="nv::cloth::Factory::NV_CLOTH_CALL_CONV::NvClothDestroyFactory" ref="a9517fa38c45084af39183dd3dd34c8a" args="(nv::cloth::Factory *)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">NV_CLOTH_IMPORT void NV_CLOTH_CALL_CONV::NvClothDestroyFactory           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_factory.html">nv::cloth::Factory</a> *&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [friend]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_factory_8h-source.html">Factory.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
