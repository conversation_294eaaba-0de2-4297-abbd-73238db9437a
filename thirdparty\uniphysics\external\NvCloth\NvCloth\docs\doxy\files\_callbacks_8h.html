<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Callbacks.h File Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>Callbacks.h File Reference</h1>All functions to initialize and use user provided callbacks are declared in this header.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;foundation/PxPreprocessor.h&gt;</code><br>
<code>#include &lt;foundation/PxProfiler.h&gt;</code><br>
<code>#include &lt;foundation/PxAssert.h&gt;</code><br>

<p>
<a href="_callbacks_8h-source.html">Go to the source code of this file.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Classes</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">class &nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a></td></tr>

<tr><td colspan="2"><br><h2>Defines</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(ret_type)&nbsp;&nbsp;&nbsp;NV_CLOTH_LINKAGE ret_type NV_CLOTH_CALL_CONV</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#95d1d44fde08004dd6fa0be04be6a445">NV_CLOTH_ASSERT</a>(exp)&nbsp;&nbsp;&nbsp;((void)0)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#7580256d644389afb1a6fbf123cd9747">NV_CLOTH_ASSERT_WITH_MESSAGE</a>(message, exp)&nbsp;&nbsp;&nbsp;((void)0)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#0a1f306c4d84c8362b056e8fd313629a">NV_CLOTH_CALL_CONV</a>&nbsp;&nbsp;&nbsp;PX_CALL_CONV</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#d43b3e4b2ee5d1c328c332ee9d1666e2">NV_CLOTH_DLL_ID</a>&nbsp;&nbsp;&nbsp;0x2</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#bd597bda23283ca6fe84282f6e2671dc">NV_CLOTH_IMPORT</a>&nbsp;&nbsp;&nbsp;PX_DLL_IMPORT</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#71c40d2ed1c52507e10baa313de4d292">NV_CLOTH_LINKAGE</a>&nbsp;&nbsp;&nbsp;PX_C_EXPORT NV_CLOTH_IMPORT</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#0369e7cdaf37f1ecd6dd5e00b2ebf7da">NV_CLOTH_LOG_ERROR</a>(...)&nbsp;&nbsp;&nbsp;nv::cloth::LogErrorFn(__FILE__,__LINE__,__VA_ARGS__)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">arguments: NV_CLOTH_LOG_ERROR("format %s %s\n","additional","arguments");  <a href="#0369e7cdaf37f1ecd6dd5e00b2ebf7da"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#db5608a2350e209f80ac3752e7ec3a42">NV_CLOTH_LOG_INFO</a>(...)&nbsp;&nbsp;&nbsp;nv::cloth::LogInfoFn(__FILE__,__LINE__,__VA_ARGS__)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#e12b7837f3ff9076845affea652c9220">NV_CLOTH_LOG_INVALID_PARAMETER</a>(...)&nbsp;&nbsp;&nbsp;nv::cloth::LogInvalidParameterFn(__FILE__,__LINE__,__VA_ARGS__)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#d7fe263c5c514ce5bc018bec64e7fba5">NV_CLOTH_LOG_WARNING</a>(...)&nbsp;&nbsp;&nbsp;nv::cloth::LogWarningFn(__FILE__,__LINE__,__VA_ARGS__)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#d31f06d741b7e7340058e66b64e8d1da">NV_CLOTH_PROFILE_START_CROSSTHREAD</a>(x, y)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#5c4305c0f359cd51e90391ce3847d4de">NV_CLOTH_PROFILE_STOP_CROSSTHREAD</a>(profilerData, x, y)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">#define&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#07a777d717c8c0eb9d8ae6d1b5db94ee">NV_CLOTH_PROFILE_ZONE</a>(x, y)&nbsp;&nbsp;&nbsp;<a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a> PX_CONCAT(_scoped, __LINE__)(x, false, y, __FILE__, __LINE__, nv::cloth::GetNvClothProfiler())</td></tr>

<tr><td colspan="2"><br><h2>Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_C_EXPORT PX_DLL_IMPORT <br class="typebreak">
physx::PxAllocatorCallback <br class="typebreak">
*PX_CALL_CONV&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="_callbacks_8h.html#083c7c54b10663c35b00c69540f1bb9b">GetNvClothAllocator</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_C_EXPORT PX_DLL_IMPORT <br class="typebreak">
physx::PxAssertHandler <br class="typebreak">
*PX_CALL_CONV&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#f21e7b5617864e8c76d48f92414b0aed">nv::cloth::GetNvClothAssertHandler</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">physx::PxProfilerCallback *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#c8af0dbdbd44112960732602ab57be65">nv::cloth::GetNvClothProfiler</a> ()</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">PX_C_EXPORT PX_DLL_IMPORT void <br class="typebreak">
PX_CALL_CONV&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#346db67917fe962e5288cf370de55c40">nv::cloth::InitializeNvCloth</a> (physx::PxAllocatorCallback *allocatorCallback, physx::PxErrorCallback *errorCallback, physx::PxAssertHandler *assertHandler, physx::PxProfilerCallback *profilerCallback, int autoDllIDCheck=0x2)</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Initialize the library by passing in callback functions.  <a href="#346db67917fe962e5288cf370de55c40"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#2c1737f5cc2aac451ac2640dad8ed079">nv::cloth::LogErrorFn</a> (const char *fileName, int lineNumber, const char *msg,...)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#0a13b5086b569935248a14d0f1973efb">nv::cloth::LogInfoFn</a> (const char *fileName, int lineNumber, const char *msg,...)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#50c14b9e7a8caded578553eea2fd2820">nv::cloth::LogInvalidParameterFn</a> (const char *fileName, int lineNumber, const char *msg,...)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="namespacenv_1_1cloth.html#e36aeb8339676fce025adc6b33a54172">nv::cloth::LogWarningFn</a> (const char *fileName, int lineNumber, const char *msg,...)</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
All functions to initialize and use user provided callbacks are declared in this header. 
<p>
Initialize the callbacks with InitializeNvCloth(...) before using any other NvCloth API. The other functions defined in this header are used to access the functionality provided by the callbacks, and are mostly for internal use. <hr><h2>Define Documentation</h2>
<a class="anchor" name="ca1463b4545d714cb99ba3e96d4245ff"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_API" ref="ca1463b4545d714cb99ba3e96d4245ff" args="(ret_type)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_API          </td>
          <td>(</td>
          <td class="paramtype">ret_type&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;NV_CLOTH_LINKAGE ret_type NV_CLOTH_CALL_CONV</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="95d1d44fde08004dd6fa0be04be6a445"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_ASSERT" ref="95d1d44fde08004dd6fa0be04be6a445" args="(exp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_ASSERT          </td>
          <td>(</td>
          <td class="paramtype">exp&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((void)0)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="7580256d644389afb1a6fbf123cd9747"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_ASSERT_WITH_MESSAGE" ref="7580256d644389afb1a6fbf123cd9747" args="(message, exp)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_ASSERT_WITH_MESSAGE          </td>
          <td>(</td>
          <td class="paramtype">message,         <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">exp&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;((void)0)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="0a1f306c4d84c8362b056e8fd313629a"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_CALL_CONV" ref="0a1f306c4d84c8362b056e8fd313629a" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_CALL_CONV&nbsp;&nbsp;&nbsp;PX_CALL_CONV          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="d43b3e4b2ee5d1c328c332ee9d1666e2"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_DLL_ID" ref="d43b3e4b2ee5d1c328c332ee9d1666e2" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_DLL_ID&nbsp;&nbsp;&nbsp;0x2          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="bd597bda23283ca6fe84282f6e2671dc"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_IMPORT" ref="bd597bda23283ca6fe84282f6e2671dc" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_IMPORT&nbsp;&nbsp;&nbsp;PX_DLL_IMPORT          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="71c40d2ed1c52507e10baa313de4d292"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_LINKAGE" ref="71c40d2ed1c52507e10baa313de4d292" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_LINKAGE&nbsp;&nbsp;&nbsp;PX_C_EXPORT NV_CLOTH_IMPORT          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="0369e7cdaf37f1ecd6dd5e00b2ebf7da"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_LOG_ERROR" ref="0369e7cdaf37f1ecd6dd5e00b2ebf7da" args="(...)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_LOG_ERROR          </td>
          <td>(</td>
          <td class="paramtype"> <em>...</em>&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;nv::cloth::LogErrorFn(__FILE__,__LINE__,__VA_ARGS__)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
arguments: NV_CLOTH_LOG_ERROR("format %s %s\n","additional","arguments"); 
<p>

</div>
</div><p>
<a class="anchor" name="db5608a2350e209f80ac3752e7ec3a42"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_LOG_INFO" ref="db5608a2350e209f80ac3752e7ec3a42" args="(...)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_LOG_INFO          </td>
          <td>(</td>
          <td class="paramtype"> <em>...</em>&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;nv::cloth::LogInfoFn(__FILE__,__LINE__,__VA_ARGS__)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="e12b7837f3ff9076845affea652c9220"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_LOG_INVALID_PARAMETER" ref="e12b7837f3ff9076845affea652c9220" args="(...)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_LOG_INVALID_PARAMETER          </td>
          <td>(</td>
          <td class="paramtype"> <em>...</em>&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;nv::cloth::LogInvalidParameterFn(__FILE__,__LINE__,__VA_ARGS__)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="d7fe263c5c514ce5bc018bec64e7fba5"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_LOG_WARNING" ref="d7fe263c5c514ce5bc018bec64e7fba5" args="(...)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_LOG_WARNING          </td>
          <td>(</td>
          <td class="paramtype"> <em>...</em>&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;nv::cloth::LogWarningFn(__FILE__,__LINE__,__VA_ARGS__)</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="d31f06d741b7e7340058e66b64e8d1da"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_PROFILE_START_CROSSTHREAD" ref="d31f06d741b7e7340058e66b64e8d1da" args="(x, y)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_PROFILE_START_CROSSTHREAD          </td>
          <td>(</td>
          <td class="paramtype">x,         <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">y&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<b>Value:</b><div class="fragment"><pre class="fragment">(GetNvClothProfiler()!=<span class="keyword">nullptr</span>?                                                                                        \
    GetNvClothProfiler()-&gt;zoneStart(x, <span class="keyword">true</span>, y):<span class="keyword">nullptr</span>)
</pre></div>
</div>
</div><p>
<a class="anchor" name="5c4305c0f359cd51e90391ce3847d4de"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_PROFILE_STOP_CROSSTHREAD" ref="5c4305c0f359cd51e90391ce3847d4de" args="(profilerData, x, y)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_PROFILE_STOP_CROSSTHREAD          </td>
          <td>(</td>
          <td class="paramtype">profilerData,         <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">x,         <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">y&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<b>Value:</b><div class="fragment"><pre class="fragment"><span class="keywordflow">if</span> (GetNvClothProfiler())                                                                                           \
    GetNvClothProfiler()-&gt;zoneEnd(profilerData, x, <span class="keyword">true</span>, y)
</pre></div>
</div>
</div><p>
<a class="anchor" name="07a777d717c8c0eb9d8ae6d1b5db94ee"></a><!-- doxytag: member="Callbacks.h::NV_CLOTH_PROFILE_ZONE" ref="07a777d717c8c0eb9d8ae6d1b5db94ee" args="(x, y)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">#define NV_CLOTH_PROFILE_ZONE          </td>
          <td>(</td>
          <td class="paramtype">x,         <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">y&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td>&nbsp;&nbsp;&nbsp;<a class="el" href="classnv_1_1cloth_1_1_nv_cloth_profile_scoped.html">nv::cloth::NvClothProfileScoped</a> PX_CONCAT(_scoped, __LINE__)(x, false, y, __FILE__, __LINE__, nv::cloth::GetNvClothProfiler())</td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Function Documentation</h2>
<a class="anchor" name="083c7c54b10663c35b00c69540f1bb9b"></a><!-- doxytag: member="Callbacks.h::GetNvClothAllocator" ref="083c7c54b10663c35b00c69540f1bb9b" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">PX_C_EXPORT PX_DLL_IMPORT physx::PxAllocatorCallback* PX_CALL_CONV GetNvClothAllocator           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
