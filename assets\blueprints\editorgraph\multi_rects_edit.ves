var node0 = ::geograph::nodes::rect::Rect()
node0.query_param("x").value = 0
node0.query_param("y").value = 0
node0.query_param("w").value = 100
node0.query_param("h").value = 100
node0.query_param("offset").value = false
node0.query_param("fill").value = false
node0.query_param("color").value.set(0.6748480796814, 0, 0)

_editor.add_node(node0, -1185.2222902399, -518.37482505212)

var node1 = ::geograph::nodes::draw_geometry::DrawGeometry()
node1.query_param("skip").value = false

_editor.add_node(node1, -1037.7976305876, -329.20797606345)

var node2 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node2, 163.45754564434, 192.08108811505)

var node3 = ::editorgraph::nodes::mouse_left_down::MouseLeftDown()

_editor.add_node(node3, -469.17448118424, 201.6521870388)

var node4 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node4, -181.58054888355, 126.34391677634)

var node5 = ::blueprint::nodes::number2::Number2()

node5.value.set(-352.89180218324, -470.08187611507)

_editor.add_node(node5, -1242.7323712423, 359.08944576382)

var node6 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node6, -334.55871890729, 160.00494723687)

var node7 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node7, 113.51347037222, -149.62610422384)

var node8 = ::editorgraph::nodes::mouse_left_drag::MouseLeftDrag()

_editor.add_node(node8, -475.34024863604, -172.13157661486)

var node9 = ::editorgraph::nodes::coord_trans::CoordTrans()

_editor.add_node(node9, -337.6245461567, -214.38808769491)

var node10 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node10, -184.29313509451, -267.49056093024)

var node11 = ::blueprint::nodes::number2::Number2()

node11.value.set(-352.89180218324, -470.08187611507)

_editor.add_node(node11, -891.27839291795, 358.93177002109)

var node12 = ::blueprint::nodes::subtract::Subtract()

_editor.add_node(node12, -1047.7521121065, 255.69325509494)

var node13 = ::editorgraph::nodes::trigger::Trigger()

_editor.add_node(node13, 262.63162057827, -458.10792596873)

var node14 = ::editorgraph::nodes::mouse_left_up::MouseLeftUp()

_editor.add_node(node14, 106.42234855114, -442.87346590288)

var node15 = ::blueprint::nodes::list_add::ListAdd()
node15.query_param("unique").value = false

_editor.add_node(node15, -196.32326165991, -524.93207528748)

var node16 = ::geograph::nodes::rect::Rect()
node16.query_param("x").value = 0
node16.query_param("y").value = 0
node16.query_param("w").value = 100
node16.query_param("h").value = 100
node16.query_param("offset").value = false
node16.query_param("fill").value = true
node16.query_param("color").value.set(0.60412019491196, 0.60412019491196, 0)

_editor.add_node(node16, -344.46634067674, -638.70588129126)

var node17 = ::geograph::nodes::draw_geometry::DrawGeometry()
node17.query_param("skip").value = false

_editor.add_node(node17, -1037.7022720049, -504.73409018456)

var node18 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node18, -23.455357013472, 82.514671720154)

var node19 = ::blueprint::nodes::store::Store()

node19.var_name = "first_pos"

_editor.add_node(node19, -1093.4149057649, 370.01718580979)

var node20 = ::blueprint::nodes::load::Load()

node20.var_name = "first_pos"

_editor.add_node(node20, -333.0769570695, 100.51793007036)

var node21 = ::blueprint::nodes::load::Load()

node21.var_name = "first_pos"

_editor.add_node(node21, -180.0437489553, 61.944601745794)

var node22 = ::blueprint::nodes::load::Load()

node22.var_name = "first_pos"

_editor.add_node(node22, -1202.2603950954, 227.87586979471)

var node23 = ::blueprint::nodes::load::Load()

node23.var_name = "first_pos"

_editor.add_node(node23, -1325.545190347, -419.39778700468)

var node24 = ::blueprint::nodes::load::Load()

node24.var_name = "first_pos"

_editor.add_node(node24, -491.17149738473, -546.43916718441)

var node25 = ::blueprint::nodes::store::Store()

node25.var_name = "second_pos"

_editor.add_node(node25, -739.90861329255, 368.8623298515)

var node26 = ::blueprint::nodes::load::Load()

node26.var_name = "second_pos"

_editor.add_node(node26, -179.33993582175, 19.040162825338)

var node27 = ::blueprint::nodes::load::Load()

node27.var_name = "second_pos"

_editor.add_node(node27, -336.57975502809, -267.09145450259)

var node28 = ::blueprint::nodes::load::Load()

node28.var_name = "second_pos"

_editor.add_node(node28, -1198.6370430447, 277.22637943827)

var node29 = ::blueprint::nodes::store::Store()

node29.var_name = "size"

_editor.add_node(node29, -895.91072468524, 253.86843876444)

var node30 = ::blueprint::nodes::load::Load()

node30.var_name = "size"

_editor.add_node(node30, -1326.1955199369, -465.40521803495)

var node31 = ::blueprint::nodes::load::Load()

node31.var_name = "size"

_editor.add_node(node31, -490.8075676897, -596.45452464642)

var node32 = ::blueprint::nodes::store::Store()

node32.var_name = "rects"

_editor.add_node(node32, -1105.2960369028, 164.00365130803)

var node33 = ::blueprint::nodes::load::Load()

node33.var_name = "rects"

_editor.add_node(node33, -1191.1228028065, -305.96755109064)

var node34 = ::blueprint::nodes::load::Load()

node34.var_name = "rects"

_editor.add_node(node34, -349.5824762589, -493.28429029421)

var node35 = ::blueprint::nodes::commentary::Commentary()

node35.set_size(725.32086181641, 528.47497558594)
node35.title = "Variates"

_editor.add_node(node35, -986.38263710937, 432.91735231445)

var node36 = ::blueprint::nodes::commentary::Commentary()

node36.set_size(809.62915039063, 365.89584350586)
node36.title = "Draw"

_editor.add_node(node36, -1010.2836054971, -258.74992911755)

var node37 = ::blueprint::nodes::commentary::Commentary()

node37.set_size(803.33959960938, 295.6484375)
node37.title = "First Point"

_editor.add_node(node37, -153.53536104565, 277.64134711665)

var node38 = ::blueprint::nodes::commentary::Commentary()

node38.set_size(798.20104980469, 244.79426574707)
node38.title = "Second Point"

_editor.add_node(node38, -176.39305842225, -74.911332944251)

var node39 = ::blueprint::nodes::commentary::Commentary()

node39.set_size(916.25573730469, 370.12246704102)
node39.title = "Finish"

_editor.add_node(node39, -115.84343110085, -382.23035147195)

var node40 = ::blueprint::nodes::subgraph::Subgraph()
node40.load_from_file(_editor, "right_down_remove.ves")

_editor.add_node(node40, -125.3247570244, -889.21509701256)

var node41 = ::blueprint::nodes::load::Load()

node41.var_name = "rects"

_editor.add_node(node41, -319.91091103168, -844.94467039114)

var node42 = ::blueprint::nodes::assignment::Assignment()

_editor.add_node(node42, 110.60446319007, -518.66541951678)

var node43 = ::blueprint::nodes::load::Load()

node43.var_name = "first_pos"

_editor.add_node(node43, -48.023406929481, -571.13358388361)

var node44 = ::blueprint::nodes::load::Load()

node44.var_name = "second_pos"

_editor.add_node(node44, -49.137775614111, -619.49256825861)

var node45 = ::blueprint::nodes::subgraph::Subgraph()
node45.load_from_file(_editor, "left_down_select.ves")

_editor.add_node(node45, -165.14448056627, 377.42088733997)

var node46 = ::blueprint::nodes::load::Load()

node46.var_name = "rects"

_editor.add_node(node46, -357.42294509541, 416.62116745498)

var node47 = ::blueprint::nodes::array::Array()
node47.query_param("serialize").value = false

_editor.add_node(node47, -904.41706364755, 169.43951203616)

var node48 = ::geograph::nodes::draw_geometry::DrawGeometry()
node48.query_param("skip").value = false

_editor.add_node(node48, -688.13917767545, -401.82675103974)

var node49 = ::blueprint::nodes::number3::Number3()

node49.value.set(0, 0.54259234666824, 0)

_editor.add_node(node49, -865.07002511534, -521.93310958744)

var node50 = ::blueprint::nodes::store::Store()

node50.var_name = "selected"

_editor.add_node(node50, -749.67062279354, 168.31586765067)

var node51 = ::blueprint::nodes::load::Load()

node51.var_name = "selected"

_editor.add_node(node51, -861.38151446704, -446.32038745238)

var node52 = ::blueprint::nodes::load::Load()

node52.var_name = "selected"

_editor.add_node(node52, -357.71201686205, 371.15257620424)

var node53 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node53, -38.235897978476, -228.31526928106)

var node54 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node54, -49.292674536551, -491.40963949279)

var node55 = ::blueprint::nodes::compare::Compare()

node55.cmp = "greater"

_editor.add_node(node55, -983.5154263801, 54.177793289231)

var node56 = ::blueprint::nodes::number2::Number2()

node56.value.set(0, 0)

_editor.add_node(node56, -1126.3374602312, 30.366668409481)

var node57 = ::blueprint::nodes::load::Load()

node57.var_name = "size"

_editor.add_node(node57, -1260.2673078223, 93.356616653501)

var node58 = ::blueprint::nodes::abs::Abs()

_editor.add_node(node58, -1129.77690774, 95.433376057321)

var node59 = ::blueprint::nodes::input::Input()

node59.var_name = "rects"
node59.var_type = "array"

_editor.add_node(node59, -1256.9763217839, 168.90335363013)

var node60 = ::blueprint::nodes::store::Store()

node60.var_name = "selected"

_editor.add_node(node60, 25.49771631185, 380.8486480369)

var node61 = ::blueprint::nodes::load::Load()

node61.var_name = "selected"

_editor.add_node(node61, -186.20722419171, -200.68710503697)

var node62 = ::blueprint::nodes::subgraph::Subgraph()
node62.load_from_file(_editor, "translate_selected.ves")

_editor.add_node(node62, -123.60679672907, -1030.5064081887)

var node63 = ::blueprint::nodes::load::Load()

node63.var_name = "selected"

_editor.add_node(node63, -314.84048143983, -996.14665592275)

var node64 = ::blueprint::nodes::load::Load()

node64.var_name = "second_pos"

_editor.add_node(node64, -313.72186486118, -1041.6699691514)

var node65 = ::blueprint::nodes::load::Load()

node65.var_name = "selected"

_editor.add_node(node65, -1113.362364732, -42.34668745152)

var node66 = ::blueprint::nodes::n_o_t::NOT()

_editor.add_node(node66, -980.0558796448, -42.46711598403)

var node67 = ::blueprint::nodes::a_n_d::AND()

_editor.add_node(node67, -833.29265757391, 15.006583170911)

var node68 = ::blueprint::nodes::store::Store()

node68.var_name = "is_draw_mode"

_editor.add_node(node68, -700.36803141949, 16.71643649922)

var node69 = ::blueprint::nodes::load::Load()

node69.var_name = "is_draw_mode"

_editor.add_node(node69, -200.20257696758, -441.88986939761)

var node70 = ::blueprint::nodes::branch::Branch()

_editor.add_node(node70, -868.42824024942, -370.90040487063)

var node71 = ::blueprint::nodes::load::Load()

node71.var_name = "is_draw_mode"

_editor.add_node(node71, -1036.8187483132, -416.92430395508)

var node72 = ::blueprint::nodes::load::Load()

node72.var_name = "selected"

_editor.add_node(node72, -318.36910271389, -889.52389497462)

var node73 = ::blueprint::nodes::input::Input()

node73.var_name = "cam_mat"
node73.var_type = "mat4"

_editor.add_node(node73, -1199.8673159321, -117.97191923212)

var node74 = ::blueprint::nodes::store::Store()

node74.var_name = "cam_mat"

_editor.add_node(node74, -1064.7494506836, -117.94660644532)

var node75 = ::blueprint::nodes::load::Load()

node75.var_name = "cam_mat"

_editor.add_node(node75, -356.84942072088, 327.34431318803)

var node76 = ::blueprint::nodes::load::Load()

node76.var_name = "cam_mat"

_editor.add_node(node76, -470.43009297957, 139.55126941066)

var node77 = ::blueprint::nodes::load::Load()

node77.var_name = "cam_mat"

_editor.add_node(node77, -475.99474508931, -231.40929003399)

var node78 = ::blueprint::nodes::load::Load()

node78.var_name = "cam_mat"

_editor.add_node(node78, -1191.6782647478, -353.42685082444)

var node79 = ::blueprint::nodes::load::Load()

node79.var_name = "cam_mat"

_editor.add_node(node79, -866.41781196072, -599.76623117883)

var node80 = ::blueprint::nodes::load::Load()

node80.var_name = "cam_mat"

_editor.add_node(node80, -318.12510571932, -938.28886595271)

Blueprint.connect(node73, "var", node74, "var")
Blueprint.connect(node74, "var", node80, "var")
Blueprint.connect(node74, "var", node79, "var")
Blueprint.connect(node74, "var", node78, "var")
Blueprint.connect(node74, "var", node77, "var")
Blueprint.connect(node74, "var", node76, "var")
Blueprint.connect(node74, "var", node75, "var")
Blueprint.connect(node59, "var", node32, "var")
Blueprint.connect(node32, "var", node46, "var")
Blueprint.connect(node32, "var", node41, "var")
Blueprint.connect(node32, "var", node34, "var")
Blueprint.connect(node32, "var", node33, "var")
Blueprint.connect(node33, "var", node1, "geos")
Blueprint.connect(node78, "var", node1, "cam_mat")
Blueprint.connect(node47, "all", node50, "var")
Blueprint.connect(node50, "var", node72, "var")
Blueprint.connect(node41, "var", node40, "geos")
Blueprint.connect(node72, "var", node40, "selected")
Blueprint.connect(node80, "var", node40, "cam_mat")
Blueprint.connect(node50, "var", node63, "var")
Blueprint.connect(node50, "var", node52, "var")
Blueprint.connect(node46, "var", node45, "geos")
Blueprint.connect(node52, "var", node45, "selected")
Blueprint.connect(node75, "var", node45, "cam_mat")
Blueprint.connect(node45, "success", node60, "var")
Blueprint.connect(node60, "var", node65, "var")
Blueprint.connect(node65, "var", node66, "in")
Blueprint.connect(node60, "var", node61, "var")
Blueprint.connect(node61, "var", node53, "cond")
Blueprint.connect(node10, "next", node53, "false")
Blueprint.connect(node50, "var", node51, "var")
Blueprint.connect(node14, "event", node13, "event")
Blueprint.connect(node42, "next", node13, "action")
Blueprint.connect(node11, "v2", node25, "var")
Blueprint.connect(node25, "var", node64, "var")
Blueprint.connect(node63, "var", node62, "selected")
Blueprint.connect(node64, "var", node62, "last_pos")
Blueprint.connect(node80, "var", node62, "cam_mat")
Blueprint.connect(node25, "var", node44, "var")
Blueprint.connect(node25, "var", node28, "var")
Blueprint.connect(node25, "var", node27, "var")
Blueprint.connect(node25, "var", node26, "var")
Blueprint.connect(node8, "pos", node9, "screen")
Blueprint.connect(node77, "var", node9, "cam_mat")
Blueprint.connect(node9, "world", node10, "src")
Blueprint.connect(node27, "var", node10, "dst")
Blueprint.connect(node8, "event", node7, "event")
Blueprint.connect(node53, "next", node7, "action")
Blueprint.connect(node5, "v2", node19, "var")
Blueprint.connect(node19, "var", node43, "var")
Blueprint.connect(node19, "var", node24, "var")
Blueprint.connect(node19, "var", node23, "var")
Blueprint.connect(node19, "var", node22, "var")
Blueprint.connect(node28, "var", node12, "a")
Blueprint.connect(node22, "var", node12, "b")
Blueprint.connect(node12, "v", node29, "var")
Blueprint.connect(node29, "var", node57, "var")
Blueprint.connect(node57, "var", node58, "v")
Blueprint.connect(node58, "v", node55, "a")
Blueprint.connect(node56, "v2", node55, "b")
Blueprint.connect(node55, "out", node67, "a")
Blueprint.connect(node66, "out", node67, "b")
Blueprint.connect(node67, "out", node68, "var")
Blueprint.connect(node68, "var", node71, "var")
Blueprint.connect(node1, "next", node70, "prev")
Blueprint.connect(node71, "var", node70, "cond")
Blueprint.connect(node17, "next", node70, "true")
Blueprint.connect(node70, "next", node48, "prev")
Blueprint.connect(node51, "var", node48, "geos")
Blueprint.connect(node49, "v3", node48, "color")
Blueprint.connect(node79, "var", node48, "cam_mat")
Blueprint.connect(node68, "var", node69, "var")
Blueprint.connect(node69, "var", node54, "cond")
Blueprint.connect(node15, "next", node54, "true")
Blueprint.connect(node54, "next", node42, "prev")
Blueprint.connect(node43, "var", node42, "src")
Blueprint.connect(node44, "var", node42, "dst")
Blueprint.connect(node29, "var", node31, "var")
Blueprint.connect(node24, "var", node16, "pos")
Blueprint.connect(node31, "var", node16, "size")
Blueprint.connect(node34, "var", node15, "list")
Blueprint.connect(node16, "geo", node15, "add")
Blueprint.connect(node29, "var", node30, "var")
Blueprint.connect(node23, "var", node0, "pos")
Blueprint.connect(node30, "var", node0, "size")
Blueprint.connect(node0, "geo", node17, "geos")
Blueprint.connect(node78, "var", node17, "cam_mat")
Blueprint.connect(node19, "var", node21, "var")
Blueprint.connect(node19, "var", node20, "var")
Blueprint.connect(node3, "pos", node6, "screen")
Blueprint.connect(node76, "var", node6, "cam_mat")
Blueprint.connect(node6, "world", node4, "src")
Blueprint.connect(node20, "var", node4, "dst")
Blueprint.connect(node4, "next", node18, "prev")
Blueprint.connect(node21, "var", node18, "src")
Blueprint.connect(node26, "var", node18, "dst")
Blueprint.connect(node45, "next", node2, "prev")
Blueprint.connect(node3, "event", node2, "event")
Blueprint.connect(node18, "next", node2, "action")
