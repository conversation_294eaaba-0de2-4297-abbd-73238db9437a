<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Member List</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::DxContextManagerCallback Member List</h1>This is the complete list of members for <a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a>, including all inherited members.<p><table>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#4d8cafe2879665280b8e9e8daa18e95f">acquireContext</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#39f71451d6802462f724554a6d06004c">getContext</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#b3ca0c862df8de0e4022fcbfee5351a3">getDevice</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#df97ac38567b401fd34168e32cdc88cd">releaseContext</a>()=0</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#a2ebcef21c55d3a59e01d344e6ee917a">synchronizeResources</a>() const =0</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [pure virtual]</code></td></tr>
  <tr class="memlist"><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html#8bdc176529c9e17936002067d13d4d47">~DxContextManagerCallback</a>()</td><td><a class="el" href="classnv_1_1cloth_1_1_dx_context_manager_callback.html">nv::cloth::DxContextManagerCallback</a></td><td><code> [inline, virtual]</code></td></tr>
</table></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
