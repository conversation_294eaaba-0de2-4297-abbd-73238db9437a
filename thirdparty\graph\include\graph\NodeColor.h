#pragma once

#include <cstdint>

namespace graph {

enum class NodeColor : uint32_t
{
    White = 0xFFFFFFFF,
    Black = 0xFF000000,
    Red   = 0xFFFF0000,
    Green = 0xFF00FF00,
    Blue  = 0xFF0000FF,
    Yellow = 0xFFFFFF00,
    Cyan   = 0xFF00FFFF,
    Magenta = 0xFFFF00FF,
    Orange = 0xFFFFA500,
    Purple = 0xFF800080,
    Gray   = 0xFF808080,
    LightGray = 0xFFD3D3D3,
    DarkGray  = 0xFFA9A9A9
};

class NodeColorHelper
{
public:
    static uint32_t ToUInt32(NodeColor color) {
        return static_cast<uint32_t>(color);
    }
    
    static NodeColor FromUInt32(uint32_t color) {
        return static_cast<NodeColor>(color);
    }
    
    static NodeColor GetRandomColor();
    static NodeColor BlendColors(NodeColor a, NodeColor b, float ratio);
};

} // namespace graph
