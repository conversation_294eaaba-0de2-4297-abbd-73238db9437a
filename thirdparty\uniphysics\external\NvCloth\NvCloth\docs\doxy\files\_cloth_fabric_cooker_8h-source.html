<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: ClothFabricCooker.h Source File</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="annotated.html"><span>Classes</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="files.html"><span>File&nbsp;List</span></a></li>
    </ul>
  </div>
<h1>ClothFabricCooker.h</h1><a href="_cloth_fabric_cooker_8h.html">Go to the documentation of this file.</a><div class="fragment"><pre class="fragment"><a name="l00001"></a>00001 <span class="comment">// This code contains NVIDIA Confidential Information and is disclosed to you</span>
<a name="l00002"></a>00002 <span class="comment">// under a form of NVIDIA software license agreement provided separately to you.</span>
<a name="l00003"></a>00003 <span class="comment">//</span>
<a name="l00004"></a>00004 <span class="comment">// Notice</span>
<a name="l00005"></a>00005 <span class="comment">// NVIDIA Corporation and its licensors retain all intellectual property and</span>
<a name="l00006"></a>00006 <span class="comment">// proprietary rights in and to this software and related documentation and</span>
<a name="l00007"></a>00007 <span class="comment">// any modifications thereto. Any use, reproduction, disclosure, or</span>
<a name="l00008"></a>00008 <span class="comment">// distribution of this software and related documentation without an express</span>
<a name="l00009"></a>00009 <span class="comment">// license agreement from NVIDIA Corporation is strictly prohibited.</span>
<a name="l00010"></a>00010 <span class="comment">//</span>
<a name="l00011"></a>00011 <span class="comment">// ALL NVIDIA DESIGN SPECIFICATIONS, CODE ARE PROVIDED "AS IS.". NVIDIA MAKES</span>
<a name="l00012"></a>00012 <span class="comment">// NO WARRANTIES, EXPRESSED, IMPLIED, STATUTORY, OR OTHERWISE WITH RESPECT TO</span>
<a name="l00013"></a>00013 <span class="comment">// THE MATERIALS, AND EXPRESSLY DISCLAIMS ALL IMPLIED WARRANTIES OF NONINFRINGEMENT,</span>
<a name="l00014"></a>00014 <span class="comment">// MERCHANTABILITY, AND FITNESS FOR A PARTICULAR PURPOSE.</span>
<a name="l00015"></a>00015 <span class="comment">//</span>
<a name="l00016"></a>00016 <span class="comment">// Information and code furnished is believed to be accurate and reliable.</span>
<a name="l00017"></a>00017 <span class="comment">// However, NVIDIA Corporation assumes no responsibility for the consequences of use of such</span>
<a name="l00018"></a>00018 <span class="comment">// information or for any infringement of patents or other rights of third parties that may</span>
<a name="l00019"></a>00019 <span class="comment">// result from its use. No license is granted by implication or otherwise under any patent</span>
<a name="l00020"></a>00020 <span class="comment">// or patent rights of NVIDIA Corporation. Details are subject to change without notice.</span>
<a name="l00021"></a>00021 <span class="comment">// This code supersedes and replaces all information previously supplied.</span>
<a name="l00022"></a>00022 <span class="comment">// NVIDIA Corporation products are not authorized for use as critical</span>
<a name="l00023"></a>00023 <span class="comment">// components in life support devices or systems without express written approval of</span>
<a name="l00024"></a>00024 <span class="comment">// NVIDIA Corporation.</span>
<a name="l00025"></a>00025 <span class="comment">//</span>
<a name="l00026"></a>00026 <span class="comment">// Copyright (c) 2008-2017 NVIDIA Corporation. All rights reserved.</span>
<a name="l00027"></a>00027 <span class="comment">// Copyright (c) 2004-2008 AGEIA Technologies, Inc. All rights reserved.</span>
<a name="l00028"></a>00028 <span class="comment">// Copyright (c) 2001-2004 NovodeX AG. All rights reserved.  </span>
<a name="l00029"></a>00029 
<a name="l00030"></a>00030 
<a name="l00031"></a>00031 <span class="preprocessor">#ifndef NV_CLOTH_EXTENSIONS_CLOTH_FABRIC_COOKER_H</span>
<a name="l00032"></a>00032 <span class="preprocessor"></span><span class="preprocessor">#define NV_CLOTH_EXTENSIONS_CLOTH_FABRIC_COOKER_H</span>
<a name="l00033"></a>00033 <span class="preprocessor"></span>
<a name="l00038"></a>00038 <span class="preprocessor">#include "<a class="code" href="_cloth_mesh_desc_8h.html">ClothMeshDesc.h</a>"</span>
<a name="l00039"></a>00039 <span class="preprocessor">#include "<a class="code" href="_fabric_8h.html">NvCloth/Fabric.h</a>"</span>
<a name="l00040"></a>00040 <span class="preprocessor">#include "<a class="code" href="_factory_8h.html">NvCloth/Factory.h</a>"</span>
<a name="l00041"></a>00041 
<a name="l00042"></a>00042 <span class="keyword">namespace </span>nv
<a name="l00043"></a>00043 {
<a name="l00044"></a>00044 <span class="keyword">namespace </span>cloth
<a name="l00045"></a>00045 {
<a name="l00046"></a>00046 
<a name="l00047"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html">00047</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html">CookedData</a>
<a name="l00048"></a>00048 {
<a name="l00049"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">00049</a>     uint32_t <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#00f9afd3833301fb02d20c779a6ec132">mNumParticles</a>;
<a name="l00050"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">00050</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#feabe61136d9cdcf6625494bf8cf2a89">mPhaseIndices</a>;
<a name="l00051"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">00051</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const int32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#17ceb5f81c8fd9c4f5af1e8c38b12b35">mPhaseTypes</a>;
<a name="l00052"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">00052</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#c29c4d9fef1364ee124e81b05149925f">mSets</a>;
<a name="l00053"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">00053</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#accb8f8ffafaaf9e3a19753ce2167bc1">mRestvalues</a>;
<a name="l00054"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">00054</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#49a9c6e81b7c95174b30d3fd978ab409">mStiffnessValues</a>;
<a name="l00055"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">00055</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#b8a3ec4f4c531de0e4702cedf8a74261">mIndices</a>;
<a name="l00056"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">00056</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#a007ccb67a4839797735e5eb1194dc20">mAnchors</a>;
<a name="l00057"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">00057</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const float&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#ca97240e8d092d9cac41fe557eb375bd">mTetherLengths</a>;
<a name="l00058"></a><a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">00058</a>     <a class="code" href="structnv_1_1cloth_1_1_range.html">Range&lt;const uint32_t&gt;</a> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html#03c99508d108059b41e9dfd6fbda6412">mTriangles</a>;
<a name="l00059"></a>00059 };
<a name="l00060"></a>00060 
<a name="l00065"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html">00065</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html" title="Describe type of phase in cloth fabric.">ClothFabricPhaseType</a>
<a name="l00066"></a>00066 {
<a name="l00067"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">00067</a>     <span class="keyword">enum</span> <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">Enum</a>
<a name="l00068"></a>00068     {
<a name="l00069"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45">00069</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45" title="invalid type">eINVALID</a>,     
<a name="l00070"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c895d408850eff3c958150f13eea8728df">00070</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c895d408850eff3c958150f13eea8728df" title="resists stretching or compression, usually along the gravity">eVERTICAL</a>,    
<a name="l00071"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c86b9950273c603473058bf8374ae22412">00071</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c86b9950273c603473058bf8374ae22412" title="resists stretching or compression, perpendicular to the gravity">eHORIZONTAL</a>,  
<a name="l00072"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c85a210009d7fffa6fb7fbf246e40b1eb9">00072</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c85a210009d7fffa6fb7fbf246e40b1eb9" title="resists out-of-plane bending in angle-based formulation">eBENDING</a>,     
<a name="l00073"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c89e3f928ec6acb0a8ab211149afc9e24c">00073</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c89e3f928ec6acb0a8ab211149afc9e24c" title="resists in-plane shearing along (typically) diagonal edges,">eSHEARING</a>,    
<a name="l00074"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c83033500239eabb666a723f55c257ad2f">00074</a>         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c83033500239eabb666a723f55c257ad2f">eCOUNT</a>        <span class="comment">// internal use only</span>
<a name="l00075"></a>00075     };
<a name="l00076"></a>00076 };
<a name="l00077"></a>00077 
<a name="l00082"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html">00082</a> <span class="keyword">struct </span><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html" title="References a set of constraints that can be solved in parallel.">ClothFabricPhase</a>
<a name="l00083"></a>00083 {
<a name="l00084"></a>00084     <a class="code" href="group__extensions.html#g09aa011d5780d368d58864791f2ff512">ClothFabricPhase</a>(<a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a> type = 
<a name="l00085"></a>00085         <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45" title="invalid type">ClothFabricPhaseType::eINVALID</a>, physx::PxU32 index = 0);
<a name="l00086"></a>00086 
<a name="l00090"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#f19b795dfc88f16a90a75621be1fbd0a">00090</a>     <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a> <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#f19b795dfc88f16a90a75621be1fbd0a" title="Type of constraints to solve.">phaseType</a>;
<a name="l00091"></a>00091 
<a name="l00095"></a><a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#a9f1038529f5a7545c6f19c95be61015">00095</a>     physx::PxU32 <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html#a9f1038529f5a7545c6f19c95be61015" title="Index of the set that contains the particle indices.">setIndex</a>;
<a name="l00096"></a>00096 };
<a name="l00097"></a>00097 
<a name="l00098"></a><a class="code" href="group__extensions.html#g09aa011d5780d368d58864791f2ff512">00098</a> PX_INLINE <a class="code" href="group__extensions.html#g09aa011d5780d368d58864791f2ff512">ClothFabricPhase::ClothFabricPhase</a>(
<a name="l00099"></a>00099     <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c8">ClothFabricPhaseType::Enum</a> type, physx::PxU32 index)
<a name="l00100"></a>00100     : phaseType(type)
<a name="l00101"></a>00101     , setIndex(index)
<a name="l00102"></a>00102 {}
<a name="l00103"></a>00103 
<a name="l00108"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html">00108</a> <span class="keyword">class </span><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html" title="References all the data required to create a fabric.">ClothFabricDesc</a>
<a name="l00109"></a>00109 {
<a name="l00110"></a>00110 <span class="keyword">public</span>:
<a name="l00112"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1">00112</a>     physx::PxU32 <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1" title="The number of particles needed when creating a PxCloth instance from the fabric.">nbParticles</a>;
<a name="l00113"></a>00113 
<a name="l00115"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78">00115</a>     physx::PxU32 <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78" title="The number of solver phases.">nbPhases</a>;
<a name="l00117"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f">00117</a>     <span class="keyword">const</span> <a class="code" href="structnv_1_1cloth_1_1_cloth_fabric_phase.html" title="References a set of constraints that can be solved in parallel.">ClothFabricPhase</a>* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f" title="Array defining which constraints to solve each phase.">phases</a>;
<a name="l00118"></a>00118 
<a name="l00120"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc">00120</a>     physx::PxU32 <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc" title="The number of sets in the fabric.">nbSets</a>;
<a name="l00122"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452">00122</a>     <span class="keyword">const</span> physx::PxU32* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452" title="Array with an index per set which points one entry beyond the last constraint of...">sets</a>;
<a name="l00123"></a>00123 
<a name="l00125"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba">00125</a>     <span class="keyword">const</span> physx::PxU32* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba" title="Array of particle indices which specifies the pair of constrained vertices.">indices</a>;
<a name="l00127"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd">00127</a>     <span class="keyword">const</span> physx::PxReal* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd" title="Array of rest values for each constraint.">restvalues</a>;
<a name="l00128"></a>00128 
<a name="l00130"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad">00130</a>     physx::PxU32 <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad" title="Size of tetherAnchors and tetherLengths arrays, needs to be multiple of nbParticles...">nbTethers</a>;
<a name="l00132"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83">00132</a>     <span class="keyword">const</span> physx::PxU32* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83" title="Array of particle indices specifying the tether anchors.">tetherAnchors</a>;
<a name="l00134"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a">00134</a>     <span class="keyword">const</span> physx::PxReal* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a" title="Array of rest distance between tethered particle pairs.">tetherLengths</a>;
<a name="l00135"></a>00135 
<a name="l00136"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">00136</a>     physx::PxU32 <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nbTriangles</a>;
<a name="l00137"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">00137</a>     <span class="keyword">const</span> physx::PxU32* <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">triangles</a>;
<a name="l00138"></a>00138 
<a name="l00142"></a>00142     PX_INLINE <a class="code" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987" title="constructor sets to default.">ClothFabricDesc</a>();
<a name="l00143"></a>00143 
<a name="l00147"></a>00147     PX_INLINE <span class="keywordtype">void</span> <a class="code" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98" title="(re)sets the structure to the default.">setToDefault</a>();
<a name="l00148"></a>00148 
<a name="l00153"></a>00153     PX_INLINE <span class="keywordtype">bool</span> <a class="code" href="group__extensions.html#gb65c431a270115915e78a73c37489dee" title="Returns true if the descriptor is valid.">isValid</a>() <span class="keyword">const</span>;
<a name="l00154"></a>00154 };
<a name="l00155"></a>00155 
<a name="l00156"></a><a class="code" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987">00156</a> PX_INLINE <a class="code" href="group__extensions.html#g25a9034d02b0edfaee83e58213288987" title="constructor sets to default.">ClothFabricDesc::ClothFabricDesc</a>()
<a name="l00157"></a>00157 {
<a name="l00158"></a>00158     <a class="code" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98" title="(re)sets the structure to the default.">setToDefault</a>();
<a name="l00159"></a>00159 }
<a name="l00160"></a>00160 
<a name="l00161"></a><a class="code" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98">00161</a> PX_INLINE <span class="keywordtype">void</span> <a class="code" href="group__extensions.html#gc0dd7bb3155e63161744b3fc07132a98" title="(re)sets the structure to the default.">ClothFabricDesc::setToDefault</a>()
<a name="l00162"></a>00162 {
<a name="l00163"></a>00163     memset(<span class="keyword">this</span>, 0, <span class="keyword">sizeof</span>(<a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html" title="References all the data required to create a fabric.">ClothFabricDesc</a>));
<a name="l00164"></a>00164 }
<a name="l00165"></a>00165 
<a name="l00166"></a><a class="code" href="group__extensions.html#gb65c431a270115915e78a73c37489dee">00166</a> PX_INLINE <span class="keywordtype">bool</span> <a class="code" href="group__extensions.html#gb65c431a270115915e78a73c37489dee" title="Returns true if the descriptor is valid.">ClothFabricDesc::isValid</a>()<span class="keyword"> const</span>
<a name="l00167"></a>00167 <span class="keyword"></span>{
<a name="l00168"></a>00168     <span class="keywordflow">return</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#27b5e237d6317729292527baded536e1" title="The number of particles needed when creating a PxCloth instance from the fabric.">nbParticles</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#2b0bdbc53cd541c268b1420443c6de78" title="The number of solver phases.">nbPhases</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#add526e57831da43c7a41de83349a38f" title="Array defining which constraints to solve each phase.">phases</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#5c77a4ffedc077675afb330b4c6dc8cd" title="Array of rest values for each constraint.">restvalues</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#de386e51c397d5ab229e73090f9a81fc" title="The number of sets in the fabric.">nbSets</a> 
<a name="l00169"></a>00169         &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#58b1640666ca9ed22a3ee84e7e7d8452" title="Array with an index per set which points one entry beyond the last constraint of...">sets</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#be0c3211f0dff98d6bed2a5ba859cdba" title="Array of particle indices which specifies the pair of constrained vertices.">indices</a> &amp;&amp; (!<a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#0f41befe55fe10d711513cf4aba0abad" title="Size of tetherAnchors and tetherLengths arrays, needs to be multiple of nbParticles...">nbTethers</a> || (<a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#368ed028593849935d0d32a47ae21a83" title="Array of particle indices specifying the tether anchors.">tetherAnchors</a> &amp;&amp; <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#19005ea1d05eadafab1ed0f52cc14a4a" title="Array of rest distance between tethered particle pairs.">tetherLengths</a>))
<a name="l00170"></a>00170         &amp;&amp; (!<a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b8e7ddf2dc4b66a96151c313c1c68e81">nbTriangles</a> || <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html#b6e6ab337d8803cc74328314432453f4">triangles</a>);
<a name="l00171"></a>00171 }
<a name="l00172"></a>00172 
<a name="l00174"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html">00174</a> <span class="keyword">class </span><a class="code" href="_callbacks_8h.html#bd597bda23283ca6fe84282f6e2671dc">NV_CLOTH_IMPORT</a> <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html" title="Use NvClothCreateFabricCooker() to create an implemented instance.">ClothFabricCooker</a> : <span class="keyword">public</span> UserAllocated
<a name="l00175"></a>00175 {
<a name="l00176"></a>00176 <span class="keyword">public</span>:
<a name="l00177"></a><a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#4fdce19d20d9db5a1e3db8b6595061de">00177</a>     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html#4fdce19d20d9db5a1e3db8b6595061de">~ClothFabricCooker</a>(){}
<a name="l00178"></a>00178 
<a name="l00190"></a>00190     <span class="keyword">virtual</span> <span class="keywordtype">bool</span> cook(<span class="keyword">const</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a>&amp; desc, physx::PxVec3 gravity, <span class="keywordtype">bool</span> useGeodesicTether = <span class="keyword">true</span>) = 0;
<a name="l00191"></a>00191 
<a name="l00193"></a>00193     <span class="keyword">virtual</span> <a class="code" href="structnv_1_1cloth_1_1_cooked_data.html">CookedData</a> getCookedData() <span class="keyword">const</span> = 0;
<a name="l00194"></a>00194 
<a name="l00196"></a>00196     <span class="keyword">virtual</span> <a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_desc.html" title="References all the data required to create a fabric.">ClothFabricDesc</a> getDescriptor() <span class="keyword">const</span> = 0;
<a name="l00198"></a>00198     <span class="keyword">virtual</span> <span class="keywordtype">void</span> save(physx::PxOutputStream&amp; stream, <span class="keywordtype">bool</span> platformMismatch) <span class="keyword">const</span> = 0;
<a name="l00199"></a>00199 };
<a name="l00200"></a>00200 
<a name="l00203"></a>00203 } <span class="comment">// namespace cloth</span>
<a name="l00204"></a>00204 } <span class="comment">// namespace nv</span>
<a name="l00205"></a>00205 
<a name="l00206"></a>00206 
<a name="l00207"></a>00207 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(<a class="code" href="classnv_1_1cloth_1_1_cloth_fabric_cooker.html" title="Use NvClothCreateFabricCooker() to create an implemented instance.">nv::cloth::ClothFabricCooker</a>*) NvClothCreateFabricCooker();
<a name="l00208"></a>00208 
<a name="l00220"></a>00220 <a class="code" href="_callbacks_8h.html#ca1463b4545d714cb99ba3e96d4245ff">NV_CLOTH_API</a>(nv::cloth::Fabric*) NvClothCookFabricFromMesh(nv::cloth::Factory* factory,
<a name="l00221"></a>00221     const nv::cloth::ClothMeshDesc&amp; desc, const physx::PxVec3&amp; gravity,
<a name="l00222"></a>00222     nv::cloth::Vector&lt;int32_t&gt;::Type* phaseTypes = nullptr, <span class="keywordtype">bool</span> useGeodesicTether = true);
<a name="l00223"></a>00223 
<a name="l00224"></a>00224 <span class="preprocessor">#endif // NV_CLOTH_EXTENSIONS_CLOTH_FABRIC_COOKER_H</span>
</pre></div></div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
