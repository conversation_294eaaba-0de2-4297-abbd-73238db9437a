<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::Solver Class Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::Solver Class Reference</h1><!-- doxytag: class="nv::cloth::Solver" -->base class for solvers  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_solver_8h-source.html">Solver.h</a>&gt;</code>
<p>

<p>
<a href="classnv_1_1cloth_1_1_solver-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#63e732712c5a43c44e6018cca6c1fb82">addCloth</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *cloth)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Adds cloth object.  <a href="#63e732712c5a43c44e6018cca6c1fb82"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#ea0f778230b2a0f211bdb5d36d3b54f3">addCloths</a> (<a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> * &gt; cloths)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Adds an array of cloth objects.  <a href="#ea0f778230b2a0f211bdb5d36d3b54f3"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#d2efbe447bf07138c615973c349ab839">beginSimulation</a> (float dt)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Begins a simulation frame.  <a href="#d2efbe447bf07138c615973c349ab839"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#19d777a3882814910f8a024d92072d48">endSimulation</a> ()=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Finishes up the simulation.  <a href="#19d777a3882814910f8a024d92072d48"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *const *&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#a3e121ffbccc07180e08a2387eb4f6ac">getClothList</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the pointer to the first cloth added to the solver.  <a href="#a3e121ffbccc07180e08a2387eb4f6ac"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#6a72529d713f46dbd17a5b541aaec6df">getInterCollisionDistance</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual uint32_t&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#124dc836903890185934c6eaedec2079">getInterCollisionNbIterations</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual float&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#09e4be9b50229213a837d00a3f2f6a3f">getInterCollisionStiffness</a> () const =0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#de377e651711ebbb9e70f928cbb682e2">getNumCloths</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the numer of cloths added to the solver.  <a href="#de377e651711ebbb9e70f928cbb682e2"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual int&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#54e393ba3b9fd5305385e2f57d3ca165">getSimulationChunkCount</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns the number of chunks that need to be simulated this frame.  <a href="#54e393ba3b9fd5305385e2f57d3ca165"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual bool&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#12a60f43b537d78499e30508bd9a6d3c">hasError</a> () const =0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Returns true if an unrecoverable error has occurred.  <a href="#12a60f43b537d78499e30508bd9a6d3c"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#1ed765a15ab2dabbb5186d14bc5f70b1">removeCloth</a> (<a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *cloth)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Removes cloth object.  <a href="#1ed765a15ab2dabbb5186d14bc5f70b1"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#5043adf6727bf66b966de6393e7d67d9">setInterCollisionDistance</a> (float distance)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#31b3d4d36f2025f10cb04a32e28fada4">setInterCollisionFilter</a> (InterCollisionFilter filter)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#3046ea1153c1f9decfc161155cc9810b">setInterCollisionNbIterations</a> (uint32_t nbIterations)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#4f15accbaeff04edbebd31bf7dd9be3e">setInterCollisionStiffness</a> (float stiffness)=0</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual void&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#62d209d861c8f5aa0523536d851de093">simulateChunk</a> (int idx)=0</td></tr>

<tr><td class="mdescLeft">&nbsp;</td><td class="mdescRight">Do the computationally heavy part of the simulation.  <a href="#62d209d861c8f5aa0523536d851de093"></a><br></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top">virtual&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#4830e23f5fbaa9dfa7c8c0ce32fa85bd">~Solver</a> ()</td></tr>

<tr><td colspan="2"><br><h2>Protected Member Functions</h2></td></tr>
<tr><td class="memItemLeft" nowrap align="right" valign="top"><a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> &amp;&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#0bc438b310a4989a96c426c83a1e0beb">operator=</a> (const <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#21b6b117db42d8a3206cee521e6af4b0">Solver</a> (const <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> &amp;)</td></tr>

<tr><td class="memItemLeft" nowrap align="right" valign="top">&nbsp;</td><td class="memItemRight" valign="bottom"><a class="el" href="classnv_1_1cloth_1_1_solver.html#8955bbdbea66b33486f0588ab2f80c46">Solver</a> ()</td></tr>

</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
base class for solvers <hr><h2>Constructor &amp; Destructor Documentation</h2>
<a class="anchor" name="8955bbdbea66b33486f0588ab2f80c46"></a><!-- doxytag: member="nv::cloth::Solver::Solver" ref="8955bbdbea66b33486f0588ab2f80c46" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Solver::Solver           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="21b6b117db42d8a3206cee521e6af4b0"></a><!-- doxytag: member="nv::cloth::Solver::Solver" ref="21b6b117db42d8a3206cee521e6af4b0" args="(const Solver &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">nv::cloth::Solver::Solver           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="4830e23f5fbaa9dfa7c8c0ce32fa85bd"></a><!-- doxytag: member="nv::cloth::Solver::~Solver" ref="4830e23f5fbaa9dfa7c8c0ce32fa85bd" args="()" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual nv::cloth::Solver::~Solver           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [inline, virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<hr><h2>Member Function Documentation</h2>
<a class="anchor" name="63e732712c5a43c44e6018cca6c1fb82"></a><!-- doxytag: member="nv::cloth::Solver::addCloth" ref="63e732712c5a43c44e6018cca6c1fb82" args="(Cloth *cloth)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::addCloth           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *&nbsp;</td>
          <td class="paramname"> <em>cloth</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Adds cloth object. 
<p>

</div>
</div><p>
<a class="anchor" name="ea0f778230b2a0f211bdb5d36d3b54f3"></a><!-- doxytag: member="nv::cloth::Solver::addCloths" ref="ea0f778230b2a0f211bdb5d36d3b54f3" args="(Range&lt; Cloth * &gt; cloths)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::addCloths           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="structnv_1_1cloth_1_1_range.html">Range</a>&lt; <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> * &gt;&nbsp;</td>
          <td class="paramname"> <em>cloths</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Adds an array of cloth objects. 
<p>

</div>
</div><p>
<a class="anchor" name="d2efbe447bf07138c615973c349ab839"></a><!-- doxytag: member="nv::cloth::Solver::beginSimulation" ref="d2efbe447bf07138c615973c349ab839" args="(float dt)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::Solver::beginSimulation           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>dt</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Begins a simulation frame. 
<p>
Returns false if there is nothing to simulate. Use <a class="el" href="classnv_1_1cloth_1_1_solver.html#62d209d861c8f5aa0523536d851de093" title="Do the computationally heavy part of the simulation.">simulateChunk()</a> after calling this function to do the computation. <dl compact><dt><b>Parameters:</b></dt><dd>
  <table border="0" cellspacing="2" cellpadding="0">
    <tr><td valign="top"></td><td valign="top"><em>dt</em>&nbsp;</td><td>The delta time for this frame. </td></tr>
  </table>
</dl>

</div>
</div><p>
<a class="anchor" name="19d777a3882814910f8a024d92072d48"></a><!-- doxytag: member="nv::cloth::Solver::endSimulation" ref="19d777a3882814910f8a024d92072d48" args="()=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::endSimulation           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Finishes up the simulation. 
<p>
This function can be expensive if inter-collision is enabled. 
</div>
</div><p>
<a class="anchor" name="a3e121ffbccc07180e08a2387eb4f6ac"></a><!-- doxytag: member="nv::cloth::Solver::getClothList" ref="a3e121ffbccc07180e08a2387eb4f6ac" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual <a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a>* const* nv::cloth::Solver::getClothList           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the pointer to the first cloth added to the solver. 
<p>

</div>
</div><p>
<a class="anchor" name="6a72529d713f46dbd17a5b541aaec6df"></a><!-- doxytag: member="nv::cloth::Solver::getInterCollisionDistance" ref="6a72529d713f46dbd17a5b541aaec6df" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Solver::getInterCollisionDistance           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="124dc836903890185934c6eaedec2079"></a><!-- doxytag: member="nv::cloth::Solver::getInterCollisionNbIterations" ref="124dc836903890185934c6eaedec2079" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual uint32_t nv::cloth::Solver::getInterCollisionNbIterations           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="09e4be9b50229213a837d00a3f2f6a3f"></a><!-- doxytag: member="nv::cloth::Solver::getInterCollisionStiffness" ref="09e4be9b50229213a837d00a3f2f6a3f" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual float nv::cloth::Solver::getInterCollisionStiffness           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="de377e651711ebbb9e70f928cbb682e2"></a><!-- doxytag: member="nv::cloth::Solver::getNumCloths" ref="de377e651711ebbb9e70f928cbb682e2" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual int nv::cloth::Solver::getNumCloths           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the numer of cloths added to the solver. 
<p>

</div>
</div><p>
<a class="anchor" name="54e393ba3b9fd5305385e2f57d3ca165"></a><!-- doxytag: member="nv::cloth::Solver::getSimulationChunkCount" ref="54e393ba3b9fd5305385e2f57d3ca165" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual int nv::cloth::Solver::getSimulationChunkCount           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns the number of chunks that need to be simulated this frame. 
<p>

</div>
</div><p>
<a class="anchor" name="12a60f43b537d78499e30508bd9a6d3c"></a><!-- doxytag: member="nv::cloth::Solver::hasError" ref="12a60f43b537d78499e30508bd9a6d3c" args="() const =0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual bool nv::cloth::Solver::hasError           </td>
          <td>(</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td> const<code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Returns true if an unrecoverable error has occurred. 
<p>

</div>
</div><p>
<a class="anchor" name="0bc438b310a4989a96c426c83a1e0beb"></a><!-- doxytag: member="nv::cloth::Solver::operator=" ref="0bc438b310a4989a96c426c83a1e0beb" args="(const Solver &amp;)" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname"><a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a>&amp; nv::cloth::Solver::operator=           </td>
          <td>(</td>
          <td class="paramtype">const <a class="el" href="classnv_1_1cloth_1_1_solver.html">Solver</a> &amp;&nbsp;</td>
          <td class="paramname">          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [protected]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="1ed765a15ab2dabbb5186d14bc5f70b1"></a><!-- doxytag: member="nv::cloth::Solver::removeCloth" ref="1ed765a15ab2dabbb5186d14bc5f70b1" args="(Cloth *cloth)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::removeCloth           </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="classnv_1_1cloth_1_1_cloth.html">Cloth</a> *&nbsp;</td>
          <td class="paramname"> <em>cloth</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Removes cloth object. 
<p>

</div>
</div><p>
<a class="anchor" name="5043adf6727bf66b966de6393e7d67d9"></a><!-- doxytag: member="nv::cloth::Solver::setInterCollisionDistance" ref="5043adf6727bf66b966de6393e7d67d9" args="(float distance)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::setInterCollisionDistance           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>distance</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="31b3d4d36f2025f10cb04a32e28fada4"></a><!-- doxytag: member="nv::cloth::Solver::setInterCollisionFilter" ref="31b3d4d36f2025f10cb04a32e28fada4" args="(InterCollisionFilter filter)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::setInterCollisionFilter           </td>
          <td>(</td>
          <td class="paramtype">InterCollisionFilter&nbsp;</td>
          <td class="paramname"> <em>filter</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="3046ea1153c1f9decfc161155cc9810b"></a><!-- doxytag: member="nv::cloth::Solver::setInterCollisionNbIterations" ref="3046ea1153c1f9decfc161155cc9810b" args="(uint32_t nbIterations)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::setInterCollisionNbIterations           </td>
          <td>(</td>
          <td class="paramtype">uint32_t&nbsp;</td>
          <td class="paramname"> <em>nbIterations</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="4f15accbaeff04edbebd31bf7dd9be3e"></a><!-- doxytag: member="nv::cloth::Solver::setInterCollisionStiffness" ref="4f15accbaeff04edbebd31bf7dd9be3e" args="(float stiffness)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::setInterCollisionStiffness           </td>
          <td>(</td>
          <td class="paramtype">float&nbsp;</td>
          <td class="paramname"> <em>stiffness</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>

</div>
</div><p>
<a class="anchor" name="62d209d861c8f5aa0523536d851de093"></a><!-- doxytag: member="nv::cloth::Solver::simulateChunk" ref="62d209d861c8f5aa0523536d851de093" args="(int idx)=0" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">virtual void nv::cloth::Solver::simulateChunk           </td>
          <td>(</td>
          <td class="paramtype">int&nbsp;</td>
          <td class="paramname"> <em>idx</em>          </td>
          <td>&nbsp;)&nbsp;</td>
          <td><code> [pure virtual]</code></td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
Do the computationally heavy part of the simulation. 
<p>
Call this function <a class="el" href="classnv_1_1cloth_1_1_solver.html#54e393ba3b9fd5305385e2f57d3ca165" title="Returns the number of chunks that need to be simulated this frame.">getSimulationChunkCount()</a> times to do the entire simulation. This function can be called from multiple threads in parallel. All Chunks need to be simulated before ending the frame. 
</div>
</div><p>
<hr>The documentation for this class was generated from the following file:<ul>
<li><a class="el" href="_solver_8h-source.html">Solver.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
