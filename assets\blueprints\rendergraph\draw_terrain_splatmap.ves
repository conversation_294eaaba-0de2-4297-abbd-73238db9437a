var node0 = ::rendergraph::nodes::draw::Draw()

node0.set_prim_type("patches")
node0.render_state = { "stencil_test" : false, "rasterization" : "fill", "stencil_func" : "always", "stencil_mask" : 255, "cull" : "back", "blend" : false, "stencil_ref" : 0, "depth_test" : true, "depth_func" : "less", "clip_plane" : true }
node0.skip = false

_editor.add_node(node0, 558.8075282446, -339.48126638417)

var node1 = ::rendergraph::nodes::shader::Shader()

node1.vs = "
#version 330 core
layout (location = 0) in vec3 position;
layout (location = 1) in vec2 texcoord;

out vec3 TCS_IN_FragPos;
out vec2 TCS_IN_TexCoord;

void main()
{
    TCS_IN_FragPos = position.xzy;
    TCS_IN_TexCoord = texcoord;
}
"
node1.tcs = "
#version 400 core

layout(vertices = 3) out;

in vec3 TCS_IN_FragPos[];
in vec2 TCS_IN_TexCoord[];

out vec3 TES_IN_FragPos[];
out vec2 TES_IN_TexCoord[];

uniform UBO_TCS
{
    mat4 projection;
    mat4 view;
    mat4 model;   

    vec3 cam_pos;
    int lod_level;  
} tcs;

void main()
{
    TES_IN_FragPos[gl_InvocationID] = TCS_IN_FragPos[gl_InvocationID];
    TES_IN_TexCoord[gl_InvocationID] = TCS_IN_TexCoord[gl_InvocationID];

    if (gl_InvocationID == 0)
    {
        vec4 curr_p = tcs.projection * tcs.view * tcs.model * vec4(TCS_IN_FragPos[0], 1.0);
        float dist = length(tcs.cam_pos - curr_p.xyz);

        int max_level = tcs.lod_level;
        int curr_level = max_level;
        for (int i = 0; i < max_level; ++i) {
            if (dist < 0.01 * pow(2, i)) {
                curr_level = i;
                break;
            }
        }
        float level = clamp(pow(2.0, (max_level - curr_level)), 1.0, float(gl_MaxTessGenLevel));
        for (int i = 0; i < 3; ++i) {
            gl_TessLevelOuter[i] = level;
        }
        gl_TessLevelInner[0] = level / 2;
    }
}
"
node1.tes = "
#version 400 core

layout(triangles, equal_spacing, ccw) in;

in vec3 TES_IN_FragPos[];
in vec2 TES_IN_TexCoord[];

out VS_OUT {
    float height;
    vec3 fragpos;
    vec2 texcoord;
} vs_out;

uniform UBO_TES
{
    mat4 projection;
    mat4 view;
    mat4 model;   

    float height_scale;
    float height_bias;

    vec4 clip_plane;    
} tes;

uniform sampler2D tes_heightmap;

vec2 interpolate_2d(vec2 v0, vec2 v1, vec2 v2)
{
    return vec2(gl_TessCoord.x) * v0 + vec2(gl_TessCoord.y) * v1 + vec2(gl_TessCoord.z) * v2;
}

vec3 interpolate_3d(vec3 v0, vec3 v1, vec3 v2)
{
    return vec3(gl_TessCoord.x) * v0 + vec3(gl_TessCoord.y) * v1 + vec3(gl_TessCoord.z) * v2;
}

void main()
{
    vec4 pos = vec4(interpolate_3d(TES_IN_FragPos[0], TES_IN_FragPos[1], TES_IN_FragPos[2]), 1.0);
    vec2 texcoord = interpolate_2d(TES_IN_TexCoord[0], TES_IN_TexCoord[1], TES_IN_TexCoord[2]);

    vs_out.texcoord = texcoord;

    vs_out.height = (texture(tes_heightmap, texcoord).r - 0.5) * 2;

    pos.y = vs_out.height * tes.height_scale + tes.height_bias;
    vs_out.fragpos = vec3(tes.model * pos);

    gl_Position = tes.projection * tes.view * tes.model * pos;

    gl_ClipDistance[0] = dot(tes.model * pos, tes.clip_plane);
}
"
node1.gs = ""
node1.fs = "
#version 330 core
out vec4 FragColor;

in VS_OUT {
    float height;
    vec3 fragpos;
    vec2 texcoord;
} fs_in;

uniform UBO_FS
{
    vec3 light_pos;
    vec2 inv_res;

    int sample_tile_type;

    vec4 layer_height_scale;
    vec4 layer_height_bias;
    vec4 layer_slope_scale;
    vec4 layer_slope_bias;    

    // brush
    vec2 mouse_pos;
    float draw_radius;    
} fs;

uniform sampler2D fs_heightmap;

uniform sampler2D splatmap0;
uniform sampler2D splatmap1;
uniform sampler2D splatmap2;
uniform sampler2D splatmap3;

const float UV_SCALE = 32.0;

float calc_height(vec2 texcoord)
{
    return texture(fs_heightmap, texcoord).r/* - 0.5*/;
}

vec3 ComputeNormalCentralDifference(vec2 position, float heightExaggeration)
{
    float leftHeight = calc_height(position - vec2(1.0, 0.0) * fs.inv_res) * heightExaggeration;
    float rightHeight = calc_height(position + vec2(1.0, 0.0) * fs.inv_res) * heightExaggeration;
    float bottomHeight = calc_height(position - vec2(0.0, 1.0) * fs.inv_res) * heightExaggeration;
    float topHeight = calc_height(position + vec2(0.0, 1.0) * fs.inv_res) * heightExaggeration;
    return normalize(vec3(leftHeight - rightHeight, 2.0, bottomHeight - topHeight));
}

vec4 hash4( vec2 p ) { return fract(sin(vec4( 1.0+dot(p,vec2(37.0,17.0)),
                                              2.0+dot(p,vec2(11.0,47.0)),
                                              3.0+dot(p,vec2(41.0,29.0)),
                                              4.0+dot(p,vec2(23.0,31.0))))*103.0); }

vec4 textureNoTile1( sampler2D samp, in vec2 uv )
{
    ivec2 iuv = ivec2( floor( uv ) );
     vec2 fuv = fract( uv );

    // generate per-tile transform
    vec4 ofa = hash4( iuv + ivec2(0,0) );
    vec4 ofb = hash4( iuv + ivec2(1,0) );
    vec4 ofc = hash4( iuv + ivec2(0,1) );
    vec4 ofd = hash4( iuv + ivec2(1,1) );

    vec2 ddx = dFdx( uv );
    vec2 ddy = dFdy( uv );

    // transform per-tile uvs
    ofa.zw = sign( ofa.zw-0.5 );
    ofb.zw = sign( ofb.zw-0.5 );
    ofc.zw = sign( ofc.zw-0.5 );
    ofd.zw = sign( ofd.zw-0.5 );

    // uv's, and derivatives (for correct mipmapping)
    vec2 uva = uv*ofa.zw + ofa.xy, ddxa = ddx*ofa.zw, ddya = ddy*ofa.zw;
    vec2 uvb = uv*ofb.zw + ofb.xy, ddxb = ddx*ofb.zw, ddyb = ddy*ofb.zw;
    vec2 uvc = uv*ofc.zw + ofc.xy, ddxc = ddx*ofc.zw, ddyc = ddy*ofc.zw;
    vec2 uvd = uv*ofd.zw + ofd.xy, ddxd = ddx*ofd.zw, ddyd = ddy*ofd.zw;

    // fetch and blend
    vec2 b = smoothstep( 0.25,0.75, fuv );

    return mix( mix( textureGrad( samp, uva, ddxa, ddya ),
                     textureGrad( samp, uvb, ddxb, ddyb ), b.x ),
                mix( textureGrad( samp, uvc, ddxc, ddyc ),
                     textureGrad( samp, uvd, ddxd, ddyd ), b.x), b.y );
}

vec4 textureNoTile2( sampler2D samp, in vec2 uv )
{
    vec2 p = floor( uv );
    vec2 f = fract( uv );

    // derivatives (for correct mipmapping)
    vec2 ddx = dFdx( uv );
    vec2 ddy = dFdy( uv );

    // voronoi contribution
    vec4 va = vec4( 0.0 );
    float wt = 0.0;
    for( int j=-1; j<=1; j++ )
    for( int i=-1; i<=1; i++ )
    {
        vec2 g = vec2( float(i), float(j) );
        vec4 o = hash4( p + g );
        vec2 r = g - f + o.xy;
        float d = dot(r,r);
        float w = exp(-5.0*d );
        vec4 c = textureGrad( samp, uv + o.zw, ddx, ddy );
        va += w*c;
        wt += w;
    }

    // normalization
    return va/wt;
}

vec4 textureSample(sampler2D samp, in vec2 uv)
{
    if (fs.sample_tile_type == 0) {
        return texture(samp, uv);
    } else if (fs.sample_tile_type == 1) {
        return textureNoTile1(samp, uv);
    } else if (fs.sample_tile_type == 2) {
        return textureNoTile2(samp, uv);
    } else {
        return texture(samp, uv);
    }
}

void main()
{
    vec3 pos = fs_in.fragpos;

    vec3 N = ComputeNormalCentralDifference(fs_in.texcoord, 500);

    vec3 light_dir = normalize(fs.light_pos - pos);
    float diff = max(dot(N, light_dir), 0.0);
    vec3 diffuse = diff * vec3(1.0, 1.0, 1.0);

    //vec4 real_height = fs_in.height * 0.5 + 0.5 + distortion * 0.03;
    vec4 real_height = vec4(fs_in.height * 0.5 + 0.5);
    vec4 lerp_weights = clamp(real_height * fs.layer_height_scale + fs.layer_height_bias, 0.0, 1.0);
    vec4 lerp_weights2 = clamp((/*distortion.w + */length(vec2(N.x, N.z))) * fs.layer_slope_scale + fs.layer_slope_bias, 0.0, 1.0);
    lerp_weights = lerp_weights * lerp_weights2;

    vec2 tex_coord = UV_SCALE * fs_in.texcoord;
    
    vec3 splat_col = vec3(0.0);
    splat_col = textureSample(splatmap0, tex_coord).rgb;
    splat_col = mix(splat_col, textureSample(splatmap1, tex_coord).rgb, lerp_weights.x);
    splat_col = mix(splat_col, textureSample(splatmap2, tex_coord).rgb, lerp_weights.y);
    splat_col = mix(splat_col, textureSample(splatmap3, tex_coord).rgb, lerp_weights.z);

    vec3 final = diffuse * splat_col;

    float dist = length(pos - vec3(fs.mouse_pos.x, pos.y, fs.mouse_pos.y));
    if (dist < fs.draw_radius) {
        float blend = pow(1.0 - dist / fs.draw_radius, 1.0);
        final.r += 0.6 * blend;
    }

    FragColor = vec4(final, 1.0);
}
"
node1.cs = ""
node1.render_gen()
node1.set_uniform("tcs.lod_level", [ 13 ])
node1.set_uniform("tes.height_scale", [ 0.1 ])
node1.set_uniform("tes.height_bias", [ 0.04 ])
node1.set_uniform("fs.light_pos", [ 0.79082131385803, 3.7241544723511, 2.3908212184906 ])
node1.set_uniform("fs.inv_res", [ 0.0009765625, 0.0009765625 ])
node1.set_uniform("fs.sample_tile_type", [ 0 ])

_editor.add_node(node1, 157.79557543759, -355.9795922234)

var node2 = ::rendergraph::nodes::clear::Clear()

node2.masks = [ "color", "depth" ]
node2.values = { "color" : [ 0, 0, 0, 255 ] }

_editor.add_node(node2, 335.02348918957, -188.12587085987)

var node3 = ::rendergraph::nodes::primitive_shape::PrimitiveShape()

node3.type = "grids"
node3.layout = [ "position", "texture" ]
node3.shape_params = { "grids_size" : 64 }

_editor.add_node(node3, 381.67421297039, -401.33131969581)

var node4 = ::blueprint::nodes::input::Input()

node4.var_name = "tes_heightmap"
node4.var_type = "texture"

_editor.add_node(node4, -251.06667315772, 79.375127070942)

var node5 = ::rendergraph::nodes::texture::Texture()
node5.query_param("unif_name").value = "u_tex"
node5.gamma_correction = false
node5.init_texture("assets/textures/terrain/dark_dirt.jpg")
node5.set_sampler("linear_repeat")

_editor.add_node(node5, -398.28562958109, -303.05954756096)

var node6 = ::rendergraph::nodes::texture::Texture()
node6.query_param("unif_name").value = "u_tex"
node6.gamma_correction = false
node6.init_texture("assets/textures/terrain/rock.jpg")
node6.set_sampler("linear_repeat")

_editor.add_node(node6, -164.60783767928, -303.48366069446)

var node7 = ::rendergraph::nodes::texture::Texture()
node7.query_param("unif_name").value = "u_tex"
node7.gamma_correction = false
node7.init_texture("assets/textures/terrain/grass.jpg")
node7.set_sampler("linear_repeat")

_editor.add_node(node7, -405.89994659973, -600.57493158753)

var node8 = ::rendergraph::nodes::texture::Texture()
node8.query_param("unif_name").value = "u_tex"
node8.gamma_correction = false
node8.init_texture("assets/textures/terrain/snow.png")
node8.set_sampler("linear_repeat")

_editor.add_node(node8, -163.47868212477, -600.57491890504)

var node9 = ::blueprint::nodes::custom::Custom()

node9.code = "
import \"maths.vector\" for Vector4
import \"math\" for Math

var blend_height     = [ 0.0, 0.52, 0.31, 0.8 ]
var blend_height_up  = [ 0, 1, 0, 1 ]
var blend_height_var = [ 0.0, 0.064, 0.32, 0.146 ]

var blend_slope     = [ 0, 44.1, 26.8, 44.4 ]
var blend_slope_up  = [ 0, 1, 0, 0 ]
var blend_slope_var = [ 0, 5.97, 34.36, 0.26 ]

fun get_scale(b)
{
    if (b) {
        return 0.5
    } else {
        return -0.5
    }
}

// height
var height2_lo = blend_height[1] - get_scale(blend_height_up[1]) * Math.max(0.001, blend_height_var[1])
var height2_hi = blend_height[1] + get_scale(blend_height_up[1]) * Math.max(0.001, blend_height_var[1])
var height3_lo = blend_height[2] - get_scale(blend_height_up[2]) * Math.max(0.001, blend_height_var[2])
var height3_hi = blend_height[2] + get_scale(blend_height_up[2]) * Math.max(0.001, blend_height_var[2])
var height4_lo = blend_height[3] - get_scale(blend_height_up[3]) * Math.max(0.001, blend_height_var[3])
var height4_hi = blend_height[3] + get_scale(blend_height_up[3]) * Math.max(0.001, blend_height_var[3])
var layer_height_scale = Vector4(1.0 / (height2_hi - height2_lo), 1.0 / (height3_hi - height3_lo), 1.0 / (height4_hi - height4_lo), 0.0)
var layer_height_bias = Vector4(-height2_lo / (height2_hi - height2_lo), -height3_lo / (height3_hi - height3_lo), -height4_lo / (height4_hi - height4_lo), 0.0)

// slope
var slope2_lo_tan = Math.tan(0.01745 * (blend_slope[1] - get_scale(blend_slope_up[1]) * Math.max(0.001, blend_slope_var[1])))
var slope2_hi_tan = Math.tan(0.01745 * (blend_slope[1] + get_scale(blend_slope_up[1]) * Math.max(0.001, blend_slope_var[1])))
var slope3_lo_tan = Math.tan(0.01745 * (blend_slope[2] - get_scale(blend_slope_up[2]) * Math.max(0.001, blend_slope_var[2])))
var slope3_hi_tan = Math.tan(0.01745 * (blend_slope[2] + get_scale(blend_slope_up[2]) * Math.max(0.001, blend_slope_var[2])))
var slope4_lo_tan = Math.tan(0.01745 * (blend_slope[3] - get_scale(blend_slope_up[3]) * Math.max(0.001, blend_slope_var[3])))
var slope4_hi_tan = Math.tan(0.01745 * (blend_slope[3] + get_scale(blend_slope_up[3]) * Math.max(0.001, blend_slope_var[3])))
var layer_slope_scale = Vector4(1.0 / (slope2_hi_tan - slope2_lo_tan), 1.0 / (slope3_hi_tan - slope3_lo_tan), 1.0 / (slope4_hi_tan - slope4_lo_tan), 0.0)
var layer_slope_bias = Vector4(-slope2_lo_tan / (slope2_hi_tan - slope2_lo_tan), -slope3_lo_tan / (slope3_hi_tan - slope3_lo_tan), -slope4_lo_tan / (slope4_hi_tan - slope4_lo_tan), 0.0)

_editor.script_stack.clear()
_editor.script_stack.add(layer_height_scale)
_editor.script_stack.add(layer_height_bias)
_editor.script_stack.add(layer_slope_scale)
_editor.script_stack.add(layer_slope_bias)
"
node9.init_ports(0, 4)
node9.use_cache = false

_editor.add_node(node9, -252.76502418668, -137.49332885429)

var node10 = ::blueprint::nodes::property::Property()

node10.var_name = "tes.clip_plane"
node10.var_type = "num4"

_editor.add_node(node10, -100.82996987323, 97.773203908102)

var node11 = ::blueprint::nodes::input::Input()

node11.var_name = "tcs.model"
node11.var_type = "mat4"

_editor.add_node(node11, -102.68643271646, 151.71171482009)

var node12 = ::blueprint::nodes::proxy::Proxy()

node12.real_name = "view_cam"
node12.init_real_node(::blueprint::nodes::camera3d::Camera3d())

_editor.add_node(node12, -305.91326836226, 217.13073328478)

var node13 = ::blueprint::nodes::perspective::Perspective()

node13.fovy = 45
node13.aspect = 0
node13.znear = 0.1
node13.zfar = 100

_editor.add_node(node13, -134.60698499512, 344.54891007019)

var node14 = ::blueprint::nodes::input::Input()

node14.var_name = "fs.mouse_pos"
node14.var_type = "num2"

_editor.add_node(node14, -247.05421839203, 31.474460990285)

var node15 = ::blueprint::nodes::input::Input()

node15.var_name = "fs.draw_radius"
node15.var_type = "num"

_editor.add_node(node15, -247.0542146495, -17.702509322296)

Blueprint.connect(node12, "zoom", node13, "fovy")
Blueprint.connect(node9, "next", node2, "prev")
Blueprint.connect(node13, "mat", node1, "tcs.projection")
Blueprint.connect(node12, "mat", node1, "tcs.view")
Blueprint.connect(node11, "var", node1, "tcs.model")
Blueprint.connect(node12, "pos", node1, "tcs.cam_pos")
Blueprint.connect(node13, "mat", node1, "tes.projection")
Blueprint.connect(node12, "mat", node1, "tes.view")
Blueprint.connect(node11, "var", node1, "tes.model")
Blueprint.connect(node10, "var", node1, "tes.clip_plane")
Blueprint.connect(node4, "var", node1, "tes_heightmap")
Blueprint.connect(node9, "out0", node1, "fs.layer_height_scale")
Blueprint.connect(node9, "out1", node1, "fs.layer_height_bias")
Blueprint.connect(node9, "out2", node1, "fs.layer_slope_scale")
Blueprint.connect(node9, "out3", node1, "fs.layer_slope_bias")
Blueprint.connect(node14, "var", node1, "fs.mouse_pos")
Blueprint.connect(node15, "var", node1, "fs.draw_radius")
Blueprint.connect(node4, "var", node1, "fs_heightmap")
Blueprint.connect(node5, "tex", node1, "splatmap0")
Blueprint.connect(node6, "tex", node1, "splatmap1")
Blueprint.connect(node7, "tex", node1, "splatmap2")
Blueprint.connect(node8, "tex", node1, "splatmap3")
Blueprint.connect(node2, "next", node0, "prev")
Blueprint.connect(node1, "out", node0, "shader")
Blueprint.connect(node3, "out", node0, "va")
