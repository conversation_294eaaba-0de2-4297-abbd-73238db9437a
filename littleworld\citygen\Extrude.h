#pragma once

#include <SM_Vector.h>
#include <vector>
#include <memory>

namespace gs { class Polygon2D; }

namespace citygen {

class Extrude
{
public:
    static std::vector<sm::vec3> ExtrudePolygon(const std::vector<sm::vec2>& polygon, float height);
    static std::vector<sm::vec3> ExtrudePolyline(const std::vector<sm::vec2>& polyline, float height);
    static std::vector<sm::vec3> Face(const std::vector<sm::vec2>& polygon, float height = 0.0f);
    static std::vector<sm::vec3> Face(std::shared_ptr<gs::Polygon2D> polygon, float height = 0.0f);

private:
    static void AddQuad(std::vector<sm::vec3>& vertices,
                       const sm::vec2& p1, const sm::vec2& p2, float height);
};

} // namespace citygen
