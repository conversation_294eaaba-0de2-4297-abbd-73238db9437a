C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\external\clipper\clipper\clipper.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\clipper.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\external\triangle\triangle.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\triangle.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Calc.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Calc.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_ConvexHull.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_ConvexHull.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_CosineSmooth.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_CosineSmooth.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\sm_c_calc.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\sm_c_calc.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\sm_c_matrix.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\sm_c_matrix.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\sm_c_vector.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\sm_c_vector.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_DouglasPeucker.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_DouglasPeucker.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Math.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Math.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Matrix2D.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Matrix2D.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Polygon.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Polygon.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Polyline.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Polyline.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_RayIntersect.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_RayIntersect.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Test.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Test.obj
C:\Users\<USER>\Desktop\workspace\git\tantien\thirdparty\sm\SM_Triangulation.cpp;C:\Users\<USER>\Desktop\workspace\git\tantien\platform\windows\projects\x64\Release\sm\SM_Triangulation.obj
