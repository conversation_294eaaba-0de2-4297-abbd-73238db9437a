<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: nv::cloth::MeshFlag Struct Reference</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="navpath"><b>nv</b>::<b>cloth</b>::<a class="el" href="structnv_1_1cloth_1_1_mesh_flag.html">MeshFlag</a>
  </div>
</div>
<div class="contents">
<h1>nv::cloth::MeshFlag Struct Reference</h1><!-- doxytag: class="nv::cloth::MeshFlag" -->Enum with flag values to be used in <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a>.  
<a href="#_details">More...</a>
<p>
<code>#include &lt;<a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a>&gt;</code>
<p>

<p>
<a href="structnv_1_1cloth_1_1_mesh_flag-members.html">List of all members.</a><table border="0" cellpadding="0" cellspacing="0">
<tr><td></td></tr>
<tr><td colspan="2"><br><h2>Public Types</h2></td></tr>
</table>
<hr><a name="_details"></a><h2>Detailed Description</h2>
Enum with flag values to be used in <a class="el" href="classnv_1_1cloth_1_1_cloth_mesh_desc.html" title="Descriptor class for a cloth mesh.">ClothMeshDesc</a>. <hr><h2>Member Enumeration Documentation</h2>
<a class="anchor" name="204e0a905a94be6c3f33d82941329489"></a><!-- doxytag: member="nv::cloth::MeshFlag::Enum" ref="204e0a905a94be6c3f33d82941329489" args="" -->
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">enum <a class="el" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489">nv::cloth::MeshFlag::Enum</a>          </td>
        </tr>
      </table>
</div>
<div class="memdoc">

<p>
<dl compact><dt><b>Enumerator: </b></dt><dd>
<table border="0" cellspacing="2" cellpadding="0">
<tr><td valign="top"><em><a class="anchor" name="204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93"></a><!-- doxytag: member="e16_BIT_INDICES" ref="204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93" args="" -->e16_BIT_INDICES</em>&nbsp;</td><td>
Denotes the use of 16-bit vertex indices. </td></tr>
</table>
</dl>

</div>
</div><p>
<hr>The documentation for this struct was generated from the following file:<ul>
<li><a class="el" href="_cloth_mesh_desc_8h-source.html">ClothMeshDesc.h</a></ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
