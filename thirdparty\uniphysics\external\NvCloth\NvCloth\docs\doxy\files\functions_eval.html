<html>
	<head>
		<title>NVIDIA(R) NvCloth API reference: Class Members - Enumerator</title>
		<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
		<LINK HREF="NVIDIA.css" REL="stylesheet" TYPE="text/css">
	</head>

	<body bgcolor="#FFFFFF">
		<div id="header">
			<hr class="first">
			<img alt="" src="logo.png">
			<br>
			<center>
				<a class="qindex" href="main.html">Main Page</a> &nbsp; 
				<!-- <a class="qindex" href="hierarchy.html">Class Hierarchy</a> &nbsp; //-->
				<a class="qindex" href="annotated.html">Class List</a> &nbsp; 
				<a class="qindex" href="functions.html">Class Members</a> &nbsp;  
			</center>
			<hr class="second">
		</div>
<!-- Generated by Doxygen 1.5.8 -->
<div class="navigation" id="top">
  <div class="tabs">
    <ul>
      <li><a href="main.html"><span>Main&nbsp;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="annotated.html"><span>Classes</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="annotated.html"><span>Class&nbsp;List</span></a></li>
      <li><a href="classes.html"><span>Class&nbsp;Index</span></a></li>
      <li><a href="hierarchy.html"><span>Class&nbsp;Hierarchy</span></a></li>
      <li class="current"><a href="functions.html"><span>Class&nbsp;Members</span></a></li>
    </ul>
  </div>
  <div class="tabs">
    <ul>
      <li><a href="functions.html"><span>All</span></a></li>
      <li><a href="functions_func.html"><span>Functions</span></a></li>
      <li><a href="functions_vars.html"><span>Variables</span></a></li>
      <li><a href="functions_enum.html"><span>Enumerations</span></a></li>
      <li class="current"><a href="functions_eval.html"><span>Enumerator</span></a></li>
      <li><a href="functions_rela.html"><span>Related&nbsp;Functions</span></a></li>
    </ul>
  </div>
</div>
<div class="contents">
&nbsp;
<p>
<ul>
<li>e16_BIT_INDICES
: <a class="el" href="structnv_1_1cloth_1_1_mesh_flag.html#204e0a905a94be6c3f33d82941329489ce9385a0c5594cbf4f7de7e76d993d93">nv::cloth::MeshFlag</a>
<li>eBENDING
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c85a210009d7fffa6fb7fbf246e40b1eb9">nv::cloth::ClothFabricPhaseType</a>
<li>eCOUNT
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c83033500239eabb666a723f55c257ad2f">nv::cloth::ClothFabricPhaseType</a>
<li>eHORIZONTAL
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c86b9950273c603473058bf8374ae22412">nv::cloth::ClothFabricPhaseType</a>
<li>eINVALID
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c847a36485416def698276d21717025a45">nv::cloth::ClothFabricPhaseType</a>
<li>eSHEARING
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c89e3f928ec6acb0a8ab211149afc9e24c">nv::cloth::ClothFabricPhaseType</a>
<li>eVERTICAL
: <a class="el" href="structnv_1_1cloth_1_1_cloth_fabric_phase_type.html#7ac4204396c4dc558681a39e1f4ad8c895d408850eff3c958150f13eea8728df">nv::cloth::ClothFabricPhaseType</a>
</ul>
</div>
<!-- start footer part -->
<div class="footer">
Copyright &copy; 2016-2017 NVIDIA Corporation, 2701 San Tomas Expressway, Santa Clara, CA 95050 U.S.A. All rights reserved. <a href="http://www.nvidia.com ">www.nvidia.com</a>
</div>
</body>
</html>
